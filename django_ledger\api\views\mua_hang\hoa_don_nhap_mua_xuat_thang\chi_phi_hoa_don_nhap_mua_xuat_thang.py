"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiPhiHoaDonNhapMuaXuatThang ViewSet implementation.
"""

from rest_framework import status  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401
from rest_framework.viewsets import ModelViewSet  # noqa: F401

from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiHoaDonNhapMuaXuatThangCreateUpdateSerializer,
    ChiPhiHoaDonNhapMuaXuatThangSerializer,
)
from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiHoaDonNhapMuaXuatThangModel,
)
from django_ledger.services.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiHoaDonNhapMuaXuatThangService,
)


class ChiPhiHoaDonNhapMuaXuatThangViewSet(ModelViewSet):
    """
    ViewSet for ChiPhiHoaDonNhapMuaXuatThangModel.
    Provides CRUD operations for invoice costs.
    """

    serializer_class = ChiPhiHoaDonNhapMuaXuatThangSerializer
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):  # noqa: C901
        """Initialize the viewset with the service."""
        super().__init__(**kwargs)
        self.service = ChiPhiHoaDonNhapMuaXuatThangService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            The queryset filtered by invoice UUID if provided.
        """
        hoa_don_uuid = self.kwargs.get('hoa_don_uuid')
        if hoa_don_uuid:
            return self.service.get_by_invoice(hoa_don_uuid)
        return ChiPhiHoaDonNhapMuaXuatThangModel.objects.none()

    def get_serializer_class(self):  # noqa: C901
        """
        Get the appropriate serializer class based on the action.

        Returns
        -------
        Serializer
            The serializer class for the current action.
        """
        if self.action in ['create', 'update', 'partial_update']:
            return ChiPhiHoaDonNhapMuaXuatThangCreateUpdateSerializer
        return ChiPhiHoaDonNhapMuaXuatThangSerializer

    def perform_create(self, serializer):  # noqa: C901
        """
        Perform the creation of a new ChiPhiHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        serializer : ChiPhiHoaDonNhapMuaXuatThangCreateUpdateSerializer
            The serializer with validated data.
        """
        hoa_don_uuid = self.kwargs.get('hoa_don_uuid')
        if hoa_don_uuid:
            from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (
                HoaDonNhapMuaXuatThangModel,
            )

            hoa_don = HoaDonNhapMuaXuatThangModel.objects.get(uuid=hoa_don_uuid)
            serializer.save(hoa_don=hoa_don)
        else:
            serializer.save()

    @action(detail=False, methods=['get'])
    def totals(self, request, hoa_don_uuid=None):  # noqa: C901
        """
        Get total cost amounts for all costs in the invoice.

        Parameters
        ----------
        request : Request
            The HTTP request.
        hoa_don_uuid : UUID
            The invoice UUID.

        Returns
        -------
        Response
            The response with total cost amounts.
        """
        try:
            if not hoa_don_uuid:
                return Response(
                    {'error': 'hoa_don_uuid is required'},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            totals = self.service.get_total_by_invoice(hoa_don_uuid)
            return Response(totals, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
