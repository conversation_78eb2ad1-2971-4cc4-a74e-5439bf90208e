"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiPhiChiTietHoaDonNhapMuaXuatThang ViewSet implementation.
"""

from rest_framework import status  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401
from rest_framework.viewsets import ModelViewSet  # noqa: F401

from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangCreateUpdateSerializer,
    ChiPhiChiTietHoaDonNhapMuaXuatThangSerializer,
)
from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangModel,
)
from django_ledger.services.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangService,
)


class ChiPhiChiTietHoaDonNhapMuaXuatThangViewSet(ModelViewSet):
    """
    ViewSet for ChiPhiChiTietHoaDonNhapMuaXuatThangModel.
    Provides CRUD operations for detailed invoice costs.
    """

    serializer_class = ChiPhiChiTietHoaDonNhapMuaXuatThangSerializer
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):  # noqa: C901
        """Initialize the viewset with the service."""
        super().__init__(**kwargs)
        self.service = ChiPhiChiTietHoaDonNhapMuaXuatThangService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            The queryset filtered by invoice UUID if provided.
        """
        hoa_don_uuid = self.kwargs.get('hoa_don_uuid')
        if hoa_don_uuid:
            return self.service.get_by_invoice(hoa_don_uuid)
        return ChiPhiChiTietHoaDonNhapMuaXuatThangModel.objects.none()

    def get_serializer_class(self):  # noqa: C901
        """
        Get the appropriate serializer class based on the action.

        Returns
        -------
        Serializer
            The serializer class for the current action.
        """
        if self.action in ['create', 'update', 'partial_update']:
            return ChiPhiChiTietHoaDonNhapMuaXuatThangCreateUpdateSerializer
        return ChiPhiChiTietHoaDonNhapMuaXuatThangSerializer

    def perform_create(self, serializer):  # noqa: C901
        """
        Perform the creation of a new ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        serializer : ChiPhiChiTietHoaDonNhapMuaXuatThangCreateUpdateSerializer
            The serializer with validated data.
        """
        hoa_don_uuid = self.kwargs.get('hoa_don_uuid')
        if hoa_don_uuid:
            from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (
                HoaDonNhapMuaXuatThangModel,
            )

            hoa_don = HoaDonNhapMuaXuatThangModel.objects.get(uuid=hoa_don_uuid)
            serializer.save(hoa_don=hoa_don)
        else:
            serializer.save()

    @action(detail=False, methods=['get'])
    def by_material(self, request, hoa_don_uuid=None):  # noqa: C901
        """
        Get detailed costs filtered by material.

        Parameters
        ----------
        request : Request
            The HTTP request.
        hoa_don_uuid : UUID
            The invoice UUID.

        Returns
        -------
        Response
            The response with filtered detailed costs.
        """
        material_uuid = request.query_params.get('material_uuid')
        if not material_uuid:
            return Response(
                {'error': 'material_uuid parameter is required'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            costs = self.service.filter_by_material(material_uuid, hoa_don_uuid)
            page = self.paginate_queryset(costs)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(costs, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def totals(self, request, hoa_don_uuid=None):  # noqa: C901
        """
        Get total detailed cost amounts for all detailed costs in the invoice.

        Parameters
        ----------
        request : Request
            The HTTP request.
        hoa_don_uuid : UUID
            The invoice UUID.

        Returns
        -------
        Response
            The response with total detailed cost amounts.
        """
        try:
            if not hoa_don_uuid:
                return Response(
                    {'error': 'hoa_don_uuid is required'},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            totals = self.service.get_total_by_invoice(hoa_don_uuid)
            return Response(totals, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
