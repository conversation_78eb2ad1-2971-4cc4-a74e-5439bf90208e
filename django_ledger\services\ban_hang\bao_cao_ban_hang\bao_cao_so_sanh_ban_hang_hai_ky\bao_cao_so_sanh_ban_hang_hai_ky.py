"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for Bao Cao So Sanh Ban <PERSON> (Sales Comparison Report Between Two Periods).
"""

import logging
from typing import Any, Dict, List

from django_ledger.models import (
    ChiTietHoaDonBanHangModel,
    ChiTietHoaDonModel,
    EntityModel,
)

from .constants import get_detail_by_config
from .filters import SalesComparisonFilters
from .utils import SalesComparisonUtils

# Q imported in filters module where needed


logger = logging.getLogger(__name__)


class BaoCaoSoSanhBanHangHaiKyService:
    """
    Service for generating sales comparison reports between two periods.

    Supports dynamic grouping by various entity types (customer, product, warehouse, etc.)
    with comprehensive UUID-based filtering capabilities.
    """

    def __init__(self):
        """Initialize the service."""
        pass

    def generate_report(
        self,
        entity_slug: str = None,
        entity: EntityModel = None,
        filters: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """
        Generate sales comparison report between two periods.

        Parameters
        ----------
        entity : EntityModel
            Entity to generate report for
        filters : Dict[str, Any]
            Filter parameters including periods and detail_by

        Returns
        -------
        List[Dict[str, Any]]
            Report data with comparison calculations

        Raises
        ------
        ValueError
            If invalid detail_by value provided
        """
        try:
            # Handle entity parameter
            if entity_slug and not entity:
                entity = EntityModel.objects.get(slug=entity_slug)
            elif not entity:
                raise ValueError("Either entity_slug or entity must be provided")

            # Handle filters parameter
            if not filters:
                filters = {}

            # Get configuration for detail_by
            detail_by = filters.get('detail_by', '100')
            config = get_detail_by_config(detail_by)
            if not config:
                raise ValueError(f"Invalid detail_by value: {detail_by}")

            logger.info(
                f"Generating sales comparison report for entity {entity.slug} "
                f"with detail_by={detail_by}"
            )

            # Get combined sales data
            combined_data = self._get_combined_sales_data(entity, filters, config)

            # Process comparison calculations
            result = SalesComparisonUtils.process_comparison_calculations(
                combined_data, filters.get('period_1', {}), filters.get('period_2', {})
            )

            # Apply group_by processing if specified
            group_by = filters.get('group_by')
            if group_by:
                logger.info(f"Applying group_by={group_by} to {len(result)} items")

                # Import here to avoid circular imports
                from .constants import is_cross_category_grouping

                # Check if this is cross-category grouping
                if is_cross_category_grouping(detail_by, group_by):
                    logger.info(
                        "Cross-category grouping detected, using generalized logic"
                    )
                    result = SalesComparisonUtils.apply_cross_category_grouping(
                        entity, filters, config, detail_by, group_by
                    )
                else:
                    result = SalesComparisonUtils.apply_group_by(
                        result, detail_by, group_by
                    )
                logger.info(f"After grouping: {len(result)} items")

            logger.info(f"Generated report with {len(result)} items")
            return result

        except Exception as e:
            logger.error(f"Error generating sales comparison report: {str(e)}")
            raise

    def _apply_cross_category_grouping(
        self,
        entity,
        filters: Dict[str, Any],
        config: Dict[str, Any],
        detail_by: str,
        group_by: str,
    ) -> List[Dict[str, Any]]:
        """
        Apply cross-category grouping logic.

        For detail_by="100" (customers) + group_by="200" (materials):
        - Query invoice details to get customer-material relationships
        - Group by materials first
        - Within each material group, aggregate customers

        Parameters
        ----------
        entity : EntityModel
            The entity
        filters : Dict[str, Any]
            Filter parameters
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG
        detail_by : str
            The detail_by code
        group_by : str
            The group_by code

        Returns
        -------
        List[Dict[str, Any]]
            Cross-category grouped results
        """
        logger.info(
            f"Applying cross-category grouping: detail_by={detail_by}, group_by={group_by}"
        )

        combined_data = self._get_combined_sales_data(entity, filters, config)
        result = SalesComparisonUtils.process_comparison_calculations(
            combined_data, filters.get('period_1', {}), filters.get('period_2', {})
        )
        return SalesComparisonUtils.apply_group_by(result, detail_by, group_by)

    def _get_combined_sales_data(
        self, entity: EntityModel, filters: Dict[str, Any], config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Get combined sales data from both HoaDonBanHang and HoaDonDichVu.

        Parameters
        ----------
        entity : EntityModel
            Entity to get data for
        filters : Dict[str, Any]
            Filter parameters
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG

        Returns
        -------
        List[Dict[str, Any]]
            Combined sales data
        """
        # Get HoaDonBanHang data
        hdbh_data = self._get_hoa_don_ban_hang_data(entity, filters, config)

        # Get HoaDonDichVu data
        hddv_data = self._get_hoa_don_dich_vu_data(entity, filters, config)

        # Combine and aggregate data
        combined_data = SalesComparisonUtils.combine_and_aggregate_data(
            hdbh_data, hddv_data, config
        )

        return combined_data

    def _get_hoa_don_ban_hang_data(
        self, entity: EntityModel, filters: Dict[str, Any], config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Get data from HoaDonBanHang and ChiTietHoaDonBanHang.

        Parameters
        ----------
        entity : EntityModel
            Entity to get data for
        filters : Dict[str, Any]
            Filter parameters
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG

        Returns
        -------
        List[Dict[str, Any]]
            HoaDonBanHang aggregated data
        """
        # Check if join_field exists in model
        if not SalesComparisonFilters.check_field_existence(
            config, ChiTietHoaDonBanHangModel
        ):
            return []

        # Build base queryset
        queryset = ChiTietHoaDonBanHangModel.objects.select_related(
            'hoa_don_ban_hang'
        ).filter(
            hoa_don_ban_hang__entity_model=entity,
            hoa_don_ban_hang__deleted_at__isnull=True,
            deleted_at__isnull=True,
        )

        # Add select_related for join field
        select_related_fields = SalesComparisonFilters.get_select_related_fields(
            config, 'hoa_don_ban_hang'
        )
        if select_related_fields:
            queryset = queryset.select_related(*select_related_fields)

        # Apply filters
        queryset = SalesComparisonFilters.apply_filters(
            queryset, filters, 'hoa_don_ban_hang'
        )

        # Apply date range filters for both periods
        queryset = SalesComparisonFilters.apply_date_filters(
            queryset, filters, 'hoa_don_ban_hang'
        )

        # Build aggregation using utils
        aggregated_data = SalesComparisonUtils.build_aggregation(
            queryset, config, 'hoa_don_ban_hang'
        )

        return list(aggregated_data)

    def _get_hoa_don_dich_vu_data(
        self, entity: EntityModel, filters: Dict[str, Any], config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Get data from HoaDonDichVu and ChiTietHoaDon.

        Parameters
        ----------
        entity : EntityModel
            Entity to get data for
        filters : Dict[str, Any]
            Filter parameters
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG

        Returns
        -------
        List[Dict[str, Any]]
            HoaDonDichVu aggregated data
        """
        # Skip HoaDonDichVu for VatTu queries since service invoices don't have VatTu
        if config['join_field'] == 'ma_vt':
            return []

        # Check if join_field exists in model
        if not SalesComparisonFilters.check_field_existence(config, ChiTietHoaDonModel):
            return []

        # Build base queryset
        queryset = ChiTietHoaDonModel.objects.select_related('hoa_don').filter(
            hoa_don__entity_model=entity,
            hoa_don__deleted_at__isnull=True,
            deleted_at__isnull=True,
        )

        # Add select_related for join field
        select_related_fields = SalesComparisonFilters.get_select_related_fields(
            config, 'hoa_don'
        )
        if select_related_fields:
            queryset = queryset.select_related(*select_related_fields)

        # Apply filters
        queryset = SalesComparisonFilters.apply_filters(queryset, filters, 'hoa_don')

        # Apply date range filters for both periods
        queryset = SalesComparisonFilters.apply_date_filters(
            queryset, filters, 'hoa_don'
        )

        # Build aggregation using utils
        aggregated_data = SalesComparisonUtils.build_aggregation(
            queryset, config, 'hoa_don'
        )

        return list(aggregated_data)
