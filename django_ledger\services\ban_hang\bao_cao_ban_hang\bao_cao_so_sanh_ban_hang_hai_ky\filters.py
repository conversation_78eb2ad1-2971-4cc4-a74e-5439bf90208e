"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Filter utilities for Sales Comparison Report Between Two Periods.
"""

from typing import Any, Dict

from django.db.models import Q, QuerySet

# Models imported in methods where needed to avoid circular imports


class SalesComparisonFilters:
    """
    Filter utilities for sales comparison report.

    Handles all UUID-based filtering logic for Stage 1 filtering.
    """

    @staticmethod
    def apply_filters(
        queryset: QuerySet, filters: Dict[str, Any], hoa_don_prefix: str
    ) -> QuerySet:
        """
        Apply all filter conditions to queryset.

        Parameters
        ----------
        queryset : QuerySet
            Base queryset to filter
        filters : Dict[str, Any]
            Filter parameters from request
        hoa_don_prefix : str
            Prefix for hoa don fields ('hoa_don_ban_hang' or 'hoa_don')

        Returns
        -------
        QuerySet
            Filtered queryset
        """
        # Apply document type filter
        queryset = SalesComparisonFilters._apply_document_type_filter(
            queryset, filters, hoa_don_prefix
        )

        # Apply customer UUID filters
        queryset = SalesComparisonFilters._apply_customer_filters(
            queryset, filters, hoa_don_prefix
        )

        # Apply product UUID filters
        queryset = SalesComparisonFilters._apply_product_filters(
            queryset, filters, hoa_don_prefix
        )

        # Apply entity UUID filters
        queryset = SalesComparisonFilters._apply_entity_filters(
            queryset, filters, hoa_don_prefix
        )

        return queryset

    @staticmethod
    def _apply_document_type_filter(
        queryset: QuerySet, filters: Dict[str, Any], hoa_don_prefix: str
    ) -> QuerySet:
        """Apply document type filter (nh_ct = "BH1,BH2,BH3")."""
        nh_ct = filters.get('nh_ct', 'BH1,BH2,BH3')
        if nh_ct:
            doc_types = [ct.strip() for ct in nh_ct.split(',')]
            # Include records with null ma_ct or matching ma_ct
            queryset = queryset.filter(
                Q(**{f"{hoa_don_prefix}__chung_tu__ma_ct__in": doc_types})
                | Q(**{f"{hoa_don_prefix}__chung_tu__ma_ct__isnull": True})
            )
        return queryset

    @staticmethod
    def _apply_customer_filters(
        queryset: QuerySet, filters: Dict[str, Any], hoa_don_prefix: str
    ) -> QuerySet:
        """Apply customer UUID filters."""
        if filters.get('ma_kh'):
            # ma_kh is UUID of CustomerModel
            queryset = queryset.filter(**{f"{hoa_don_prefix}__ma_kh": filters['ma_kh']})

        # Complex customer filters: nh_kh1-3 + rg_code
        # These require sub-query to find matching customers first
        customer_group_filters = {}
        if filters.get('nh_kh1'):
            customer_group_filters['customer_group1'] = filters['nh_kh1']
        if filters.get('nh_kh2'):
            customer_group_filters['customer_group2'] = filters['nh_kh2']
        if filters.get('nh_kh3'):
            customer_group_filters['customer_group3'] = filters['nh_kh3']
        if filters.get('rg_code'):
            customer_group_filters['region'] = filters['rg_code']

        if customer_group_filters:
            # Import here to avoid circular imports
            from django_ledger.models import CustomerModel

            # Find customers matching the group/region criteria
            matching_customers = CustomerModel.objects.filter(
                **customer_group_filters
            ).values_list('uuid', flat=True)

            # Filter invoices by these customers
            queryset = queryset.filter(
                **{f"{hoa_don_prefix}__ma_kh__in": matching_customers}
            )

        return queryset

    @staticmethod
    def _apply_product_filters(
        queryset: QuerySet, filters: Dict[str, Any], hoa_don_prefix: str
    ) -> QuerySet:
        """Apply product UUID filters."""
        if filters.get('ma_vt'):
            # ma_vt is UUID of VatTuModel
            if hoa_don_prefix == 'hoa_don_ban_hang':
                queryset = queryset.filter(ma_vt=filters['ma_vt'])
            else:
                # For HoaDonDichVu, ma_vt maps to ma_dv (DichVuModel)
                queryset = queryset.filter(ma_dv=filters['ma_vt'])

        # Complex material filters: nh_vt1-3 + ma_lvt + ma_kho
        # These require sub-query to find matching materials first
        material_group_filters = {}
        if filters.get('ma_lvt'):
            material_group_filters['ma_lvt'] = filters['ma_lvt']
        if filters.get('nh_vt1'):
            material_group_filters['nh_vt1'] = filters['nh_vt1']
        if filters.get('nh_vt2'):
            material_group_filters['nh_vt2'] = filters['nh_vt2']
        if filters.get('nh_vt3'):
            material_group_filters['nh_vt3'] = filters['nh_vt3']
        # Note: ma_kho is handled separately as it's at detail level, not material level

        if material_group_filters:
            # Import here to avoid circular imports
            from django_ledger.models import VatTuModel

            # Find materials matching the group/type criteria
            matching_materials = VatTuModel.objects.filter(
                **material_group_filters
            ).values_list('uuid', flat=True)

            # Filter invoice details by these materials
            if hoa_don_prefix == 'hoa_don_ban_hang':
                queryset = queryset.filter(ma_vt__in=matching_materials)
            else:
                # For HoaDonDichVu, materials are referenced as ma_dv
                queryset = queryset.filter(ma_dv__in=matching_materials)

        return queryset

    @staticmethod
    def _apply_entity_filters(
        queryset: QuerySet, filters: Dict[str, Any], hoa_don_prefix: str = None
    ) -> QuerySet:
        """Apply other entity UUID filters."""
        if filters.get('ma_kho'):
            # ma_kho is UUID of KhoHangModel - only apply to sales invoice model
            # Service invoice model doesn't have ma_kho field
            if hoa_don_prefix == 'hoa_don_ban_hang':
                queryset = queryset.filter(ma_kho=filters['ma_kho'])

        if filters.get('ma_bp'):
            # ma_bp is UUID of BoPhanModel
            queryset = queryset.filter(ma_bp=filters['ma_bp'])

        if filters.get('ma_vv'):
            # ma_vv is UUID of VuViecModel
            queryset = queryset.filter(ma_vv=filters['ma_vv'])

        if filters.get('ma_hd'):
            # ma_hd is UUID of ContractModel
            queryset = queryset.filter(ma_hd=filters['ma_hd'])

        if filters.get('ma_ku'):
            # ma_ku is UUID of KheUocModel
            queryset = queryset.filter(ma_ku=filters['ma_ku'])

        if filters.get('ma_phi'):
            # ma_phi is UUID of PhiModel
            queryset = queryset.filter(ma_phi=filters['ma_phi'])

        if filters.get('ma_unit'):
            # ma_unit is UUID of EntityUnitModel - filter at invoice level
            queryset = queryset.filter(
                **{f"{hoa_don_prefix}__unit_id": filters['ma_unit']}
            )

        if filters.get('ma_nvbh'):
            # ma_nvbh is UUID of NhanVienModel (sales staff) - filter at invoice level
            queryset = queryset.filter(
                **{f"{hoa_don_prefix}__ma_nvbh": filters['ma_nvbh']}
            )

        if filters.get('ma_dtt'):
            # ma_dtt is UUID of DoiTuongThanhToanModel
            queryset = queryset.filter(ma_dtt=filters['ma_dtt'])

        if filters.get('ma_sp'):
            # ma_sp is UUID of SanPhamModel
            queryset = queryset.filter(ma_sp=filters['ma_sp'])

        if filters.get('ma_lsx'):
            # ma_lsx is UUID of LenhSanXuatModel
            queryset = queryset.filter(ma_lsx=filters['ma_lsx'])

        if filters.get('ma_cp0'):
            # ma_cp0 is UUID of ChiPhiModel
            queryset = queryset.filter(ma_cp0=filters['ma_cp0'])

        # Account filters (tk_vt, tk_dt, tk_gv) - SKIPPED for future implementation
        # Batch and location filters (ma_lo, ma_vi_tri) - SKIPPED for future implementation
        # Document number filters (so_ct1, so_ct2) - SKIPPED for future implementation
        # Description filter (dien_giai) - SKIPPED for future implementation

        return queryset

    @staticmethod
    def check_field_existence(config: Dict[str, Any], model_class) -> bool:
        """
        Check if join_field exists in model or related models.

        Parameters
        ----------
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG
        model_class : Model
            Model class to check

        Returns
        -------
        bool
            True if field exists, False otherwise
        """
        join_field = config['join_field']

        # Fields that exist directly in model
        model_fields = [f.name for f in model_class._meta.fields]

        # Fields that exist in related models (accessed via parent__)
        parent_fields = ['ma_kh']  # Customer is at parent level

        return join_field in model_fields or join_field in parent_fields

    @staticmethod
    def get_select_related_fields(config: Dict[str, Any], hoa_don_prefix: str) -> list:
        """
        Get select_related fields based on join_field.

        Parameters
        ----------
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG
        hoa_don_prefix : str
            Prefix for hoa don fields

        Returns
        -------
        list
            List of field names for select_related
        """
        join_field = config['join_field']

        # Base select_related fields
        fields = [f'{hoa_don_prefix}__chung_tu_item']

        # Add join field specific select_related
        if join_field == 'ma_vt':
            if hoa_don_prefix == 'hoa_don':
                # For service invoices, use ma_dv instead of ma_vt
                fields.append('ma_dv')
            else:
                fields.append('ma_vt')
        elif join_field == 'ma_kh':
            fields.append(f'{hoa_don_prefix}__ma_kh')
        elif join_field in [
            'ma_kho',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_ku',
            'ma_phi',
            'ma_unit',
            'ma_nvbh',
            'ma_dtt',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
        ]:
            fields.append(join_field)

        return fields

    @staticmethod
    def apply_date_filters(queryset, filters: Dict[str, Any], hoa_don_prefix: str):
        """
        Apply date range filters for both periods.

        Parameters
        ----------
        queryset : QuerySet
            Base queryset
        filters : Dict[str, Any]
            Filter parameters with period_1 and period_2
        hoa_don_prefix : str
            Prefix for hoa don fields

        Returns
        -------
        QuerySet
            Filtered queryset
        """
        period_1 = filters.get('period_1', {})
        period_2 = filters.get('period_2', {})

        date_filter = Q()
        if period_1.get('start_date') and period_1.get('end_date'):
            date_filter |= Q(
                **{
                    f"{hoa_don_prefix}__chung_tu_item__ngay_ct__gte": period_1[
                        'start_date'
                    ],
                    f"{hoa_don_prefix}__chung_tu_item__ngay_ct__lte": period_1[
                        'end_date'
                    ],
                }
            )
        if period_2.get('start_date') and period_2.get('end_date'):
            date_filter |= Q(
                **{
                    f"{hoa_don_prefix}__chung_tu_item__ngay_ct__gte": period_2[
                        'start_date'
                    ],
                    f"{hoa_don_prefix}__chung_tu_item__ngay_ct__lte": period_2[
                        'end_date'
                    ],
                }
            )

        if date_filter:
            queryset = queryset.filter(date_filter)

        return queryset

    @staticmethod
    def query_by_group_by_entity(
        entity,
        period: Dict[str, Any],
        start_key: str,
        end_key: str,
        group_by: str,
        filters: Dict[str, Any],
    ):
        """
        STEP 1: Từ group_by → Kiểm tra xem hóa đơn/chi tiết có match được query đó không

        Query invoice details based on group_by entity type to find matching records.

        Parameters
        ----------
        entity : EntityModel
            The entity
        period : Dict[str, Any]
            Period configuration
        start_key : str
            Start date key
        end_key : str
            End date key
        group_by : str
            Group by code (e.g., "200" for customers, "300" for materials)
        filters : Dict[str, Any]
            Additional filters

        Returns
        -------
        QuerySet
            Invoice details that match group_by criteria
        """
        from datetime import datetime

        from django.db.models import Q

        from django_ledger.models import ChiTietHoaDonBanHangModel

        # Build base query
        base_query = Q(hoa_don_ban_hang__entity_model=entity)

        # Add period filter
        if period.get(start_key) and period.get(end_key):
            start_date = datetime.strptime(period[start_key], '%Y%m%d').date()
            end_date = datetime.strptime(period[end_key], '%Y%m%d').date()
            base_query &= Q(
                hoa_don_ban_hang__ngay_px__gte=start_date,
                hoa_don_ban_hang__ngay_px__lte=end_date,
            )

        # Apply group_by specific filters
        base_query = SalesComparisonFilters._apply_group_by_filters(
            base_query, group_by, filters
        )

        # Apply other common filters
        base_query = SalesComparisonFilters._apply_common_filters(base_query, filters)

        return ChiTietHoaDonBanHangModel.objects.filter(base_query).select_related(
            'hoa_don_ban_hang',
            'hoa_don_ban_hang__ma_kh',
            'hoa_don_ban_hang__unit_id',
            'ma_vt',
            'ma_bp',
            'ma_vv',
        )

    @staticmethod
    def _apply_group_by_filters(
        base_query: Q, group_by: str, filters: Dict[str, Any]
    ) -> Q:
        """
        Apply group_by specific filters to base query.

        Query levels:
        - Invoice level: Customers (200), Units (700), Sales Staff (910)
        - Detail level: Materials (300), Departments (810), Tasks (820),
                       Contracts (830), Agreements (840), Fees (850), Products (860)

        Parameters
        ----------
        base_query : Q
            Base query object
        group_by : str
            Group by code
        filters : Dict[str, Any]
            Filter parameters

        Returns
        -------
        Q
            Updated query with group_by filters
        """
        # INVOICE LEVEL ENTITIES
        if group_by == "200":  # Customers (Invoice level)
            # Filter by customer-related criteria
            nh_kh1 = filters.get('nh_kh1')
            if nh_kh1:
                base_query &= Q(hoa_don_ban_hang__ma_kh__customer_group1__uuid=nh_kh1)
            nh_kh2 = filters.get('nh_kh2')
            if nh_kh2:
                base_query &= Q(hoa_don_ban_hang__ma_kh__customer_group2__uuid=nh_kh2)
            nh_kh3 = filters.get('nh_kh3')
            if nh_kh3:
                base_query &= Q(hoa_don_ban_hang__ma_kh__customer_group3__uuid=nh_kh3)
            ma_kh = filters.get('ma_kh')
            if ma_kh:
                base_query &= Q(hoa_don_ban_hang__ma_kh__uuid=ma_kh)

        elif group_by == "700":  # Units (Invoice level)
            # Filter by unit-related criteria
            ma_unit = filters.get('ma_unit')
            if ma_unit:
                base_query &= Q(hoa_don_ban_hang__unit_id__uuid=ma_unit)

        elif group_by == "910":  # Sales Staff (Invoice level)
            # Filter by sales staff-related criteria
            ma_nvbh = filters.get('ma_nvbh')
            if ma_nvbh:
                base_query &= Q(hoa_don_ban_hang__ma_nvbh__uuid=ma_nvbh)

        # DETAIL LEVEL ENTITIES
        elif group_by == "300":  # Materials (Detail level)
            # Filter by material-related criteria
            ma_vt = filters.get('ma_vt')
            if ma_vt:
                base_query &= Q(ma_vt__uuid=ma_vt)
            nh_vt1 = filters.get('nh_vt1')
            if nh_vt1:
                base_query &= Q(ma_vt__nh_vt1__uuid=nh_vt1)
            nh_vt2 = filters.get('nh_vt2')
            if nh_vt2:
                base_query &= Q(ma_vt__nh_vt2__uuid=nh_vt2)
            nh_vt3 = filters.get('nh_vt3')
            if nh_vt3:
                base_query &= Q(ma_vt__nh_vt3__uuid=nh_vt3)

        elif group_by == "810":  # Departments (Detail level)
            # Filter by department-related criteria
            ma_bp = filters.get('ma_bp')
            if ma_bp:
                base_query &= Q(ma_bp__uuid=ma_bp)

        elif group_by == "820":  # Tasks (Detail level)
            # Filter by task-related criteria
            ma_vv = filters.get('ma_vv')
            if ma_vv:
                base_query &= Q(ma_vv__uuid=ma_vv)

        elif group_by == "830":  # Contracts (Detail level)
            # Filter by contract-related criteria
            ma_hd = filters.get('ma_hd')
            if ma_hd:
                base_query &= Q(ma_hd__uuid=ma_hd)

        elif group_by == "840":  # Agreements (Detail level)
            # Filter by agreement-related criteria
            ma_ku = filters.get('ma_ku')
            if ma_ku:
                base_query &= Q(ma_ku__uuid=ma_ku)

        elif group_by == "850":  # Fees (Detail level)
            # Filter by fee-related criteria
            ma_phi = filters.get('ma_phi')
            if ma_phi:
                base_query &= Q(ma_phi__uuid=ma_phi)

        elif group_by == "860":  # Products (Detail level)
            # Filter by product-related criteria
            ma_sp = filters.get('ma_sp')
            if ma_sp:
                base_query &= Q(ma_sp__uuid=ma_sp)

        return base_query

    @staticmethod
    def _apply_common_filters(base_query: Q, filters: Dict[str, Any]) -> Q:
        """
        Apply common filters that are used across all group_by types.

        Parameters
        ----------
        base_query : Q
            Base query object
        filters : Dict[str, Any]
            Filter parameters

        Returns
        -------
        Q
            Updated query with common filters
        """
        # Apply warehouse filter
        ma_kho = filters.get('ma_kho')
        if ma_kho:
            base_query &= Q(ma_kho__uuid=ma_kho)

        return base_query

    @staticmethod
    def query_by_group_by_entity(
        entity,
        period: Dict[str, Any],
        start_key: str,
        end_key: str,
        group_by: str,
        filters: Dict[str, Any],
    ):
        """
        STEP 1: Từ group_by → Kiểm tra xem hóa đơn/chi tiết có match được query đó không

        Query invoice details based on group_by entity type to find matching records.

        Parameters
        ----------
        entity : EntityModel
            The entity
        period : Dict[str, Any]
            Period configuration
        start_key : str
            Start date key
        end_key : str
            End date key
        group_by : str
            Group by code (e.g., "100" for customers, "200" for materials)
        filters : Dict[str, Any]
            Additional filters

        Returns
        -------
        QuerySet
            Invoice details that match group_by criteria
        """
        from datetime import datetime

        from django.db.models import Q

        from django_ledger.models import ChiTietHoaDonBanHangModel

        # Build base query
        base_query = Q(hoa_don_ban_hang__entity_model=entity)

        # Add period filter
        if period.get(start_key) and period.get(end_key):
            start_date = datetime.strptime(period[start_key], '%Y%m%d').date()
            end_date = datetime.strptime(period[end_key], '%Y%m%d').date()
            base_query &= Q(
                hoa_don_ban_hang__ngay_px__gte=start_date,
                hoa_don_ban_hang__ngay_px__lte=end_date,
            )

        # Apply group_by specific filters
        base_query = SalesComparisonFilters._apply_group_by_filters(
            base_query, group_by, filters
        )

        # Apply other common filters
        ma_kho = filters.get('ma_kho')
        if ma_kho:
            base_query &= Q(ma_kho__uuid=ma_kho)

        return ChiTietHoaDonBanHangModel.objects.filter(base_query).select_related(
            'hoa_don_ban_hang',
            'hoa_don_ban_hang__ma_kh',
            'hoa_don_ban_hang__unit_id',
            'ma_vt',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'hoa_don_ban_hang__ma_nvbh',
        )

    @staticmethod
    def _apply_group_by_filters(base_query, group_by: str, filters: Dict[str, Any]):
        """
        Apply group_by specific filters to base query.

        Parameters
        ----------
        base_query : Q
            Base query object
        group_by : str
            Group by code
        filters : Dict[str, Any]
            Filter parameters

        Returns
        -------
        Q
            Updated query object
        """
        # Group by filter mapping for cleaner code
        group_filter_mapping = {
            "200": {  # Customers
                'nh_kh1': 'hoa_don_ban_hang__ma_kh__customer_group1__uuid',
                'nh_kh2': 'hoa_don_ban_hang__ma_kh__customer_group2__uuid',
                'nh_kh3': 'hoa_don_ban_hang__ma_kh__customer_group3__uuid',
                'ma_kh': 'hoa_don_ban_hang__ma_kh__uuid',
            },
            "300": {  # Materials
                'ma_vt': 'ma_vt__uuid',
                'nh_vt1': 'ma_vt__nh_vt1__uuid',
                'nh_vt2': 'ma_vt__nh_vt2__uuid',
                'nh_vt3': 'ma_vt__nh_vt3__uuid',
            },
            "700": {'ma_unit': 'hoa_don_ban_hang__unit_id__uuid'},  # Units
            "810": {'ma_bp': 'ma_bp__uuid'},  # Departments
            "820": {'ma_vv': 'ma_vv__uuid'},  # Tasks
            "830": {'ma_hd': 'ma_hd__uuid'},  # Contracts
            "840": {'ma_ku': 'ma_ku__uuid'},  # Agreements
            "850": {'ma_phi': 'ma_phi__uuid'},  # Fees
            "860": {'ma_sp': 'ma_sp__uuid'},  # Products
            "910": {'ma_nvbh': 'hoa_don_ban_hang__ma_nvbh__uuid'},  # Sales Staff
        }

        # Apply filters based on group_by mapping
        if group_by in group_filter_mapping:
            for filter_key, query_path in group_filter_mapping[group_by].items():
                filter_value = filters.get(filter_key)
                if filter_value:
                    base_query &= Q(**{query_path: filter_value})

        return base_query
