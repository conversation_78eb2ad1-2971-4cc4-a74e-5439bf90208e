"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bao <PERSON> So Sanh Ban Hang Hai Ky (Sales Comparison Report Between Two Periods).
"""

from datetime import datetime

from django.db import models
from rest_framework import serializers


class DetailByChoices(models.TextChoices):
    """
    Enum cho các lựa chọn detail_by trong báo c<PERSON>o so sánh bán hàng.

    Updated entity codes with query level classification:
    - Invoice level: Customers (200), Units (700), Sales Staff (910)
    - Detail level: Materials (300), Departments (810), Tasks (820),
                   Contracts (830), Agreements (840), Fees (850), Products (860)
    """

    # Invoice Level Entities
    KHACH_HANG = "200", "Khách hàng"
    NHOM_KHACH_1 = "210", "Nhóm khách 1"
    NHOM_KHACH_2 = "220", "Nhóm khách 2"
    NHOM_KHACH_3 = "230", "<PERSON><PERSON><PERSON><PERSON> khách 3"
    DON_VI = "700", "Đơn vị"
    NHAN_VIEN_BAN_HANG = "910", "<PERSON>h<PERSON> viên bán hàng"

    # Detail Level Entities
    VAT_TU = "300", "Vật tư"
    NHOM_VAT_TU_1 = "320", "Nhóm vật tư 1"
    NHOM_VAT_TU_2 = "330", "Nhóm vật tư 2"
    NHOM_VAT_TU_3 = "340", "Nhóm vật tư 3"
    BO_PHAN = "810", "Bộ phận"
    VU_VIEC = "820", "Vụ việc"
    HOP_DONG = "830", "Hợp đồng"
    KHE_UOC = "840", "Khế ước"
    PHI = "850", "Phí"
    SAN_PHAM = "860", "Sản phẩm"


class BaoCaoSoSanhBanHangHaiKyRequestSerializer(serializers.Serializer):
    """
    Serializer for Sales Comparison Report request parameters.

    Handles 32 filter conditions from cURL request plus period parameters.
    """

    # Period parameters
    ngay_ct1 = serializers.CharField(
        required=True, help_text="Ngày bắt đầu kỳ này (YYYYMMDD format, e.g., 20250101)"
    )
    ngay_ct2 = serializers.CharField(
        required=True,
        help_text="Ngày kết thúc kỳ này (YYYYMMDD format, e.g., 20250408)",
    )
    ngay_kt1 = serializers.CharField(
        required=True,
        help_text="Ngày bắt đầu kỳ trước (YYYYMMDD format, e.g., 20250414)",
    )
    ngay_kt2 = serializers.CharField(
        required=True,
        help_text="Ngày kết thúc kỳ trước (YYYYMMDD format, e.g., 20250626)",
    )

    # Display parameters
    detail_by = serializers.ChoiceField(
        choices=DetailByChoices.choices,
        required=False,
        default=DetailByChoices.KHACH_HANG,
        help_text="Cấp độ chi tiết hiển thị - xem DetailByChoices enum để biết các options",
    )
    group_by = serializers.CharField(
        required=False, allow_blank=True, help_text="Nhóm dữ liệu theo tiêu chí"
    )
    mau_bc = serializers.IntegerField(
        required=False, default=20, help_text="Mẫu báo cáo"
    )

    # Customer filters
    ma_kh = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã khách hàng"
    )
    nh_kh1 = serializers.CharField(
        required=False, allow_blank=True, help_text="Nhóm khách hàng 1"
    )
    nh_kh2 = serializers.CharField(
        required=False, allow_blank=True, help_text="Nhóm khách hàng 2"
    )
    nh_kh3 = serializers.CharField(
        required=False, allow_blank=True, help_text="Nhóm khách hàng 3"
    )
    rg_code = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã vùng"
    )

    # Product filters
    ma_vt = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã vật tư"
    )
    ma_lvt = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã loại vật tư"
    )
    ton_kho_yn = serializers.BooleanField(
        required=False, default=True, help_text="Có tồn kho hay không"
    )
    nh_vt1 = serializers.CharField(
        required=False, allow_blank=True, help_text="Nhóm vật tư 1"
    )
    nh_vt2 = serializers.CharField(
        required=False, allow_blank=True, help_text="Nhóm vật tư 2"
    )
    nh_vt3 = serializers.CharField(
        required=False, allow_blank=True, help_text="Nhóm vật tư 3"
    )

    # Warehouse and unit filters
    ma_kho = serializers.CharField(required=False, allow_blank=True, help_text="Mã kho")
    ma_unit = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã đơn vị"
    )

    # Document type filters
    nh_ct = serializers.CharField(
        required=False, default="BH1,BH2,BH3", help_text="Nhóm chứng từ (BH1,BH2,BH3)"
    )
    loai_du_lieu = serializers.IntegerField(
        required=False, default=1, help_text="Loại dữ liệu"
    )

    # Department and classification filters
    ma_bp = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã bộ phận"
    )
    ma_vv = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã vụ việc"
    )
    ma_hd = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã hợp đồng"
    )
    ma_dtt = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã đối tượng thanh toán"
    )
    ma_ku = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã khu vực"
    )
    ma_phi = serializers.CharField(required=False, allow_blank=True, help_text="Mã phí")
    ma_sp = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã sản phẩm"
    )
    ma_lsx = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã lệnh sản xuất"
    )
    ma_cp0 = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã chi phí"
    )

    # Transaction filters
    ma_gd = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã giao dịch"
    )
    ma_nvbh = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã nhân viên bán hàng"
    )

    # Account filters
    tk_vt = serializers.CharField(
        required=False, allow_blank=True, help_text="Tài khoản vật tư"
    )
    tk_dt = serializers.CharField(
        required=False, allow_blank=True, help_text="Tài khoản doanh thu"
    )
    tk_gv = serializers.CharField(
        required=False, allow_blank=True, help_text="Tài khoản giá vốn"
    )

    # Batch and location filters
    ma_lo = serializers.CharField(required=False, allow_blank=True, help_text="Mã lô")
    ma_vi_tri = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã vị trí"
    )

    def validate(self, data):
        """
        Validate request data including group_by logic.
        """
        detail_by = data.get('detail_by')
        group_by = data.get('group_by')

        # Validate group_by if provided
        if group_by:
            # Import here to avoid circular imports
            from django_ledger.services.ban_hang.bao_cao_ban_hang.bao_cao_so_sanh_ban_hang_hai_ky.constants import (
                is_valid_group_by_combination,
            )

            # Cannot group by self
            if detail_by == group_by:
                raise serializers.ValidationError(
                    {
                        'group_by': f'Cannot group by the same value as detail_by ({detail_by})'
                    }
                )

            # Check if combination is valid
            if not is_valid_group_by_combination(detail_by, group_by):
                raise serializers.ValidationError(
                    {
                        'group_by': f'Invalid group_by combination: detail_by={detail_by}, group_by={group_by}'
                    }
                )

        return data

    # Document number filters
    so_ct1 = serializers.CharField(
        required=False, allow_blank=True, help_text="Số chứng từ từ"
    )
    so_ct2 = serializers.CharField(
        required=False, allow_blank=True, help_text="Số chứng từ đến"
    )
    dien_giai = serializers.CharField(
        required=False, allow_blank=True, help_text="Diễn giải"
    )

    def validate_ngay_ct1(self, value):
        """Validate start date of period 1."""
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "Invalid date format. Expected YYYYMMDD (e.g., 20250101)"
            )

    def validate_ngay_ct2(self, value):
        """Validate end date of period 1."""
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "Invalid date format. Expected YYYYMMDD (e.g., 20250408)"
            )

    def validate_ngay_kt1(self, value):
        """Validate start date of period 2."""
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "Invalid date format. Expected YYYYMMDD (e.g., 20250414)"
            )

    def validate_ngay_kt2(self, value):
        """Validate end date of period 2."""
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "Invalid date format. Expected YYYYMMDD (e.g., 20250626)"
            )

    def validate_detail_by(self, value):
        """Validate detail_by parameter."""
        if value not in DetailByChoices.values:
            raise serializers.ValidationError(
                f"Invalid detail_by value. Must be one of: {', '.join(DetailByChoices.values)}"
            )
        return value

    def get_parsed_filters(self):
        """
        Parse and convert filter parameters to service-compatible format.

        Returns
        -------
        Dict[str, Any]
            Parsed filters with converted date formats
        """
        validated_data = self.validated_data

        # Convert date strings to datetime objects
        period_1 = {
            'start_date': datetime.strptime(
                validated_data['ngay_ct1'], "%Y%m%d"
            ).date(),
            'end_date': datetime.strptime(validated_data['ngay_ct2'], "%Y%m%d").date(),
        }

        period_2 = {
            'start_date': datetime.strptime(
                validated_data['ngay_kt1'], "%Y%m%d"
            ).date(),
            'end_date': datetime.strptime(validated_data['ngay_kt2'], "%Y%m%d").date(),
        }

        # Build parsed filters
        parsed_filters = {
            'period_1': period_1,
            'period_2': period_2,
            'detail_by': validated_data.get('detail_by', '200'),
            'group_by': validated_data.get('group_by', ''),
            'mau_bc': validated_data.get('mau_bc', 20),
        }

        # Add all filter conditions
        filter_fields = [
            'ma_kh',
            'nh_kh1',
            'nh_kh2',
            'nh_kh3',
            'rg_code',
            'ma_vt',
            'ma_lvt',
            'ton_kho_yn',
            'nh_vt1',
            'nh_vt2',
            'nh_vt3',
            'ma_kho',
            'ma_unit',
            'nh_ct',
            'loai_du_lieu',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            'ma_gd',
            'ma_nvbh',
            'tk_vt',
            'tk_dt',
            'tk_gv',
            'ma_lo',
            'ma_vi_tri',
            'so_ct1',
            'so_ct2',
            'dien_giai',
        ]

        for field in filter_fields:
            if field in validated_data:
                parsed_filters[field] = validated_data[field]

        return parsed_filters


class BaoCaoSoSanhBanHangHaiKyResponseSerializer(serializers.Serializer):
    """
    Serializer for Sales Comparison Report response data.

    Dynamic response structure based on detail_by parameter.
    """

    # Normal report fields (optional for flexibility)
    ma = serializers.CharField(
        required=False, allow_blank=True, help_text="Mã định danh (theo detail_by)"
    )
    ten = serializers.CharField(
        required=False, allow_blank=True, help_text="Tên mô tả (theo detail_by)"
    )
    nhom = serializers.CharField(required=False, allow_blank=True, help_text="Mã nhóm")
    ten_nhom = serializers.CharField(
        required=False, allow_blank=True, help_text="Tên nhóm"
    )
    sl_kn = serializers.FloatField(required=False, help_text="Số lượng kỳ này")
    tien_kn = serializers.FloatField(required=False, help_text="Tiền kỳ này")
    sl_kt = serializers.FloatField(required=False, help_text="Số lượng kỳ trước")
    tien_kt = serializers.FloatField(required=False, help_text="Tiền kỳ trước")
    sl_cl = serializers.FloatField(
        required=False, help_text="Số lượng chênh lệch (sl_kn - sl_kt)"
    )
    sl_tl = serializers.FloatField(
        required=False, help_text="Số lượng tỷ lệ % ((sl_kn - sl_kt) / sl_kt * 100)"
    )
    tien_cl = serializers.FloatField(
        required=False, help_text="Tiền chênh lệch (tien_kn - tien_kt)"
    )
    tien_nt_cl = serializers.FloatField(
        required=False, help_text="Tiền ngoại tệ chênh lệch"
    )
    tien_tl = serializers.FloatField(
        required=False, help_text="Tiền tỷ lệ % ((tien_kn - tien_kt) / tien_kt * 100)"
    )

    # Group_by fields
    is_group_header = serializers.BooleanField(
        required=False, help_text="Indicates if this is a group header row"
    )

    # Debug fields (for troubleshooting)
    debug_info = serializers.DictField(
        required=False, help_text="Debug information when no data is found"
    )
    error = serializers.CharField(
        required=False, allow_blank=True, help_text="Error message if any"
    )
    debug = serializers.CharField(
        required=False, allow_blank=True, help_text="Debug message"
    )
