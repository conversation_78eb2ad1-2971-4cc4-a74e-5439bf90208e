"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for PhieuChi model.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models import PhieuChiModel  # noqa: F401,
from django_ledger.repositories.tien_mat.hach_toan.phieu_chi.phieu_chi import (  # noqa: F401,
    PhieuChiRepository,
)
from django_ledger.repositories.tien_mat.hach_toan.phieu_chi.phieu_chi_chi_tiet import (  # noqa: F401,
    PhieuChiChiTietRepository,
)
from django_ledger.repositories.tien_mat.hach_toan.phieu_chi.phieu_chi_thue import (  # noqa: F401,
    PhieuChiThueRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.utils_new.debt_management.cong_no_creation import CongNoCreation  # noqa: F401,


class PhieuChiService(BaseService):
    """
    Service class for handling PhieuChiModel business logic.
    Implements business rules and orchestrates repository operations.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Phiếu chi accounting mappings
    # Use Header-Only pattern for now - each detail will be processed separately
    PAYMENT_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'CHIHD',                # Chi hóa đơn (ma_ngv=1)
            'debit_account_field': 'tk_no',         # Tài khoản chi phí/công nợ - DEBIT
            'credit_account_field': 'tk',           # Tài khoản tiền - CREDIT
            'debit_account_source': 'detail',       # Lấy debit account từ detail (tk_no)
            'credit_account_source': 'header',      # Lấy credit account từ header (tk)
            'amount_field': 'tien',                 # Số tiền từ detail
            'detail_source': 'children',            # Related name to detail (PhieuChiChiTiet)
            'canCreate': True                       # Default: always create entry
        }
    ]

    def __init__(self):  # noqa: C901
        """
        Initialize the service with repositories.
        """
        super().__init__()
        self.repository = PhieuChiRepository()
        self.chi_tiet_repository = PhieuChiChiTietRepository()
        self.thue_repository = PhieuChiThueRepository()
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(self, phieu_chi: PhieuChiModel) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên ma_ngv và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - CHIHD cho ma_ngv=1 (Chi hóa đơn)
        - CHINCC cho ma_ngv=2,3,4 (Chi nhà cung cấp)
        - CHITHUE cho bút toán thuế (nếu có data thuế)

        Parameters
        ----------
        phieu_chi : PhieuChiModel
            Phiếu chi để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với journal_type được set theo business logic
        """
        mappings = []

        # ✅ BUSINESS LOGIC: Determine journal type based on ma_ngv
        if phieu_chi.ma_ngv == "1":
            journal_type = "CHIHD"  # Chi hóa đơn
        elif phieu_chi.ma_ngv in ["2", "3", "4"]:
            journal_type = "CHINCC"  # Chi nhà cung cấp
        else:
            journal_type = "CHIHD"  # Default fallback

        # Add main payment mapping
        main_mapping = self.PAYMENT_ACCOUNTING_CONFIG[0].copy()
        main_mapping['journal_type'] = journal_type
        mappings.append(main_mapping)

        # TODO: Add tax mapping when we implement Header-Detail pattern for tax
        # For now, only handle main payment mapping

        return mappings

    def get(self, entity_slug: str, uuid: UUID) -> PhieuChiModel:  # noqa: C901
        """
        Get a specific PhieuChi instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : UUID
            The UUID of the PhieuChi to retrieve

        Returns
        -------
        PhieuChiModel
            The PhieuChi instance

        Raises
        ------
        PhieuChiModel.DoesNotExist
            If the instance does not exist
        """
        return self.repository.get_by_uuid(entity_slug=entity_slug, uuid=uuid)

    def list(  # noqa: C901
        self,
        entity_slug: str,
        search_query: str = None,
        status: str = None,
        from_date: str = None,
        to_date: str = None,
    ) -> QuerySet:
        """
        Get a list of PhieuChi instances with filters

        Parameters:
        ----------:
        entity_slug : str
            The entity slug
        search_query : str, optional
            Search query to filter results, by default None
        status : str, optional
            Status filter ('1' for active, '0' for inactive), by default None:
        from_date : str, optional
            Start date for filtering, by default None:
        to_date : str, optional
            End date for filtering, by default None

        Returns
        -------
        QuerySet
            QuerySet of PhieuChi instances
        """
        return self.repository.list(
            entity_slug=entity_slug,
            search_query=search_query,
            status=status,
            from_date=from_date,
            to_date=to_date,
        )

    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuChiModel:  # noqa: C901
        """
        Creates a new PhieuChiModel instance with related details.Parameters()
        ----------:
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuChiModel including details.Returns()
        -------
        PhieuChiModel
            The created PhieuChiModel instance.
        """
        # Extract child items if present (support both naming conventions)
        chi_tiet_data = data.pop('chi_tiet_data', None)
        chi_tiet = data.pop('chi_tiet', None)
        child_items = data.pop('child_items', None)
        detail_items = chi_tiet_data or chi_tiet or child_items  # Priority: chi_tiet_data > chi_tiet > child_items

        # Extract tax data
        thue_data = data.pop('thue_data', None)
        thue = data.pop('thue', None)
        tax_items = thue_data or thue  # Priority: thue_data > thue

        # Validate required fields
        self._validate_create_data(data)

        # Create the instance using repository
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # Create child items if provided
        if detail_items:
            for item in detail_items:
                # ✅ Ensure amount has max 2 decimal places for validation
                if 'tien' in item and item['tien'] is not None:
                    item['tien'] = round(float(item['tien']), 2)

                self.chi_tiet_repository.create(parent_field=instance, data=item)

        # Create tax records if provided
        if tax_items:
            for thue_item in tax_items:
                # ✅ Ensure tax amount has max 2 decimal places for validation
                if 't_thue' in thue_item and thue_item['t_thue'] is not None:
                    thue_item['t_thue'] = round(float(thue_item['t_thue']), 2)

                self.thue_repository.create(parent_field=instance, data=thue_item)

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not instance.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(source_document=instance,
                                                                       document_type="phiếu chi",
                                                                       account_mappings=self._determine_accounting_mappings(instance))
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuChi and accounting entries
                raise Exception(f"Failed to create accounting entry for PhieuChi {instance.so_ct}: {str(e)}") from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance

    @transaction.atomic()
    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> PhieuChiModel:  # noqa: C901
        """
        Updates an existing PhieuChiModel instance.Parameters()
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuChiModel to update.
        data : Dict[str, Any]
            The data to update.Returns()
        -------
        PhieuChiModel
            The updated PhieuChiModel instance.Raises()
        ------
        PhieuChiModel.DoesNotExist()
            If the instance does not exist.
        """
        # Extract details and tax data
        chi_tiet_data = data.pop('chi_tiet', None)
        thue_data = data.pop('thue', None)
        # Update the main record
        instance = self.repository.update(entity_slug, uuid, data)
        # Update details if provided:
        if chi_tiet_data is not None:
            self._update_chi_tiet(instance, chi_tiet_data)

        # Update tax records if provided:
        if thue_data is not None:
            self._update_thue(instance, thue_data)

        # ✅ UNIFIED ACCOUNTING: Cập nhật bút toán kế toán
        # Only update accounting if ledger exists
        if instance.ledger:
            try:
                self._cong_no_service.update_document_accounting_entries(source_document=instance,
                                                                       document_type="phiếu chi",
                                                                       account_mappings=self._determine_accounting_mappings(instance))
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuChi and accounting entries
                raise Exception(f"Failed to update accounting entry for PhieuChi {instance.so_ct}: {str(e)}") from e
        # Note: If no ledger exists, skip accounting update

        return instance

    @transaction.atomic()
    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuChiModel instance and its related records.Parameters()
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuChiModel to delete.Returns()
        -------
        bool
            True if the instance was deleted, False otherwise.Raises()
        ------
        PhieuChiModel.DoesNotExist()
            If the instance does not exist.
        """
        # Delete related records first (cascade should handle this, but being explicit)
        self.chi_tiet_repository.delete_by_phieu_chi(uuid)
        self.thue_repository.delete_by_phieu_chi(uuid)

        # Delete the main record:
        return self.repository.delete(entity_slug, uuid)

    def get_by_entity(self, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiModel instances for a specific entity.Parameters()
        ----------:
        entity_slug : str
            The entity slug.Returns()
        -------
        QuerySet
            QuerySet of PhieuChiModel instances for the entity.
        """
        return self.repository.get_by_entity(entity_slug)

    def get_active(self, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Gets active PhieuChiModel instances for a specific entity.Parameters()
        ----------:
        entity_slug : str
            The entity slug.Returns()
        -------
        QuerySet
            QuerySet of active PhieuChiModel instances.
        """
        return self.repository.get_active(entity_slug)

    def get_by_date_range(
        self, entity_slug: str, from_date: str, to_date: str
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiModel instances within a date range.Parameters()
        ----------
        entity_slug : str
            The entity slug.
        from_date : str
            Start date.
        to_date : str
            End date.Returns()
        -------
        QuerySet
            QuerySet of PhieuChiModel instances within the date range.
        """
        return self.repository.get_by_date_range(entity_slug, from_date, to_date)

    def get_by_customer(
        self, entity_slug: str, customer_uuid: Union[str, UUID]
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiModel instances for a specific customer.Parameters()
        ----------:
        entity_slug : str
            The entity slug.
        customer_uuid : Union[str, UUID]
            The customer UUID.Returns()
        -------
        QuerySet
            QuerySet of PhieuChiModel instances for the customer.
        """
        return self.repository.get_by_customer(entity_slug, customer_uuid)

    def _validate_create_data(self, data: Dict[str, Any]) -> None:  # noqa: C901
        """
        Validates data for creating a PhieuChiModel.Parameters()
        ----------:
        data : Dict[str, Any]
            The data to validate.Raises()
        ------
        ValueError
            If validation fails.
        """
        required_fields = [
            'dien_giai',
            'ngay_ct',
            'ngay_lct',
        ]
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValueError(f"Required field {field}' is missing or None")

    def _update_chi_tiet(
        self, instance: PhieuChiModel, chi_tiet_data: list
    ) -> None:  # noqa: C901
        """
        Updates detail records for a PhieuChiModel.Parameters()
        ----------:
        instance : PhieuChiModel
            The parent PhieuChiModel instance.
        chi_tiet_data : list
            List of detail data dictionaries.
        """
        # Delete existing details
        self.chi_tiet_repository.delete_by_phieu_chi(instance.uuid)

        # Create new details
        if chi_tiet_data:
            self.chi_tiet_repository.bulk_create(instance, chi_tiet_data)

    def _update_thue(
        self, instance: PhieuChiModel, thue_data: list
    ) -> None:  # noqa: C901
        """
        Updates tax records for a PhieuChiModel.Parameters()
        ----------:
        instance : PhieuChiModel
            The parent PhieuChiModel instance.
        thue_data : list
            List of tax data dictionaries.
        """
        # Delete existing tax records
        self.thue_repository.delete_by_phieu_chi(instance.uuid)

        # Create new tax records
        if thue_data:
            self.thue_repository.bulk_create(instance, thue_data)

