"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietHoaDonNhapMuaXuatThangModel, which represents the details
of a HoaDonNhapMuaXuatThang (Invoice Import/Purchase/Export Monthly).
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietHoaDonNhapMuaXuatThangModelQueryset(models.QuerySet):
    """
    A custom defined ChiTietHoaDonNhapMuaXuatThangModel QuerySet.
    """

    def for_invoice(self, hoa_don_uuid):  # noqa: C901
        """
        Filter by invoice UUID.
        """
        return self.filter(hoa_don__uuid=hoa_don_uuid)


class ChiTietHoaDonNhapMuaXuatThangModelManager(models.Manager):
    """
    A custom defined ChiTietHoaDonNhapMuaXuatThangModel Manager that will act as an interface to handle the  # noqa: E501
    ChiTietHoaDonNhapMuaXuatThangModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietHoaDonNhapMuaXuatThangModelQueryset.
        """
        return ChiTietHoaDonNhapMuaXuatThangModelQueryset(self.model, using=self._db)


class ChiTietHoaDonNhapMuaXuatThangModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietHoaDonNhapMuaXuatThangModel database will inherit from.  # noqa: E501
    The ChiTietHoaDonNhapMuaXuatThangModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    hoa_don : ForeignKey
        Reference to the main HoaDonNhapMuaXuatThang.
    id_goc : IntegerField
        Original ID.
    line : IntegerField
        Line number.
    ma_vt : ForeignKey
        Material code.
    dvt : ForeignKey
        Unit of measure.
    ten_dvt : CharField
        Unit name.
    ma_kho : ForeignKey
        Warehouse code.
    ten_kho : CharField
        Warehouse name.
    ma_lo : ForeignKey
        Lot code.
    ten_lo : CharField
        Lot name.
    lo_yn : IntegerField
        Lot management flag.
    ma_vi_tri : ForeignKey
        Location code.
    ten_vi_tri : CharField
        Location name.
    vi_tri_yn : IntegerField
        Location management flag.
    he_so : DecimalField
        Conversion factor.
    the_tich : DecimalField
        Volume.
    khoi_luong : DecimalField
        Weight.
    so_luong : DecimalField
        Quantity.
    gia_nt0 : DecimalField
        Price in foreign currency 0.
    tien_nt0 : DecimalField
        Amount in foreign currency 0.
    cp_nt : DecimalField
        Cost in foreign currency.
    ma_thue : ForeignKey
        Tax code.
    ten_thue : CharField
        Tax name.
    thue_suat : DecimalField
        Tax rate.
    tk_thue : ForeignKey
        Tax account.
    ten_tk_thue : CharField
        Tax account name.
    thue_nt : DecimalField
        Tax in foreign currency.
    tk_vt : ForeignKey
        Material account.
    ten_tk_vt : CharField
        Material account name.
    px_dd : IntegerField
        Export identification.
    ma_nx : ForeignKey
        Import/Export code.
    tk_cpxt : ForeignKey
        Export cost account.
    ten_tk_cpxt : CharField
        Export cost account name.
    gia0 : DecimalField
        Price 0.
    tien0 : DecimalField
        Amount 0.
    cp : DecimalField
        Cost.
    thue : DecimalField
        Tax.
    ma_bp : ForeignKey
        Department code.
    ma_vv : ForeignKey
        Task code.
    ma_hd : ForeignKey
        Contract code.
    ma_dtt : ForeignKey
        Tax object code.
    ma_ku : ForeignKey
        Area code.
    ma_phi : ForeignKey
        Fee code.
    ma_sp : ForeignKey
        Product code.
    ma_lsx : ForeignKey
        Production order code.
    tien_nt : DecimalField
        Amount in foreign currency.
    tien : DecimalField
        Amount.
    tt_nt : DecimalField
        Payment in foreign currency.
    tt : DecimalField
        Payment.
    gia_nt : DecimalField
        Price in foreign currency.
    gia : DecimalField
        Price.
    id_pn : IntegerField
        Receipt ID.
    line_pn : IntegerField
        Receipt line.
    id_dh5 : IntegerField
        Order ID 5.
    id_dh6 : IntegerField
        Order ID 6.
    line_dh : IntegerField
        Order line.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)

    # Link to main invoice
    hoa_don = models.ForeignKey(
        'django_ledger.HoaDonNhapMuaXuatThangModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet',
        verbose_name=_('Hóa đơn'),
        help_text=_('Main invoice reference'),
    )

    # Line information
    id_goc = models.IntegerField(
        verbose_name=_('ID gốc'),
        help_text=_('Original ID'),
        null=True,
        blank=True,
    )
    line = models.IntegerField(
        verbose_name=_('Số dòng'),
        help_text=_('Line number'),
    )

    # Material information
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vật tư'),
        help_text=_('Material code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_vt',
        null=True,
        blank=True,
    )
    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.CASCADE,
        verbose_name=_('Đơn vị tính'),
        help_text=_('Unit of measure'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_dvt',
        null=True,
        blank=True,
    )
    ten_dvt = models.CharField(
        max_length=100,
        verbose_name=_('Tên đơn vị tính'),
        help_text=_('Unit name'),
        blank=True,
    )

    # Warehouse information
    ma_kho = models.ForeignKey(
        'django_ledger.KhoHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã kho'),
        help_text=_('Warehouse code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_kho',
        null=True,
        blank=True,
    )
    ten_kho = models.CharField(
        max_length=200,
        verbose_name=_('Tên kho'),
        help_text=_('Warehouse name'),
        blank=True,
    )
    ma_lo = models.ForeignKey(
        'django_ledger.LoModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã lô'),
        help_text=_('Lot code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_lo',
        null=True,
        blank=True,
    )
    ten_lo = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Tên lô'),
        help_text=_('Lot name'),
    )
    lo_yn = models.IntegerField(
        default=0,
        verbose_name=_('Quản lý theo lô'),
        help_text=_('Lot management flag'),
    )

    # Location information
    ma_vi_tri = models.ForeignKey(
        'django_ledger.ViTriKhoHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vị trí'),
        help_text=_('Location code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_vi_tri',
        null=True,
        blank=True,
    )
    ten_vi_tri = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Tên vị trí'),
        help_text=_('Location name'),
    )
    vi_tri_yn = models.IntegerField(
        default=0,
        verbose_name=_('Quản lý theo vị trí'),
        help_text=_('Location management flag'),
    )

    # Quantity and price information
    he_so = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        verbose_name=_('Hệ số'),
        help_text=_('Conversion factor'),
    )
    the_tich = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        verbose_name=_('Thể tích'),
        help_text=_('Volume'),
    )
    khoi_luong = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        verbose_name=_('Khối lượng'),
        help_text=_('Weight'),
    )
    so_luong = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        verbose_name=_('Số lượng'),
        help_text=_('Quantity'),
    )
    gia_nt0 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Giá ngoại tệ 0'),
        help_text=_('Price in foreign currency 0'),
    )
    tien_nt0 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền ngoại tệ 0'),
        help_text=_('Amount in foreign currency 0'),
    )
    cp_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Chi phí ngoại tệ'),
        help_text=_('Cost in foreign currency'),
    )

    # Tax information
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thuế'),
        help_text=_('Tax code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_thue',
        null=True,
        blank=True,
    )
    ten_thue = models.CharField(
        max_length=100,
        verbose_name=_('Tên thuế'),
        help_text=_('Tax name'),
        blank=True,
    )
    thue_suat = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_('Thuế suất'),
        help_text=_('Tax rate'),
    )
    tk_thue = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản thuế'),
        help_text=_('Tax account'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_tk_thue',
        null=True,
        blank=True,
    )
    ten_tk_thue = models.CharField(
        max_length=200,
        verbose_name=_('Tên tài khoản thuế'),
        help_text=_('Tax account name'),
        blank=True,
    )
    thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Thuế ngoại tệ'),
        help_text=_('Tax in foreign currency'),
    )

    # Material account
    tk_vt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản vật tư'),
        help_text=_('Material account'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_tk_vt',
        null=True,
        blank=True,
    )
    ten_tk_vt = models.CharField(
        max_length=200,
        verbose_name=_('Tên tài khoản vật tư'),
        help_text=_('Material account name'),
        blank=True,
    )
    px_dd = models.IntegerField(
        verbose_name=_('Phiếu xuất định danh'),
        help_text=_('Export identification'),
    )

    # Classification codes
    ma_nx = models.ForeignKey(
        'django_ledger.NhapXuatModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã nhập xuất'),
        help_text=_('Import/Export code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_nx',
        null=True,
        blank=True,
    )
    tk_cpxt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản chi phí xuất'),
        help_text=_('Export cost account'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_tk_cpxt',
        null=True,
        blank=True,
    )
    ten_tk_cpxt = models.CharField(
        max_length=200,
        verbose_name=_('Tên tài khoản chi phí xuất'),
        help_text=_('Export cost account name'),
        blank=True,
    )

    # Price and amount in VND
    gia0 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Giá 0'),
        help_text=_('Price 0'),
    )
    tien0 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền 0'),
        help_text=_('Amount 0'),
    )
    cp = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Chi phí'),
        help_text=_('Cost'),
    )
    thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Thuế'),
        help_text=_('Tax'),
    )

    # Allocation codes
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã bộ phận'),
        help_text=_('Department code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_bp',
        null=True,
        blank=True,
    )
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vụ việc'),
        help_text=_('Task code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_vv',
        null=True,
        blank=True,
    )
    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã hợp đồng'),
        help_text=_('Contract code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_hd',
        null=True,
        blank=True,
    )
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã đợt thanh toán'),
        help_text=_('Payment batch code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_dtt',
        null=True,
        blank=True,
    )
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.CASCADE,
        verbose_name=_('Khế ước'),
        help_text=_('Contract code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_ku',
        null=True,
        blank=True,
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã phí'),
        help_text=_('Fee code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_phi',
        null=True,
        blank=True,
    )
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã sản phẩm'),
        help_text=_('Product code'),
        related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_sp',
        null=True,
        blank=True,
    )
    # ma_lsx = models.ForeignKey(
    #     'django_ledger.LenhSanXuatModel',
    #     on_delete=models.CASCADE,
    #     verbose_name=_('Mã lệnh sản xuất'),
    #     help_text=_('Production order code'),
    #     related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_lsx',
    #     null=True,
    #     blank=True,
    # )
    ma_lsx = models.CharField(
        max_length=20,
        verbose_name=_('Mã lệnh sản xuất'),
        help_text=_('Production order code'),
        null=True,
        blank=True,
    )

    # Total amounts and payments
    tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền ngoại tệ'),
        help_text=_('Amount in foreign currency'),
    )
    tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền'),
        help_text=_('Amount'),
    )
    tt_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Thanh toán ngoại tệ'),
        help_text=_('Payment in foreign currency'),
    )
    tt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Thanh toán'),
        help_text=_('Payment'),
    )
    gia_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Giá ngoại tệ'),
        help_text=_('Price in foreign currency'),
    )
    gia = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Giá'),
        help_text=_('Price'),
    )

    # Order links
    id_pn = models.IntegerField(
        verbose_name=_('ID phiếu nhập'),
        help_text=_('Receipt ID'),
        null=True,
        blank=True,
    )
    line_pn = models.IntegerField(
        verbose_name=_('Dòng phiếu nhập'),
        help_text=_('Receipt line'),
        null=True,
        blank=True,
    )
    id_dh5 = models.IntegerField(
        verbose_name=_('ID đơn hàng 5'),
        help_text=_('Order ID 5'),
        null=True,
        blank=True,
    )
    id_dh6 = models.IntegerField(
        verbose_name=_('ID đơn hàng 6'),
        help_text=_('Order ID 6'),
        null=True,
        blank=True,
    )
    line_dh = models.IntegerField(
        verbose_name=_('Dòng đơn hàng'),
        help_text=_('Order line'),
        null=True,
        blank=True,
    )

    objects = ChiTietHoaDonNhapMuaXuatThangModelManager.from_queryset(
        ChiTietHoaDonNhapMuaXuatThangModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi Tiết Hóa Đơn Nhập Mua Xuất Tháng')
        verbose_name_plural = _('Chi Tiết Hóa Đơn Nhập Mua Xuất Tháng')
        ordering = ['line']
        indexes = [
            models.Index(fields=['hoa_don']),
            models.Index(fields=['ma_vt']),
            models.Index(fields=['ma_kho']),
            models.Index(fields=['line']),
            models.Index(fields=['created']),
            models.Index(fields=['updated']),
        ]

    def __str__(self):
        return f"{self.hoa_don.so_ct} - Dòng {self.line}"


class ChiTietHoaDonNhapMuaXuatThangModel(ChiTietHoaDonNhapMuaXuatThangModelAbstract):
    """
    Base ChiTietHoaDonNhapMuaXuatThangModel from Abstract.
    """

    class Meta(ChiTietHoaDonNhapMuaXuatThangModelAbstract.Meta):
        abstract = False
        db_table = "chi_tiet_hoa_don_nhap_mua_xuat_thang"
