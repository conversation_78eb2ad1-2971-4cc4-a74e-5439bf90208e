# Refactor <PERSON><PERSON><PERSON>ơn Mua Hàng Trong Nước Services

## <PERSON><PERSON><PERSON> tiêu
- [ ] Refactor cấu trúc services để dễ đọc và maintain hơn
- [ ] Áp dụng ChungTuMixin pattern từ phieu_xuat_kho
- [ ] Consolidate logic từ nhiều file thành cấu trúc clean
- [ ] Test API với curl command

## Phân tích hiện tại
### Cấu trúc file hiện tại (rất phức tạp):
- [ ] `service.py` - Main service với logic phức tạp
- [ ] `creation.py` - Creation logic riêng
- [ ] `update.py` - Update logic riêng  
- [ ] `accounting.py` - Accounting logic riêng
- [ ] `core.py` - Core utilities
- [ ] Các file chi tiết: chi_tiet, chi_phi, chi_phi_chi_tiet, thue

### Vấn đề:
- [ ] Logic phân tán quá nhiều file
- [ ] Khó theo dõi flow business logic
- [ ] Chưa áp dụng <PERSON>TuMixin pattern đúng cách

## Kế hoạch thực hiện

### Bước 1: Tạo cấu trúc service mới
- [ ] Tạo main service file theo pattern phieu_nhap_chi_phi_mua_hang
- [ ] Consolidate creation, update, accounting logic vào main service
- [ ] Giữ các service chi tiết riêng biệt (chi_tiet, chi_phi, thue)

### Bước 2: Áp dụng ChungTuMixin pattern
- [ ] Tham khảo phieu_xuat_kho model/serializer/repository
- [ ] Áp dụng process_chung_tu_fields_extraction_and_conversion
- [ ] Áp dụng update_instance_with_chung_tu_fields utilities
- [ ] Xử lý so_ct validation đúng cách

### Bước 3: Cleanup và tối ưu
- [ ] Remove các file không cần thiết
- [ ] Update __init__.py exports
- [ ] Đảm bảo imports đúng

### Bước 4: Testing
- [ ] Test API với curl command
- [ ] Verify ChungTuMixin pattern hoạt động đúng
- [ ] Check so_ct validation

## Tham khảo
- `django_ledger/services/mua_hang/hoa_don_mua_vao/phieu_nhap_chi_phi_mua_hang/` - Clean structure
- `django_ledger/models/ton_kho/xuat_kho_noi_bo/phieu_xuat_kho/` - ChungTuMixin pattern
- `django_ledger/repositories/chung_tu_item_utils` - ChungTu utilities

## Test Command
```bash
curl --location 'http://127.0.0.1:8001/api/entities/tutimi-dnus2xnc/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-hang-trong-nuoc/' \
--header 'authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
--header 'content-type: application/json' \
--data-raw '{...}'
```
