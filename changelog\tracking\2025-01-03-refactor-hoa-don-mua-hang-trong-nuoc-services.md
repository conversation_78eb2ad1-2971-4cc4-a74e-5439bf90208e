# Refactor <PERSON><PERSON><PERSON>ơn Mua Hàng Trong Nước Services

## <PERSON><PERSON><PERSON> tiêu

- [ ] Refactor cấu trúc services để dễ đọc và maintain hơn
- [ ] Áp dụng ChungTuMixin pattern từ phieu_xuat_kho
- [ ] Consolidate logic từ nhiều file thành cấu trúc clean
- [ ] Test API với curl command

## Phân tích hiện tại

### Cấu trúc file hiện tại (rất phức tạp):

- [ ] `service.py` - Main service với logic phức tạp
- [ ] `creation.py` - Creation logic riêng
- [ ] `update.py` - Update logic riêng
- [ ] `accounting.py` - Accounting logic riêng
- [ ] `core.py` - Core utilities
- [ ] Các file chi tiết: chi_tiet, chi_phi, chi_phi_chi_tiet, thue

### Vấn đề:

- [ ] Logic phân tán quá nhiều file
- [ ] Khó theo dõi flow business logic
- [ ] Chưa áp dụng <PERSON>uMixin pattern đúng cách

## Kế hoạch thực hiện

### Bước 1: Tạo cấu trúc service mới

- [x] Tạo main service file theo pattern phieu_nhap_chi_phi_mua_hang
- [x] Consolidate creation, update, accounting logic vào main service
- [x] Giữ các service chi tiết riêng biệt (chi_tiet, chi_phi, thue)

### Bước 2: Áp dụng ChungTuMixin pattern

- [x] Tham khảo phieu_xuat_kho model/serializer/repository
- [x] Áp dụng process_chung_tu_fields_extraction_and_conversion
- [x] Áp dụng update_instance_with_chung_tu_fields utilities
- [x] Xử lý so_ct validation đúng cách

### Bước 3: Cleanup và tối ưu

- [x] Remove các file không cần thiết
- [x] Update **init**.py exports
- [x] Đảm bảo imports đúng

### Bước 4: Testing

- [x] Test API với curl command
- [x] Verify ChungTuMixin pattern hoạt động đúng
- [x] Fix missing methods (get_filtered_data)
- [x] GET request hoạt động thành công
- [ ] POST request bị treo (cần debug ChungTuMixin create)
- [ ] Check so_ct validation

## Kết quả đã hoàn thành

### ✅ Đã hoàn thành:

1. **Refactor service structure**: Tạo file `hoa_don_mua_hang_trong_nuoc.py` mới theo pattern clean
2. **Consolidate logic**: Gộp logic từ creation.py, update.py, accounting.py vào main service
3. **Remove old files**: Xóa service.py, creation.py, update.py, accounting.py, core.py
4. **ChungTuMixin pattern**: Áp dụng utilities cho repository create/update methods
5. **Update imports**: Sửa view và các file khác import service mới
6. **Add missing methods**: Thêm get_all, create_instance, delete_by_hoa_don, get_filtered_data methods
7. **Fix repository**: Sửa create method để truyền entity_model đúng cách
8. **GET API hoạt động**: Test GET request thành công, trả về {"count":0,"next":null,"previous":null,"results":[]}

### ⚠️ Vấn đề hiện tại:

- POST request bị treo khi tạo hóa đơn mới (có thể do ChungTuMixin create process)
- Search query với parameter ?q= cũng bị treo
- Cần debug thêm để tìm nguyên nhân POST request bị treo

### 🧪 Test Results:
- ✅ **GET /api/entities/tutimi-dnus2xnc/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-hang-trong-nuoc/** - SUCCESS
- ❌ **POST /api/entities/tutimi-dnus2xnc/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-hang-trong-nuoc/** - HANGS
- ❌ **GET with search ?q=HD** - HANGS

## Tham khảo

- `django_ledger/services/mua_hang/hoa_don_mua_vao/phieu_nhap_chi_phi_mua_hang/` - Clean structure
- `django_ledger/models/ton_kho/xuat_kho_noi_bo/phieu_xuat_kho/` - ChungTuMixin pattern
- `django_ledger/repositories/chung_tu_item_utils` - ChungTu utilities

## Test Command

```bash
curl --location 'http://127.0.0.1:8001/api/entities/tutimi-dnus2xnc/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-hang-trong-nuoc/' \
--header 'authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
--header 'content-type: application/json' \
--data-raw '{...}'
```
