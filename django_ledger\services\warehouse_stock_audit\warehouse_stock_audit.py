"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Warehouse Stock Audit Service - Enterprise-grade inventory audit management
Follows AccountBalanceAuditService pattern for consistency and performance.
"""

import logging
from decimal import Decimal
from typing import Dict, Any, Optional, List, Set
from datetime import date, datetime

from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils import timezone

from django_ledger.models.ton_kho.stock_transaction import StockTransactionModel
from django_ledger.models.ton_kho.warehouse_stock_audit import WarehouseStockAuditModel
from django_ledger.models.ton_kho.stock_recalc_queue import StockRecalcQueueModel
from django_ledger.models.kho_hang import KhoHangModel
from django_ledger.models.vat_tu import VatTuModel
from django_ledger.services.base import BaseService
from django_ledger.services.stock_recalc_queue.stock_recalc_queue import StockRecalcQueueService

logger = logging.getLogger(__name__)


class WarehouseStockAuditService(BaseService):
    """
    Service class for WarehouseStockAuditModel.

    Enterprise-grade implementation with:
    - Hybrid processing: Auto-select immediate vs queue based on data size
    - Chronological recalculation: Maintains proper audit trail order
    - Microsecond timestamps: Ensures deterministic ordering
    - O(1) performance for audit record creation
    """

    # Hybrid configuration
    LARGE_RECALC_THRESHOLD = 10000  # Transactions threshold for queue processing
    CONSERVATIVE_ESTIMATE = 50000   # Fallback estimate on errors

    def __init__(self):
        self.stock_recalc_queue_service = StockRecalcQueueService()
        super().__init__()

    @transaction.atomic
    def create_audit_record(
        self,
        stock_transaction: StockTransactionModel,
    ) -> Dict[str, Any]:
        """
        Create audit record with O(1) performance.

        Automatically calculates balance_before from latest audit record
        instead of scanning all transactions. This is enterprise-grade
        ERP implementation standard.

        Parameters
        ----------
        stock_transaction : StockTransactionModel
            Stock transaction that triggered the audit

        Returns
        -------
        Dict[str, Any]
            Result with audit record info
        """
        try:
            warehouse = stock_transaction.ma_kho
            product = stock_transaction.ma_vt
            entity_model = warehouse.entity_model if warehouse else stock_transaction.journal_entry.ledger.entity

            # Get latest audit record - ONLY 1 QUERY O(1)
            filters = {
                'ma_vt': product,
                'entity_model': entity_model
            }

            # Add warehouse filter only if warehouse exists
            if warehouse:
                filters['ma_kho'] = warehouse

            # Add optional filters
            if stock_transaction.ma_lo:
                filters['ma_lo'] = stock_transaction.ma_lo

            if stock_transaction.ma_vi_tri:
                filters['ma_vi_tri'] = stock_transaction.ma_vi_tri

            latest_audit = WarehouseStockAuditModel.objects.filter(
                **filters
            ).order_by('-timestamp').first()

            # Calculate balance_before from latest audit (O(1))
            if latest_audit:
                balance_before = latest_audit.balance_after
                value_before = latest_audit.value_after
                unit_cost_before = latest_audit.unit_cost_after
            else:
                balance_before = Decimal('0')
                value_before = Decimal('0')
                unit_cost_before = Decimal('0')

            # Calculate changes
            change_quantity = stock_transaction.net_quantity
            change_value = (
                stock_transaction.tien_nhap if stock_transaction.sl_nhap > 0
                else -stock_transaction.tien_xuat
            )

            # Calculate new balances
            balance_after = balance_before + change_quantity
            value_after = value_before + change_value

            # Calculate unit cost (weighted average)
            if balance_after > 0:
                unit_cost_after = value_after / balance_after
            else:
                unit_cost_after = Decimal('0')

            # Create audit record
            audit_record = WarehouseStockAuditModel.objects.create(
                entity_model=entity_model,
                stock_transaction=stock_transaction,
                ma_kho=warehouse,
                ma_vt=product,
                ma_lo=stock_transaction.ma_lo,
                ma_vi_tri=stock_transaction.ma_vi_tri,
                balance_before=balance_before,
                balance_after=balance_after,
                change_quantity=change_quantity,
                change_type=stock_transaction.loai_giao_dich,
                value_before=value_before,
                value_after=value_after,
                change_value=change_value,
                unit_cost_before=unit_cost_before,
                unit_cost_after=unit_cost_after,
                timestamp=stock_transaction.journal_entry.timestamp or timezone.now(),
                is_recalculated=False,
                recalc_reason=None
            )

            logger.info(
                f"Created stock audit record {audit_record.uuid} for transaction {stock_transaction.uuid}"
            )

            return {
                'success': True,
                'audit_record_uuid': str(audit_record.uuid),
                'balance_before': balance_before,
                'balance_after': balance_after,
                'change_quantity': change_quantity,
                'value_after': value_after,
                'unit_cost_after': unit_cost_after
            }

        except Exception as e:
            logger.error(f"Error creating stock audit record: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def recalculate_stock_balances(
        self,
        warehouse_id: str,
        product_id: str,
        from_date: date,
        lot_id: str = None,
        location_id: str = None,
        force_queue: bool = False,
        force_immediate: bool = False
    ) -> Dict[str, Any]:
        """
        Hybrid recalculation with auto queue/immediate selection.

        Automatically chooses between immediate and queue processing
        based on data size for optimal performance.

        Parameters
        ----------
        warehouse_id : str
            Warehouse UUID to recalculate
        product_id : str
            Product UUID to recalculate
        from_date : date
            Recalculate from this date onwards
        lot_id : str, optional
            Specific lot to recalculate
        location_id : str, optional
            Specific location to recalculate
        force_queue : bool
            Force queue processing
        force_immediate : bool
            Force immediate processing

        Returns
        -------
        Dict[str, Any]
            Recalculation result
        """
        try:
            # Get warehouse and product
            warehouse = KhoHangModel.objects.get(uuid=warehouse_id)
            product = VatTuModel.objects.get(uuid=product_id)

            # Estimate transaction count for hybrid decision
            if not force_queue and not force_immediate:
                filters = {
                    'ma_kho': warehouse,
                    'ma_vt': product,
                    'created__date__gte': from_date
                }

                if lot_id:
                    filters['ma_lo__uuid'] = lot_id

                if location_id:
                    filters['ma_vi_tri__uuid'] = location_id

                try:
                    transaction_count = StockTransactionModel.objects.filter(**filters).count()
                except Exception as e:
                    logger.warning(f"Error estimating transaction count: {str(e)}")
                    transaction_count = self.CONSERVATIVE_ESTIMATE

                # Hybrid decision
                use_queue = transaction_count >= self.LARGE_RECALC_THRESHOLD
            else:
                use_queue = force_queue and not force_immediate

            if use_queue:
                # Queue processing for large datasets
                return self._queue_recalculation(
                    warehouse=warehouse,
                    product=product,
                    from_date=from_date,
                    lot_id=lot_id,
                    location_id=location_id
                )
            else:
                # Immediate processing for small datasets
                return self._immediate_recalculation(
                    warehouse=warehouse,
                    product=product,
                    from_date=from_date,
                    lot_id=lot_id,
                    location_id=location_id
                )

        except Exception as e:
            logger.error(f"Error in stock balance recalculation: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processing_method': 'error'
            }

    def _queue_recalculation(
        self,
        warehouse: KhoHangModel,
        product: VatTuModel,
        from_date: date,
        lot_id: str = None,
        location_id: str = None
    ) -> Dict[str, Any]:
        """Queue recalculation for large datasets."""
        try:
            # Get optional related objects
            lot = None
            if lot_id:
                from django_ledger.models.lo import LoModel
                lot = LoModel.objects.get(uuid=lot_id)

            location = None
            if location_id:
                from django_ledger.models.vi_tri_kho_hang import ViTriKhoHangModel
                location = ViTriKhoHangModel.objects.get(uuid=location_id)

            # Queue the recalculation
            queue_result = self.stock_recalc_queue_service.queue_stock_recalculation(
                warehouse_uuid=str(warehouse.uuid),
                product_uuid=str(product.uuid),
                from_date=from_date,
                lot_uuid=lot_id,
                location_uuid=location_id,
                priority=1,  # High priority
                reason=f"Stock balance recalculation for {product} in {warehouse} from {from_date}"
            )

            return {
                'success': True,
                'processing_method': 'queue',
                'queue_item_uuid': queue_result.get('queue_item_uuid'),
                'estimated_processing_time': '5-15 minutes',
                'warehouse': warehouse.ten_kho,
                'product': str(product),
                'from_date': from_date
            }

        except Exception as e:
            logger.error(f"Error queuing stock recalculation: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processing_method': 'queue_error'
            }

    @transaction.atomic
    def _immediate_recalculation(
        self,
        warehouse: KhoHangModel,
        product: VatTuModel,
        from_date: date,
        lot_id: str = None,
        location_id: str = None
    ) -> Dict[str, Any]:
        """Immediate recalculation for small datasets."""
        try:
            # Delete existing audit records from the recalculation date
            filters = {
                'ma_kho': warehouse,
                'ma_vt': product,
                'timestamp__date__gte': from_date
            }

            if lot_id:
                filters['ma_lo__uuid'] = lot_id

            if location_id:
                filters['ma_vi_tri__uuid'] = location_id

            deleted_count = WarehouseStockAuditModel.objects.filter(**filters).delete()[0]

            # Get stock transactions to recalculate
            txn_filters = {
                'ma_kho': warehouse,
                'ma_vt': product,
                'created__date__gte': from_date
            }

            if lot_id:
                txn_filters['ma_lo__uuid'] = lot_id

            if location_id:
                txn_filters['ma_vi_tri__uuid'] = location_id

            transactions = StockTransactionModel.objects.filter(
                **txn_filters
            ).order_by('created')

            # Get starting balance (latest audit before from_date)
            balance_filters = {
                'ma_kho': warehouse,
                'ma_vt': product,
                'timestamp__date__lt': from_date
            }

            if lot_id:
                balance_filters['ma_lo__uuid'] = lot_id

            if location_id:
                balance_filters['ma_vi_tri__uuid'] = location_id

            starting_audit = WarehouseStockAuditModel.objects.filter(
                **balance_filters
            ).order_by('-timestamp').first()

            if starting_audit:
                running_balance = starting_audit.balance_after
                running_value = starting_audit.value_after
                running_unit_cost = starting_audit.unit_cost_after
            else:
                running_balance = Decimal('0')
                running_value = Decimal('0')
                running_unit_cost = Decimal('0')

            # Recalculate audit records
            audit_records_created = 0
            for txn in transactions:
                # Calculate changes
                change_quantity = txn.net_quantity
                change_value = (
                    txn.tien_nhap if txn.sl_nhap > 0
                    else -txn.tien_xuat
                )

                # Calculate new balances
                balance_before = running_balance
                value_before = running_value
                unit_cost_before = running_unit_cost

                running_balance += change_quantity
                running_value += change_value

                if running_balance > 0:
                    running_unit_cost = running_value / running_balance
                else:
                    running_unit_cost = Decimal('0')

                # Create audit record
                WarehouseStockAuditModel.objects.create(
                    entity_model=warehouse.entity_model,
                    stock_transaction=txn,
                    ma_kho=warehouse,
                    ma_vt=product,
                    ma_lo=txn.ma_lo,
                    ma_vi_tri=txn.ma_vi_tri,
                    balance_before=balance_before,
                    balance_after=running_balance,
                    change_quantity=change_quantity,
                    change_type=txn.loai_giao_dich,
                    value_before=value_before,
                    value_after=running_value,
                    change_value=change_value,
                    unit_cost_before=unit_cost_before,
                    unit_cost_after=running_unit_cost,
                    timestamp=txn.journal_entry.timestamp or txn.created,
                    is_recalculated=True,
                    recalc_reason=f"Immediate recalculation from {from_date}"
                )
                audit_records_created += 1

            logger.info(
                f"Immediate recalculation completed: {audit_records_created} audit records created, "
                f"{deleted_count} old records deleted"
            )

            return {
                'success': True,
                'processing_method': 'immediate',
                'audit_records_created': audit_records_created,
                'old_records_deleted': deleted_count,
                'final_balance': running_balance,
                'final_value': running_value,
                'final_unit_cost': running_unit_cost,
                'warehouse': warehouse.ten_kho,
                'product': str(product),
                'from_date': from_date
            }

        except Exception as e:
            logger.error(f"Error in immediate stock recalculation: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processing_method': 'immediate_error'
            }

    def get_opening_balances_with_filters(
        self, entity_slug: str, filters: Dict[str, Any], start_date: date
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get opening balances for inventory reporting with comprehensive filtering.

        This method implements enterprise-grade opening balance calculation
        for inventory reports with proper filtering capabilities.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters including warehouse, material, etc.
        start_date : date
            Get balances before this date

        Returns
        -------
        Dict[str, Dict[str, Any]]
            Opening balances keyed by warehouse_material combination
        """
        try:
            # Get entity
            from django_ledger.models import EntityModel
            entity = EntityModel.objects.get(slug=entity_slug)

            opening_balances = {}

            # Get the latest audit records before start_date for each warehouse-material combination
            audit_queryset = WarehouseStockAuditModel.objects.filter(
                entity_model=entity,
                timestamp__date__lt=start_date
            ).select_related('ma_kho', 'ma_vt')

            # Apply same filters as stock transactions
            if filters.get('ma_kho'):
                warehouse_uuids = filters['ma_kho']
                if isinstance(warehouse_uuids, list) and warehouse_uuids:
                    audit_queryset = audit_queryset.filter(ma_kho__uuid__in=warehouse_uuids)

            if filters.get('ma_vt'):
                material_uuid = filters['ma_vt']
                if material_uuid:
                    audit_queryset = audit_queryset.filter(ma_vt__uuid=material_uuid)

            # Get latest audit record for each warehouse-material combination
            # Use dictionary approach since DISTINCT ON is not supported by all database backends
            latest_audits = {}

            for audit in audit_queryset.order_by('-timestamp'):
                key = f"{audit.ma_kho.uuid}_{audit.ma_vt.uuid}"
                if key not in latest_audits:
                    latest_audits[key] = audit

            # Process the latest audits
            for audit in latest_audits.values():
                key = f"{audit.ma_kho.ma_kho}_{audit.ma_vt.ma_vt}"
                opening_balances[key] = {
                    'ton_dau': audit.balance_after or Decimal('0'),
                    'du_dau': audit.value_after or Decimal('0'),
                    'ma_kho': audit.ma_kho.ma_kho,
                    'ten_kho': audit.ma_kho.ten_kho,
                    'ma_vt': audit.ma_vt.ma_vt,
                    'ten_vt': audit.ma_vt.ten_vt,
                    'dvt': audit.ma_vt.dvt or '',
                }

            return opening_balances

        except Exception as e:
            logger.error(f"Error getting opening balances with filters: {str(e)}", exc_info=True)
            raise Exception(f"Failed to calculate opening balances for inventory report: {str(e)}")

    def get_current_balances_with_filters(
        self, entity_slug: str, filters: Dict[str, Any], report_date: date
    ) -> List[Dict[str, Any]]:
        """
        Get current balances for inventory reporting with comprehensive filtering.

        This method implements enterprise-grade current balance calculation
        for inventory reports with proper filtering capabilities.
        Following the same pattern as get_opening_balances_with_filters.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters including warehouse, material, etc.
        report_date : date
            Get balances as of this date

        Returns
        -------
        List[Dict[str, Any]]
            Current balances for all warehouse-material combinations
        """
        try:
            # Get entity
            from django_ledger.models import EntityModel
            entity = EntityModel.objects.get(slug=entity_slug)

            current_balances = []

            # Get the latest audit records as of report_date for each warehouse-material combination
            audit_queryset = WarehouseStockAuditModel.objects.filter(
                entity_model=entity,
                timestamp__date__lte=report_date
            ).select_related('ma_kho', 'ma_vt')

            # Apply filters
            if filters.get('ma_kho'):
                audit_queryset = audit_queryset.filter(ma_kho__uuid=filters['ma_kho'])

            if filters.get('ma_vt'):
                audit_queryset = audit_queryset.filter(ma_vt__uuid=filters['ma_vt'])

            if filters.get('ma_lvt'):
                audit_queryset = audit_queryset.filter(ma_vt__ma_lvt__uuid=filters['ma_lvt'])

            if filters.get('nh_vt1'):
                audit_queryset = audit_queryset.filter(ma_vt__nh_vt1__uuid=filters['nh_vt1'])

            if filters.get('nh_vt2'):
                audit_queryset = audit_queryset.filter(ma_vt__nh_vt2__uuid=filters['nh_vt2'])

            if filters.get('nh_vt3'):
                audit_queryset = audit_queryset.filter(ma_vt__nh_vt3__uuid=filters['nh_vt3'])

            if filters.get('dvt') and len(filters['dvt']) > 0:
                audit_queryset = audit_queryset.filter(ma_vt__dvt__uuid__in=filters['dvt'])

            if filters.get('ton_kho_yn', True):
                audit_queryset = audit_queryset.filter(ma_vt__ton_kho_yn=True)

            # Get latest audit record for each warehouse-material combination
            latest_audits = {}

            for audit in audit_queryset.order_by('-timestamp'):
                key = f"{audit.ma_kho.uuid}_{audit.ma_vt.uuid}"
                if key not in latest_audits:
                    latest_audits[key] = audit

            # Process the latest audits and filter out zero balances
            for audit in latest_audits.values():
                balance_after = audit.balance_after or Decimal('0')
                value_after = audit.value_after or Decimal('0')

                # Only include items with non-zero balances
                if balance_after != 0 or value_after != 0:
                    current_balances.append({
                        'ma_kho': audit.ma_kho.ma_kho,
                        'ma_vt': audit.ma_vt.ma_vt,
                        'nhom': audit.ma_vt.ma_lvt.ten_lvt if audit.ma_vt.ma_lvt else '',
                        'dvt': audit.ma_vt.dvt.ten_dvt if audit.ma_vt.dvt else '',
                        'ton_cuoi': balance_after,
                        'du_cuoi': value_after,
                        'ten_vt': audit.ma_vt.ten_vt,
                        'ten_kho': audit.ma_kho.ten_kho,
                        # Add grouping fields for group_by functionality (following tong_hop_nhap_xuat_ton pattern)
                        'ma_vt__ma_lvt': str(audit.ma_vt.ma_lvt.uuid) if audit.ma_vt.ma_lvt else '',
                        'ma_vt__nh_vt1': str(audit.ma_vt.nh_vt1.uuid) if audit.ma_vt.nh_vt1 else '',
                        'ma_vt__nh_vt2': str(audit.ma_vt.nh_vt2.uuid) if audit.ma_vt.nh_vt2 else '',
                        'ma_vt__nh_vt3': str(audit.ma_vt.nh_vt3.uuid) if audit.ma_vt.nh_vt3 else '',
                        'ma_vt__tk_gv': str(audit.ma_vt.ma_lvt.tk_vt.uuid) if audit.ma_vt.ma_lvt and audit.ma_vt.ma_lvt.tk_vt else '',
                    })

            return current_balances

        except Exception as e:
            logger.error(f"Error getting current balances with filters: {str(e)}", exc_info=True)
            # Return empty list instead of raising exception for consistency with other services
            return []
