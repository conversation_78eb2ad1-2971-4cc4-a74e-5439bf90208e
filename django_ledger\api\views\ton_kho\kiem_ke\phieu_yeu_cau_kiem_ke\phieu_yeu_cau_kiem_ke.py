"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuYeuCauKiemKe (Inventory Check Request) view implementation.
"""

from rest_framework import permissions, status
from rest_framework.response import Response

from django_ledger.api.decorators import api_exception_handler
from django_ledger.api.serializers.ton_kho.kiem_ke.phieu_yeu_cau_kiem_ke import (
    PhieuYeuCauKiemKeModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: E402
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: E402
from django_ledger.services.ton_kho.kiem_ke.phieu_yeu_cau_kiem_ke import (  # noqa: E402
    PhieuYeuCauKiemKeService,
)


class PhieuYeuCauKiemKeViewSet(EntityRelatedViewSet):
    """
    ViewSet for PhieuYeuCauKiemKeModel.
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'pk'

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = PhieuYeuCauKiemKeService()

    def get_serializer_context(self):  # noqa: C901
        """
        Returns the serializer context.
        """
        return {
            'request': self.request,
            'entity_slug': self.kwargs['entity_slug'],
        }

    def get_serializer(self, *args, **kwargs):  # noqa: C901
        """
        Returns the serializer instance.
        """
        kwargs['context'] = self.get_serializer_context()
        return PhieuYeuCauKiemKeModelSerializer(*args, **kwargs)

    @api_exception_handler
    def list(self, request, entity_slug=None):  # noqa: F811,
        """
        Lists PhieuYeuCauKiemKeModel instances for a specific entity.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        """
        queryset = self.service.list(entity_slug=entity_slug)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @api_exception_handler
    def retrieve(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Retrieves a PhieuYeuCauKiemKeModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        pk : str
            The UUID of the PhieuYeuCauKiemKeModel to retrieve.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {'detail': 'PhieuYeuCauKiemKe not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, entity_slug=None):  # noqa: F811,
        """
        Creates a new PhieuYeuCauKiemKeModel instance.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            # Create the PhieuYeuCauKiemKeModel instance
            instance = self.service.create(
                entity_slug=entity_slug, data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(
                entity_slug=entity_slug, uuid=instance.uuid
            )
            serializer = self.get_serializer(instance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def update(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Updates an existing PhieuYeuCauKiemKeModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {'detail': 'PhieuYeuCauKiemKe not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            # Update the PhieuYeuCauKiemKeModel instance
            instance = self.service.update(
                entity_slug=entity_slug, uuid=pk, data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(
                entity_slug=entity_slug, uuid=pk
            )
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        return Response(
            serializer.errors, status=status.HTTP_400_BAD_REQUEST
        )

    @api_exception_handler
    def partial_update(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Partially updates an existing PhieuYeuCauKiemKeModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {'detail': 'PhieuYeuCauKiemKe not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(
            instance, data=request.data, partial=True
        )
        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            # Update the PhieuYeuCauKiemKeModel instance
            instance = self.service.update(
                entity_slug=entity_slug, uuid=pk, data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(
                entity_slug=entity_slug, uuid=pk
            )
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        return Response(
            serializer.errors, status=status.HTTP_400_BAD_REQUEST
        )

    @api_exception_handler
    def destroy(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Deletes a PhieuYeuCauKiemKeModel instance.
        """
        success = self.service.delete(entity_slug=entity_slug, uuid=pk)
        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)
        return Response(
            {'detail': 'PhieuYeuCauKiemKe not found.'},
            status=status.HTTP_404_NOT_FOUND,
        )
