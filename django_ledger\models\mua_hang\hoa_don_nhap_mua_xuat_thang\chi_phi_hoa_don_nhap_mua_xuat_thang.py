"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiPhiHoaDonNhapMuaXuatThangModel, which represents the costs
associated with a HoaDonNhapMuaXuatThang (Invoice Import/Purchase/Export Monthly).
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiPhiHoaDonNhapMuaXuatThangModelQueryset(models.QuerySet):
    """
    A custom defined ChiPhiHoaDonNhapMuaXuatThangModel QuerySet.
    """

    def for_invoice(self, hoa_don_uuid):  # noqa: C901
        """
        Filter by invoice UUID.
        """
        return self.filter(hoa_don__uuid=hoa_don_uuid)


class ChiPhiHoaDonNhapMuaXuatThangModelManager(models.Manager):
    """
    A custom defined ChiPhiHoaDonNhapMuaXuatThangModel Manager that will act as an interface to handle the  # noqa: E501
    ChiPhiHoaDonNhapMuaXuatThangModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiPhiHoaDonNhapMuaXuatThangModelQueryset.
        """
        return ChiPhiHoaDonNhapMuaXuatThangModelQueryset(self.model, using=self._db)


class ChiPhiHoaDonNhapMuaXuatThangModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiPhiHoaDonNhapMuaXuatThangModel database will inherit from.  # noqa: E501
    The ChiPhiHoaDonNhapMuaXuatThangModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    hoa_don : ForeignKey
        Reference to the main HoaDonNhapMuaXuatThang.
    id_goc : IntegerField
        Original ID.
    line : IntegerField
        Line number.
    ma_cp : ForeignKey
        Cost code.
    tien_cp_nt : DecimalField
        Cost amount in foreign currency.
    tien_cp : DecimalField
        Cost amount.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)

    # Link to main invoice
    hoa_don = models.ForeignKey(
        'django_ledger.HoaDonNhapMuaXuatThangModel',
        on_delete=models.CASCADE,
        related_name='chi_phi',
        verbose_name=_('Hóa đơn'),
        help_text=_('Main invoice reference'),
    )

    # Line information
    id_goc = models.IntegerField(
        verbose_name=_('ID gốc'),
        help_text=_('Original ID'),
        null=True,
        blank=True,
    )
    line = models.IntegerField(
        verbose_name=_('Số dòng'),
        help_text=_('Line number'),
    )

    # Cost information
    ma_cp = models.ForeignKey(
        'django_ledger.ChiPhiModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã chi phí'),
        help_text=_('Cost code'),
        related_name='chi_phi_hoa_don_nhap_mua_xuat_thang',
        null=True,
        blank=True,
    )
    tien_cp_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền chi phí ngoại tệ'),
        help_text=_('Cost amount in foreign currency'),
    )
    tien_cp = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền chi phí'),
        help_text=_('Cost amount'),
    )

    objects = ChiPhiHoaDonNhapMuaXuatThangModelManager.from_queryset(
        ChiPhiHoaDonNhapMuaXuatThangModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi Phí Hóa Đơn Nhập Mua Xuất Tháng')
        verbose_name_plural = _('Chi Phí Hóa Đơn Nhập Mua Xuất Tháng')
        ordering = ['line']
        indexes = [
            models.Index(fields=['hoa_don']),
            models.Index(fields=['ma_cp']),
            models.Index(fields=['line']),
            models.Index(fields=['created']),
            models.Index(fields=['updated']),
        ]

    def __str__(self):
        return f"{self.hoa_don.so_ct} - Chi phí {self.ma_cp}"


class ChiPhiHoaDonNhapMuaXuatThangModel(ChiPhiHoaDonNhapMuaXuatThangModelAbstract):
    """
    Base ChiPhiHoaDonNhapMuaXuatThangModel from Abstract.
    """

    class Meta(ChiPhiHoaDonNhapMuaXuatThangModelAbstract.Meta):
        abstract = False
        db_table = "chi_phi_hoa_don_nhap_mua_xuat_thang"
