"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

HoaDonNhapMuaXuatThang URL patterns.
"""

from django.urls import include, path  # noqa: F401
from rest_framework.routers import DefaultRouter  # noqa: F401

from django_ledger.api.views.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangViewSet,
    ChiPhiHoaDonNhapMuaXuatThangViewSet,
    ChiTietHoaDonNhapMuaXuatThangViewSet,
    HoaDonNhapMuaXuatThangViewSet,
    ThueHoaDonNhapMuaXuatThangViewSet,
)

# Create router for HoaDonNhapMuaXuatThang API
router = DefaultRouter()

# Register main invoice viewset
router.register(
    r'hoa-don-nhap-mua-xuat-thang',
    HoaDonNhapMuaXuatThangViewSet,
    basename='hoa-don-nhap-mua-xuat-thang',
)

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for hoa-don-nhap-mua-xuat-thang
    path(
        "hoa-don-nhap-mua-xuat-thang/<uuid:hoa_don_uuid>/",
        include(
            [
                # Chi tiet hoa don routes
                path(
                    "chi-tiet/",
                    ChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-hoa-don-nhap-mua-xuat-thang-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-hoa-don-nhap-mua-xuat-thang-detail",
                ),
                # Chi tiet actions
                path(
                    "chi-tiet/<uuid:uuid>/calculate-totals/",
                    ChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"post": "calculate_totals"}
                    ),
                    name="chi-tiet-hoa-don-nhap-mua-xuat-thang-calculate-totals",
                ),
                path(
                    "chi-tiet/by-material/",
                    ChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"get": "by_material"}
                    ),
                    name="chi-tiet-hoa-don-nhap-mua-xuat-thang-by-material",
                ),
                path(
                    "chi-tiet/by-warehouse/",
                    ChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"get": "by_warehouse"}
                    ),
                    name="chi-tiet-hoa-don-nhap-mua-xuat-thang-by-warehouse",
                ),
                path(
                    "chi-tiet/totals/",
                    ChiTietHoaDonNhapMuaXuatThangViewSet.as_view({"get": "totals"}),
                    name="chi-tiet-hoa-don-nhap-mua-xuat-thang-totals",
                ),
                # Chi phi hoa don routes
                path(
                    "chi-phi/",
                    ChiPhiHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-phi-hoa-don-nhap-mua-xuat-thang-list",
                ),
                path(
                    "chi-phi/<uuid:uuid>/",
                    ChiPhiHoaDonNhapMuaXuatThangViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-phi-hoa-don-nhap-mua-xuat-thang-detail",
                ),
                path(
                    "chi-phi/totals/",
                    ChiPhiHoaDonNhapMuaXuatThangViewSet.as_view({"get": "totals"}),
                    name="chi-phi-hoa-don-nhap-mua-xuat-thang-totals",
                ),
                # Chi phi chi tiet routes
                path(
                    "chi-phi-vat-tu/",
                    ChiPhiChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-phi-chi-tiet-hoa-don-nhap-mua-xuat-thang-list",
                ),
                path(
                    "chi-phi-vat-tu/<uuid:uuid>/",
                    ChiPhiChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-phi-chi-tiet-hoa-don-nhap-mua-xuat-thang-detail",
                ),
                path(
                    "chi-phi-vat-tu/by-material/",
                    ChiPhiChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"get": "by_material"}
                    ),
                    name="chi-phi-chi-tiet-hoa-don-nhap-mua-xuat-thang-by-material",
                ),
                path(
                    "chi-phi-vat-tu/totals/",
                    ChiPhiChiTietHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"get": "totals"}
                    ),
                    name="chi-phi-chi-tiet-hoa-don-nhap-mua-xuat-thang-totals",
                ),
                # Thue hoa don routes
                path(
                    "thue/",
                    ThueHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="thue-hoa-don-nhap-mua-xuat-thang-list",
                ),
                path(
                    "thue/<uuid:uuid>/",
                    ThueHoaDonNhapMuaXuatThangViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="thue-hoa-don-nhap-mua-xuat-thang-detail",
                ),
                # Thue actions
                path(
                    "thue/<uuid:uuid>/calculate-tax/",
                    ThueHoaDonNhapMuaXuatThangViewSet.as_view(
                        {"post": "calculate_tax"}
                    ),
                    name="thue-hoa-don-nhap-mua-xuat-thang-calculate-tax",
                ),
                path(
                    "thue/by-tax-code/",
                    ThueHoaDonNhapMuaXuatThangViewSet.as_view({"get": "by_tax_code"}),
                    name="thue-hoa-don-nhap-mua-xuat-thang-by-tax-code",
                ),
                path(
                    "thue/totals/",
                    ThueHoaDonNhapMuaXuatThangViewSet.as_view({"get": "totals"}),
                    name="thue-hoa-don-nhap-mua-xuat-thang-totals",
                ),
            ]
        ),
    ),
    # Additional invoice-level actions
    path(
        "hoa-don-nhap-mua-xuat-thang/<uuid:uuid>/calculate-totals/",
        HoaDonNhapMuaXuatThangViewSet.as_view({"post": "calculate_totals"}),
        name="hoa-don-nhap-mua-xuat-thang-calculate-totals",
    ),
    path(
        "hoa-don-nhap-mua-xuat-thang/<uuid:uuid>/summary/",
        HoaDonNhapMuaXuatThangViewSet.as_view({"get": "summary"}),
        name="hoa-don-nhap-mua-xuat-thang-summary",
    ),
    path(
        "hoa-don-nhap-mua-xuat-thang/statistics/",
        HoaDonNhapMuaXuatThangViewSet.as_view({"get": "statistics"}),
        name="hoa-don-nhap-mua-xuat-thang-statistics",
    ),
    path(
        "hoa-don-nhap-mua-xuat-thang/by-customer/",
        HoaDonNhapMuaXuatThangViewSet.as_view({"get": "by_customer"}),
        name="hoa-don-nhap-mua-xuat-thang-by-customer",
    ),
    path(
        "hoa-don-nhap-mua-xuat-thang/by-date-range/",
        HoaDonNhapMuaXuatThangViewSet.as_view({"get": "by_date_range"}),
        name="hoa-don-nhap-mua-xuat-thang-by-date-range",
    ),
]
