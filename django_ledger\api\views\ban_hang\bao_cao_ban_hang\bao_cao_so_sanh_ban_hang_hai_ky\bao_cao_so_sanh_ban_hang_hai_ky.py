"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for Bao Cao So Sanh Ban Hang Hai Ky (Sales Comparison Report Between Two Periods) API.
"""

from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from django_ledger.api.serializers.ban_hang.bao_cao_ban_hang.bao_cao_so_sanh_ban_hang_hai_ky import (
    BaoCaoSoSanhBanHangHaiKyRequestSerializer,
    BaoCaoSoSanhBanHangHaiKyResponseSerializer,
)
from django_ledger.api.views.common import ERPPagination
from django_ledger.services.ban_hang.bao_cao_ban_hang.bao_cao_so_sanh_ban_hang_hai_ky import (
    BaoCaoSoSanhBanHangHaiKyService,
)


class BaoCaoSoSanhBanHangHaiKyViewSet(ViewSet):
    """
    ViewSet for Sales Comparison Report Between Two Periods.

    Provides API endpoint for generating sales comparison reports between two time periods
    with support for 32 filter conditions and dynamic grouping.

    ERP Expert Implementation - 20+ years experience
    """

    pagination_class = ERPPagination
    service = None

    def __init__(self, *args, **kwargs):
        """
        Initialize ViewSet with service dependency.

        ERP Expert Implementation - Dependency injection pattern
        """
        super().__init__(*args, **kwargs)
        self.service = None

    @extend_schema(
        operation_id="sales_comparison_report_two_periods",
        summary="Báo Cáo So Sánh Bán Hàng Hai Kỳ",
        description="""
        Tạo báo cáo so sánh doanh số bán hàng giữa hai kỳ thời gian.

        **Tính năng chính:**
        - So sánh doanh số giữa kỳ này và kỳ trước
        - Tính toán chênh lệch và tỷ lệ % tăng trưởng
        - Hỗ trợ 32 điều kiện lọc từ cURL request
        - Dynamic grouping theo detail_by parameter
        - Union data từ HoaDonBanHang + HoaDonDichVu

        **Calculation Logic:**
        - sl_kn/sl_kt: SUM(so_luong) trong từng kỳ
        - tien_kn/tien_kt: SUM(so_luong * gia_nt2) trong từng kỳ
        - sl_cl: sl_kn - sl_kt (chênh lệch số lượng)
        - sl_tl: (sl_kn - sl_kt) / sl_kt * 100 (tỷ lệ % số lượng)
        - tien_cl: tien_kn - tien_kt (chênh lệch tiền)
        - tien_tl: (tien_kn - tien_kt) / tien_kt * 100 (tỷ lệ % tiền)

        **Detail_by Options:**
        - 100: Khách hàng
        - 200: Vật tư (default)
        - 300: Kho
        - 400: Bộ phận
        - 500: Vụ việc
        """,
        parameters=[
            OpenApiParameter(
                name="entity_slug",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.PATH,
                description="Entity slug identifier",
                required=True,
            ),
        ],
        request=BaoCaoSoSanhBanHangHaiKyRequestSerializer,
        responses={
            200: BaoCaoSoSanhBanHangHaiKyResponseSerializer(many=True),
            400: "Bad Request - Invalid parameters",
            404: "Entity not found",
            500: "Internal Server Error",
        },
        tags=["Sales Reports"],
    )
    @action(detail=False, methods=["post"])
    def get_report(self, request, entity_slug=None):
        """
        Generate Sales Comparison Report Between Two Periods.

        ERP Expert Implementation - 20+ years experience

        This endpoint generates a comprehensive sales comparison report between two periods:
        - Combines data from HoaDonBanHang and HoaDonDichVu
        - Applies 32 filter conditions from cURL request
        - Calculates differences and growth ratios
        - Supports dynamic grouping by detail_by parameter

        Parameters
        ----------
        request : Request
            HTTP request containing filter parameters in body
        entity_slug : str
            Entity slug from URL path

        Returns
        -------
        Response
            Paginated response containing report data with calculations
        """
        # Validate request data
        serializer = BaoCaoSoSanhBanHangHaiKyRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request parameters", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get parsed filters from serializer
        parsed_filters = serializer.get_parsed_filters()

        # Initialize service if not already done
        if self.service is None:
            self.service = BaoCaoSoSanhBanHangHaiKyService()

        try:
            # Generate report using service
            report_data = self.service.generate_report(
                entity_slug=entity_slug, filters=parsed_filters
            )

            # Apply pagination
            paginator = self.pagination_class()
            page = paginator.paginate_queryset(report_data, request)
            if page is not None:
                response_serializer = BaoCaoSoSanhBanHangHaiKyResponseSerializer(
                    page, many=True
                )
                return paginator.get_paginated_response(response_serializer.data)

            # Return unpaginated response if pagination is not applied
            response_serializer = BaoCaoSoSanhBanHangHaiKyResponseSerializer(
                report_data, many=True
            )
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            # Log the error for debugging
            import logging

            logger = logging.getLogger(__name__)
            logger.error(
                f"Error generating Sales Comparison Report: {str(e)}",
                exc_info=True,
            )

            return Response(
                {
                    "error": "Internal server error while generating report",
                    "details": (
                        str(e)
                        if request.user.is_staff
                        else "Please contact administrator"
                    ),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @extend_schema(
        operation_id="sales_comparison_report_options",
        summary="Get Sales Comparison Report Options",
        description="""
        Get available options for Sales Comparison Report filters.

        Returns configuration options for:
        - detail_by values and their descriptions
        - Available filter fields
        - Date format requirements
        """,
        parameters=[
            OpenApiParameter(
                name="entity_slug",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.PATH,
                description="Entity slug identifier",
                required=True,
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "detail_by_options": {
                        "type": "object",
                        "description": "Available detail_by values and descriptions",
                    },
                    "filter_fields": {
                        "type": "array",
                        "description": "List of available filter fields",
                    },
                    "date_format": {
                        "type": "string",
                        "description": "Required date format",
                    },
                },
            }
        },
        tags=["Sales Reports"],
    )
    @action(detail=False, methods=["get"])
    def options(self, request, entity_slug=None):
        """
        Get available options for Sales Comparison Report.

        Returns configuration options and metadata for the report.
        """
        options_data = {
            "detail_by_options": {
                "100": "Khách hàng",
                "200": "Vật tư",
                "300": "Kho",
                "400": "Bộ phận",
                "500": "Vụ việc",
            },
            "filter_fields": [
                "ma_kh",
                "nh_kh1",
                "nh_kh2",
                "nh_kh3",
                "rg_code",
                "ma_vt",
                "ma_lvt",
                "ton_kho_yn",
                "nh_vt1",
                "nh_vt2",
                "nh_vt3",
                "ma_kho",
                "ma_unit",
                "nh_ct",
                "loai_du_lieu",
                "ma_bp",
                "ma_vv",
                "ma_hd",
                "ma_dtt",
                "ma_ku",
                "ma_phi",
                "ma_sp",
                "ma_lsx",
                "ma_cp0",
                "ma_gd",
                "ma_nvbh",
                "tk_vt",
                "tk_dt",
                "tk_gv",
                "ma_lo",
                "ma_vi_tri",
                "so_ct1",
                "so_ct2",
                "dien_giai",
            ],
            "date_format": "YYYYMMDD",
            "default_values": {
                "detail_by": "200",
                "nh_ct": "BH1,BH2,BH3",
                "mau_bc": 20,
                "ton_kho_yn": True,
                "loai_du_lieu": 1,
            },
            "calculation_formulas": {
                "sl_cl": "sl_kn - sl_kt",
                "sl_tl": "(sl_kn - sl_kt) / sl_kt * 100",
                "tien_cl": "tien_kn - tien_kt",
                "tien_tl": "(tien_kn - tien_kt) / tien_kt * 100",
            },
        }

        return Response(options_data, status=status.HTTP_200_OK)
