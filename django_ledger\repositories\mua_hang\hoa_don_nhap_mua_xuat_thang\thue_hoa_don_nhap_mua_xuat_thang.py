"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ThueHoaDonNhapMuaXuatThang Repository implementation.
"""

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ThueHoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401


class ThueHoaDonNhapMuaXuatThangRepository(BaseRepository):
    """
    Repository class for ThueHoaDonNhapMuaXuatThangModel.
    Handles data access operations for invoice taxes.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the ThueHoaDonNhapMuaXuatThangModel.
        """
        super().__init__(model_class=ThueHoaDonNhapMuaXuatThangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for ThueHoaDonNhapMuaXuatThangModel with optimized relationships.

        Returns
        -------
        QuerySet
            The base queryset for ThueHoaDonNhapMuaXuatThangModel with related objects.
        """
        return self.model_class.objects.all().select_related(
            "hoa_don",
            "ma_thue",
            "ma_kh",
            "tk_thue_no",
            "tk_du",
            "ma_kh9",
            "ma_tt",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
        )

    def get_by_invoice(self, hoa_don_uuid) -> QuerySet:  # noqa: C901
        """
        Get ThueHoaDonNhapMuaXuatThangModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ThueHoaDonNhapMuaXuatThangModel instances for the specified invoice.
        """
        return self.get_queryset().filter(hoa_don__uuid=hoa_don_uuid)

    def get_by_id(
        self, uuid, hoa_don_uuid=None
    ) -> ThueHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Get a ThueHoaDonNhapMuaXuatThangModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ThueHoaDonNhapMuaXuatThangModel to retrieve.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ThueHoaDonNhapMuaXuatThangModel
            The retrieved ThueHoaDonNhapMuaXuatThangModel instance.

        Raises
        ------
        ObjectDoesNotExist
            If the ThueHoaDonNhapMuaXuatThangModel with the given UUID does not exist.
        """
        qs = self.get_queryset()
        if hoa_don_uuid:
            qs = self.get_by_invoice(hoa_don_uuid)
        return qs.get(uuid=uuid)

    def create(self, **kwargs) -> ThueHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ThueHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        **kwargs : dict
            The data to create the ThueHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ThueHoaDonNhapMuaXuatThangModel
            The created ThueHoaDonNhapMuaXuatThangModel instance.
        """
        return self.model_class.objects.create(**kwargs)

    def update(
        self, instance: ThueHoaDonNhapMuaXuatThangModel, **kwargs
    ) -> ThueHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ThueHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        instance : ThueHoaDonNhapMuaXuatThangModel
            The instance to update.
        **kwargs : dict
            The data to update the instance with.

        Returns
        -------
        ThueHoaDonNhapMuaXuatThangModel
            The updated ThueHoaDonNhapMuaXuatThangModel instance.
        """
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(self, instance: ThueHoaDonNhapMuaXuatThangModel) -> None:  # noqa: C901
        """
        Delete a ThueHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        instance : ThueHoaDonNhapMuaXuatThangModel
            The instance to delete.
        """
        instance.delete()

    def filter_by_tax_code(self, tax_uuid, hoa_don_uuid=None) -> QuerySet:  # noqa: C901
        """
        Filter ThueHoaDonNhapMuaXuatThangModel instances by tax code.

        Parameters
        ----------
        tax_uuid : UUID
            The UUID of the tax to filter by.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        QuerySet
            A queryset of ThueHoaDonNhapMuaXuatThangModel instances for the specified tax.
        """
        qs = self.get_queryset()
        if hoa_don_uuid:
            qs = self.get_by_invoice(hoa_don_uuid)
        return qs.filter(ma_thue__uuid=tax_uuid)

    def get_total_by_invoice(self, hoa_don_uuid) -> dict:  # noqa: C901
        """
        Get total tax amounts for ThueHoaDonNhapMuaXuatThangModel instances by invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        dict
            A dictionary containing total tax amounts.
        """
        from django.db.models import Sum

        qs = self.get_by_invoice(hoa_don_uuid)
        return qs.aggregate(
            total_amount=Sum('t_tien'),
            total_amount_nt=Sum('t_tien_nt'),
            total_tax=Sum('t_thue'),
            total_tax_nt=Sum('t_thue_nt'),
        )
