# Hướng Dẫn Triển Khai Unified Accounting Service - Chuẩn ERP

**Hướng Dẫn ERP Expert - Dựa Trên Chuẩn HoaDonBanHangService**
**Ngày**: 2024-12-30 (Cậ<PERSON> nhật với Flexible Account Sources)
**M<PERSON><PERSON> đích**: Hướng dẫn developers triển khai unified accounting theo chuẩn đã được tối ưu hóa

## 📋 Tổng Quan

Dựa trên implementation chuẩn trong `HoaDonBanHangService`, `PhieuThuService`, và `PhieuChiService`, đây là pattern được khuyến nghị cho tất cả document services. Pattern này đã được tối ưu hóa để:

- ✅ **Đơn giản hóa**: Không cần quản lý entity_id phức tạp
- ✅ **Linh hoạt**: Mỗi document có business logic riêng với flexible account sources
- ✅ **Hiệu quả**: Tập trung vào business logic thay vì technical complexity
- ✅ **<PERSON><PERSON> bảo trì**: Code sạch, ít boilerplate, dễ hiểu
- ✅ **Flexible Account Sources**: Hỗ trợ lấy tài khoản từ header hoặc detail

## 🎯 Quy Trình Triển Khai Thực Tế (6 Bước Chi Tiết)

### Bước 1: Thêm Mối Quan Hệ 1-1 Với Ledger Trong Model

**📝 Quan trọng**: Trước khi implement service, bạn cần thêm mối quan hệ với LedgerModel vào model chứng từ của mình.

```python
# Trong model chứng từ của bạn (VD: PhieuThuModel, PhieuChiModel, HoaDonMuaHangModel)
from django.db import models
from django.utils.translation import gettext_lazy as _

class ModelChungTuCuaBan(models.Model):
    # ... các field hiện tại của bạn ...

    # ✅ THÊM MỐI QUAN HỆ 1-1 VỚI LEDGER (BẮT BUỘC)
    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho chứng từ này"),
        related_name="ten_model_cua_ban",  # VD: "phieu_thu", "phieu_chi"
    )

    class Meta:
        # ... meta hiện tại ...
        indexes = [
            models.Index(fields=['ledger']),  # ✅ Index cho performance
        ]
```

**📝 Ví dụ cụ thể cho các loại chứng từ:**

```python
# PhieuThuModel
class PhieuThuModel(models.Model):
    # ... existing fields ...

    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho phiếu thu này"),
        related_name="phieu_thu",
    )

# PhieuChiModel
class PhieuChiModel(models.Model):
    # ... existing fields ...

    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho phiếu chi này"),
        related_name="phieu_chi",
    )

# HoaDonMuaHangModel
class HoaDonMuaHangModel(models.Model):
    # ... existing fields ...

    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho hóa đơn mua hàng này"),
        related_name="hoa_don_mua_hang",
    )
```

**📝 Lưu ý quan trọng về Ledger relationship:**

1. **OneToOneField**: Mỗi chứng từ có một sổ cái riêng
2. **on_delete=models.PROTECT**: Bảo vệ không xóa ledger khi còn chứng từ
3. **null=True, blank=True**: Cho phép chứng từ tồn tại mà chưa có ledger
4. **related_name**: Tên unique cho từng loại chứng từ (phải khác nhau)
5. **Index**: Thêm index cho performance khi query

**🔧 Migration cần thiết:**

Sau khi thêm field, chạy migration:

```bash
python manage.py makemigrations
python manage.py migrate
```

**📋 Tham khảo từ các model hiện có:**

- **HoaDonBanHangModel**: `related_name="hoa_don_ban_hang"`
- **PhieuThuModel**: `related_name="phieu_thu"`
- **PhieuChiModel**: `related_name="phieu_chi"`

### Bước 2: Nghiên Cứu Chứng Từ Trên SafeBook

**🔍 Truy cập SafeBook để phân tích:**

1. Truy cập: https://safebooks5.arito.vn:44350/app?id=appdrawer
2. Chọn vào phiếu, giấy, hóa đơn cần hạch toán
3. Phân tích cấu trúc và quy tắc nghiệp vụ

**📊 Những thông tin cần thu thập:**

- **Loại chứng từ** (`ma_ngv`): Xác định loại bút toán cần tạo
- **Trạng thái** (`status`): Xác định khi nào tạo/không tạo bút toán
- **Cấu trúc dữ liệu**: Header-only hay Header-Detail
- **Tài khoản kế toán**: Nằm ở header hay detail
- **Quy tắc nghiệp vụ**: Điều kiện đặc biệt

**💡 Ví dụ phân tích:**

```
PhieuChi:
- ma_ngv = "1": Chi hóa đơn → journal_type = "CHIHD"
- ma_ngv = "2,3,4": Chi nhà cung cấp → journal_type = "CHINCC"
- status = "1": Đã duyệt → canCreate = True
- status = "0": Nháp → canCreate = False
- Cấu trúc: Header + Detail (chi_tiet_data)
- tk (header) = Credit account, tk_no (detail) = Debit account
```

### Bước 3: Phân Tích Quy Tắc Nghiệp Vụ

**📋 Xác định logic tạo bút toán:**

1. **Theo loại chứng từ** (`ma_ngv`):

   ```python
   if ma_ngv == "1":
       journal_type = "CHIHD"  # Chi hóa đơn
   elif ma_ngv in ["2", "3", "4"]:
       journal_type = "CHINCC"  # Chi nhà cung cấp
   ```

2. **Theo trạng thái** (`status`):

   ```python
   if status == "0":
       canCreate = False  # Nháp - không tạo bút toán
   elif status == "1":
       canCreate = True   # Đã duyệt - tạo bút toán
   ```

3. **Theo điều kiện nghiệp vụ**:
   - Có số tiền > 0
   - Có tài khoản hợp lệ
   - Có chi tiết (nếu cần)

## 🚀 Quy Trình Triển Khai Code (Bước 4-6)

### Bước 4: Import và Thiết Lập Service Class

```python
from django_ledger.utils_new.debt_management import CongNoCreation
from typing import List, Dict, Any

class ServiceChungTuCuaBan(BaseService):
    """
    Service cho chứng từ của bạn với tích hợp Unified Accounting.

    Ví dụ: PhieuThuService, PhieuChiService, HoaDonMuaHangService, v.v.
    Thay thế 'ServiceChungTuCuaBan' bằng tên service thực tế của bạn.
    """
```

### Bước 5: Định Nghĩa Cấu Hình Kế Toán

```python
# ✅ CẤU HÌNH ĐỊNH SẴN: Mapping kế toán cho chứng từ của bạn
DOCUMENT_ACCOUNTING_CONFIG = [
    {
        'journal_type': 'LOAI_BUT_TOAN',       # VD: 'CONGNO', 'DT0CK', 'THUE'
        'debit_account_field': 'tk_no',        # Tên field tài khoản nợ - DEBIT
        'credit_account_field': 'tk_co',       # Tên field tài khoản có - CREDIT
        'debit_account_source': 'header',      # 'header' hoặc 'detail' (mặc định 'header')
        'credit_account_source': 'detail',     # 'header' hoặc 'detail' (mặc định 'detail')
        'amount_field': 'so_tien',             # Tên field số tiền
        'detail_source': 'chi_tiet',           # Tên relation đến detail (None nếu chỉ có header)
        'canCreate': True                      # Flag mặc định cho phép tạo bút toán
    }
    # Thêm nhiều mapping khác nếu cần...
]
```

**📝 Giải thích chi tiết từng field:**

- **journal_type**: Loại bút toán kế toán

  - `'CONGNO'`: Bút toán công nợ (phiếu thu, phiếu chi)
  - `'DT0CK'`: Bút toán doanh thu (hóa đơn bán hàng)
  - `'THUE'`: Bút toán thuế (thuế VAT)
  - `'CHIPHI'`: Bút toán chi phí (hóa đơn mua hàng)

- **debit_account_field**: Tên field chứa mã tài khoản nợ

  - Ví dụ: `'tk'`, `'tk_tien'`, `'tk_cp'`
  - Phải là tên field thực tế trong model của bạn

- **credit_account_field**: Tên field chứa mã tài khoản có

  - Có thể ở header hoặc detail tùy thuộc vào cấu trúc chứng từ
  - Ví dụ: `'tk_du'`, `'tk_dt'`, `'tk_thue_co'`

- **amount_field**: Tên field chứa số tiền cần ghi sổ

  - Ví dụ: `'tien'`, `'tien2'`, `'thue'`, `'thanh_tien'`

- **detail_source**: Tên relationship đến bảng chi tiết

  - `None`: Nếu chứng từ chỉ có header (phiếu thu, phiếu chi)
  - `'chi_tiet'`: Nếu chứng từ có cả header và detail (hóa đơn)

- **canCreate**: Flag điều khiển việc tạo bút toán
  - `True`: Mặc định cho phép tạo
  - Sẽ được business logic điều chỉnh dựa trên trạng thái chứng từ

### Bước 5: Khởi Tạo Service Đơn Giản

```python
def __init__(self):
    super().__init__()
    # ... code khởi tạo hiện tại của bạn ...

    # ✅ ĐƠN GIẢN: Khởi tạo service không cần tham số
    # Entity sẽ được tự động lấy từ source_document
    self._cong_no_service = CongNoCreation()
```

**📝 Lưu ý quan trọng:**

- Không cần truyền entity_id vào constructor
- Không cần caching phức tạp
- Service sẽ tự động lấy entity từ document được truyền vào

### Bước 4: Triển Khai Business Logic (BẮT BUỘC)

```python
def _determine_accounting_mappings(self, document) -> List[Dict[str, Any]]:
    """
    ✅ BUSINESS LOGIC: Xác định mapping kế toán dựa trên trạng thái và quy tắc nghiệp vụ.

    PATTERN CHUẨN từ HoaDonBanHangService - TÙY CHỈNH cho chứng từ của bạn!
    """
    # Lấy cấu hình mapping cơ bản
    mappings = self.DOCUMENT_ACCOUNTING_CONFIG.copy()

    # ✅ LOGIC TRẠNG THÁI CHUẨN - TÙY CHỈNH GIÁ TRỊ CHO CHỨNG TỪ CỦA BẠN
    if hasattr(document, 'status'):
        status = getattr(document, 'status', 'gia_tri_mac_dinh_duyet')

        # TODO: Thay thế các giá trị status này bằng giá trị thực tế của chứng từ bạn
        if status in ['gia_tri_nhap', 'gia_tri_cho_duyet']:  # Nháp/Chờ duyệt - KHÔNG tạo bút toán
            for mapping in mappings:
                mapping['canCreate'] = False

        elif status in ['gia_tri_da_duyet', 'gia_tri_xac_nhan']:  # Đã duyệt/Xác nhận - TẠO đầy đủ bút toán
            for mapping in mappings:
                mapping['canCreate'] = True
        else:
            # Trạng thái không xác định - hành vi mặc định
            for mapping in mappings:
                mapping['canCreate'] = True
    else:
        # Không có field status - hành vi mặc định (tương thích ngược)
        for mapping in mappings:
            mapping['canCreate'] = True

    # ✅ QUY TẮC NGHIỆP VỤ BỔ SUNG: Tùy chỉnh ở đây

    # Ví dụ từ HoaDonBanHangService: Bỏ qua bút toán thuế nếu không có thuế
    if hasattr(document, 'chi_tiet'):
        try:
            chi_tiet_list = document.chi_tiet.all()
            has_tax = any(getattr(ct, 'thue', 0) > 0 for ct in chi_tiet_list)

            if not has_tax:
                for mapping in mappings:
                    if mapping['journal_type'] == 'THUE':
                        mapping['canCreate'] = False
        except:
            pass  # Bỏ qua lỗi trong quá trình đánh giá business logic

    # Ví dụ thêm: Bỏ qua nếu số tiền = 0 (cho chứng từ header-only)
    if hasattr(document, 'tien') and getattr(document, 'tien', 0) <= 0:
        for mapping in mappings:
            mapping['canCreate'] = False

    return mappings
```

**📝 Hướng dẫn tùy chỉnh Business Logic:**

1. **Thay thế giá trị status**:

   - `'gia_tri_nhap'` → giá trị thực tế cho trạng thái nháp của chứng từ bạn
   - `'gia_tri_cho_duyet'` → giá trị thực tế cho trạng thái chờ duyệt
   - `'gia_tri_da_duyet'` → giá trị thực tế cho trạng thái đã duyệt
   - `'gia_tri_xac_nhan'` → giá trị thực tế cho trạng thái xác nhận

2. **Thêm quy tắc nghiệp vụ**:
   - Kiểm tra số tiền > 0
   - Kiểm tra tài khoản hợp lệ
   - Kiểm tra điều kiện đặc biệt của chứng từ
   - Tích hợp với workflow phê duyệt

### Bước 6: Triển Khai Các Method Kế Toán Cốt Lõi

```python
def create_debt_entry(self, document) -> bool:
    """
    ✅ CHUẨN: Tạo bút toán kế toán cho chứng từ.

    Pattern từ HoaDonBanHangService - đã được kiểm chứng và tối ưu hóa.
    """
    try:
        # ✅ BUSINESS LOGIC: Xác định mapping dựa trên trạng thái chứng từ
        account_mappings = self._determine_accounting_mappings(document)

        # ✅ ĐƠN GIẢN: Sử dụng service đã khởi tạo sẵn
        return self._cong_no_service.create_document_accounting_entries(
            source_document=document,
            document_type="tên loại chứng từ của bạn",  # VD: "phiếu thu", "phiếu chi"
            account_mappings=account_mappings
        )

    except Exception as e:
        # ✅ FAIL FAST: Thông báo lỗi rõ ràng để dễ debug
        raise Exception(f"Lỗi tạo bút toán {document.so_ct}: {str(e)}") from e

def update_debt_entry(self, document) -> bool:
    """
    ✅ CHUẨN: Cập nhật bút toán kế toán cho chứng từ.

    Pattern từ HoaDonBanHangService - đã được kiểm chứng và tối ưu hóa.
    """
    try:
        # ✅ BUSINESS LOGIC: Xác định mapping dựa trên trạng thái chứng từ
        account_mappings = self._determine_accounting_mappings(document)

        # ✅ ĐƠN GIẢN: Sử dụng service đã khởi tạo sẵn
        return self._cong_no_service.update_document_accounting_entries(
            source_document=document,
            document_type="tên loại chứng từ của bạn",  # VD: "phiếu thu", "phiếu chi"
            account_mappings=account_mappings
        )

    except Exception as e:
        # ✅ FAIL FAST: Thông báo lỗi rõ ràng để dễ debug
        raise Exception(f"Lỗi cập nhật bút toán {document.so_ct}: {str(e)}") from e

def get_accounting_configuration(self) -> List[Dict[str, Any]]:
    """
    Lấy cấu hình kế toán cho chứng từ.

    Returns:
        List[Dict[str, Any]]: Danh sách mapping configuration
    """
    return self.DOCUMENT_ACCOUNTING_CONFIG.copy()
```

**📝 Lưu ý quan trọng về Core Methods:**

1. **create_debt_entry()**:

   - Được gọi khi tạo mới chứng từ
   - Sử dụng trong transaction của method tạo chứng từ
   - Nếu thất bại sẽ rollback toàn bộ transaction

2. **update_debt_entry()**:

   - Được gọi khi cập nhật chứng từ
   - Sẽ xóa bút toán cũ và tạo lại bút toán mới
   - Đảm bảo tính nhất quán của dữ liệu kế toán

3. **document_type**:

   - Thay thế bằng tên mô tả chứng từ của bạn
   - Ví dụ: "phiếu thu tiền mặt", "hóa đơn mua hàng", "phiếu chi ngân hàng"

4. **Error Handling**:
   - Luôn throw exception với thông báo rõ ràng
   - Giúp dễ dàng debug khi có lỗi
   - Đảm bảo transaction safety

## 📊 Ví Dụ Thực Tế Từ Các Service Đã Triển Khai

### 1. PhieuChiService - Service Phiếu Chi (Pattern Header-Detail với Flexible Account Sources)

```python
class PhieuChiService(BaseService):
    """
    Service cho Phiếu Chi - Pattern Header-Detail với flexible account sources.

    ✅ THỰC TẾ: Đã được test và hoạt động thành công
    """

    # ✅ CẤU HÌNH KẾ TOÁN CHO PHIẾU CHI (Flexible Account Sources)
    PAYMENT_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'CHIHD',                # Chi hóa đơn (ma_ngv=1)
            'debit_account_field': 'tk_no',         # Tài khoản chi phí/công nợ - DEBIT
            'credit_account_field': 'tk',           # Tài khoản tiền - CREDIT
            'debit_account_source': 'detail',       # ✅ Lấy debit account từ detail (tk_no)
            'credit_account_source': 'header',      # ✅ Lấy credit account từ header (tk)
            'amount_field': 'tien',                 # Số tiền từ detail
            'detail_source': 'children',            # Related name to detail (PhieuChiChiTiet)
            'canCreate': True                       # Default: always create entry
        }
    ]

    def _determine_accounting_mappings(self, phieu_chi):
        """
        ✅ THỰC TẾ: Business logic đã được test với SafeBook
        """
        mappings = []

        # ✅ BUSINESS LOGIC: Determine journal type based on ma_ngv
        if phieu_chi.ma_ngv == "1":
            journal_type = "CHIHD"  # Chi hóa đơn
        elif phieu_chi.ma_ngv in ["2", "3", "4"]:
            journal_type = "CHINCC"  # Chi nhà cung cấp
        else:
            journal_type = "CHIHD"  # Default fallback

        # Add main payment mapping
        main_mapping = self.PAYMENT_ACCOUNTING_CONFIG[0].copy()
        main_mapping['journal_type'] = journal_type
        mappings.append(main_mapping)

        return mappings
```

**🎯 Kết quả test thành công:**

- **Input**: PhieuChi với 2 chi tiết (200,000 + 20,000)
- **Output**: 2 journal entries được tạo đúng
- **Logic**: tk_no từ detail, tk từ header, tien từ detail

### 2. PhieuThuService - Service Phiếu Thu (Pattern Header-Detail)

```python
class PhieuThuService(BaseService):
    """
    Service cho Phiếu Thu - Pattern chỉ có Header.

    Phiếu thu thường có cấu trúc đơn giản: chỉ có thông tin header,
    không có bảng chi tiết phức tạp.
    """

    # ✅ CẤU HÌNH KẾ TOÁN CHO PHIẾU THU
    RECEIPT_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'CONGNO',              # Loại bút toán: Công nợ
            'debit_account_field': 'tk_tien',      # TK tiền mặt/ngân hàng (Nợ)
            'credit_account_field': 'tk_du',       # TK công nợ phải thu (Có)
            'amount_field': 'tien',                # Field số tiền thu
            'detail_source': None,                 # Không có bảng chi tiết
            'canCreate': True                      # Mặc định cho phép tạo
        }
    ]

    def __init__(self):
        super().__init__()
        # ... code khởi tạo khác của bạn ...

        # ✅ Khởi tạo unified accounting service
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(self, phieu_thu):
        """
        Business logic cho phiếu thu.

        Tùy chỉnh logic này theo quy trình nghiệp vụ của công ty bạn.
        """
        mappings = self.get_accounting_configuration()

        # ✅ LOGIC TRẠNG THÁI - TÙY CHỈNH GIÁ TRỊ STATUS
        if hasattr(phieu_thu, 'status'):
            status = getattr(phieu_thu, 'status', 'mac_dinh_da_duyet')

            # TODO: Thay thế bằng giá trị status thực tế của PhieuThu
            if status in ['nhap', 'cho_duyet']:  # Nháp, chờ duyệt
                for mapping in mappings:
                    mapping['canCreate'] = False  # Không tạo bút toán

            elif status in ['da_duyet', 'hoan_thanh']:  # Đã duyệt, hoàn thành
                for mapping in mappings:
                    mapping['canCreate'] = True   # Tạo bút toán

        # ✅ QUY TẮC NGHIỆP VỤ: Không tạo bút toán nếu số tiền = 0
        if not hasattr(phieu_thu, 'tien') or getattr(phieu_thu, 'tien', 0) <= 0:
            for mapping in mappings:
                mapping['canCreate'] = False

        # ✅ QUY TẮC NGHIỆP VỤ: Kiểm tra tài khoản hợp lệ
        if hasattr(phieu_thu, 'tk_tien') and not getattr(phieu_thu, 'tk_tien', ''):
            for mapping in mappings:
                mapping['canCreate'] = False  # Không có tài khoản tiền

        return mappings

    # ✅ Triển khai các method cốt lõi
    def create_debt_entry(self, phieu_thu) -> bool:
        try:
            account_mappings = self._determine_accounting_mappings(phieu_thu)
            return self._cong_no_service.create_document_accounting_entries(
                source_document=phieu_thu,
                document_type="phiếu thu tiền mặt",
                account_mappings=account_mappings
            )
        except Exception as e:
            raise Exception(f"Lỗi tạo bút toán phiếu thu {phieu_thu.so_ct}: {str(e)}") from e

    def update_debt_entry(self, phieu_thu) -> bool:
        try:
            account_mappings = self._determine_accounting_mappings(phieu_thu)
            return self._cong_no_service.update_document_accounting_entries(
                source_document=phieu_thu,
                document_type="phiếu thu tiền mặt",
                account_mappings=account_mappings
            )
        except Exception as e:
            raise Exception(f"Lỗi cập nhật bút toán phiếu thu {phieu_thu.so_ct}: {str(e)}") from e

    def get_accounting_configuration(self):
        return self.RECEIPT_ACCOUNTING_CONFIG.copy()
```

**📝 Giải thích PhieuThuService:**

- **Bút toán phiếu thu**: Nợ TK tiền, Có TK công nợ phải thu
- **Header-only**: Không có bảng chi tiết, tất cả thông tin ở header
- **Business logic**: Kiểm tra trạng thái, số tiền, tài khoản hợp lệ
- **Error handling**: Thông báo lỗi cụ thể cho phiếu thu

## 🎯 Những Điểm Quan Trọng Cần Nhớ

### ✅ **Đơn Giản Hóa So Với Trước**

1. **Không cần quản lý Entity ID**:

   ```python
   # ❌ Cách cũ (phức tạp)
   entity_id = str(document.entity_model.uuid)
   cong_no_service = self._get_cong_no_service(entity_id)

   # ✅ Cách mới (đơn giản)
   self._cong_no_service = CongNoCreation()  # Không cần tham số
   ```

2. **Không cần Caching phức tạp**:

   ```python
   # ❌ Cách cũ
   def _get_cong_no_service(self, entity_id):
       if self._cong_no_service is None or self._current_entity_id != entity_id:
           # ... logic phức tạp

   # ✅ Cách mới
   def __init__(self):
       self._cong_no_service = CongNoCreation()  # Đơn giản
   ```

### ✅ **Status Values - Tùy Chỉnh Cho Từng Chứng Từ**

Mỗi loại chứng từ có status values riêng, **KHÔNG có pattern chung**:

```python
# Ví dụ các status values khác nhau:

# PhieuThu có thể có:
if status in ['nhap', 'cho_duyet']:          # Không tạo bút toán
if status in ['da_duyet', 'hoan_thanh']:     # Tạo bút toán

# PhieuChi có thể có:
if status in ['nhap', 'cho_duyet', 'cho_ky']: # Không tạo bút toán
if status in ['da_duyet', 'da_chi']:          # Tạo bút toán

# HoaDonMuaHang có thể có:
if status in ['nhap', 'cho_duyet']:           # Không tạo bút toán
if status in ['da_duyet', 'da_ghi_nhan']:     # Tạo bút toán
```

**📝 TODO**: Bạn cần xác định status values cụ thể cho từng loại chứng từ.

## 🚀 **Bước Tiếp Theo**

1. **Chọn loại chứng từ** bạn muốn implement đầu tiên
2. **Xác định status values** cụ thể cho chứng từ đó
3. **Copy template** phù hợp (Header-Only hoặc Header-Detail)
4. **Tùy chỉnh business logic** theo quy trình của công ty
5. **Test kỹ lưỡng** với dữ liệu thực
6. **Lặp lại** cho các loại chứng từ khác

## 📞 **Hỗ Trợ**

Nếu gặp khó khăn trong quá trình implement:

1. **Kiểm tra cấu hình**: Đảm bảo tất cả field names đúng
2. **Test business logic**: Isolate test method `_determine_accounting_mappings()`
3. **Debug từng bước**: Kiểm tra từng mapping có `canCreate = True`
4. **Validate dữ liệu**: Đảm bảo document có đầy đủ field cần thiết

**Tham khảo**: HoaDonBanHangService là implementation chuẩn hoàn chỉnh nhất.

---

## � Flexible Account Sources Patterns

### Pattern 1: Traditional (Debit từ header, Credit từ detail)

```python
# ✅ VD: Hóa đơn bán hàng
{
    'debit_account_field': 'tk',              # Công nợ khách hàng
    'credit_account_field': 'tk_dt',          # Doanh thu
    'debit_account_source': 'header',         # tk từ header
    'credit_account_source': 'detail',        # tk_dt từ detail
    'detail_source': 'chi_tiet'               # Có detail
}
```

### Pattern 2: Reversed (Debit từ detail, Credit từ header)

```python
# ✅ VD: PhieuChi - Đã test thành công
{
    'debit_account_field': 'tk_no',           # Chi phí/Công nợ
    'credit_account_field': 'tk',             # Tiền mặt
    'debit_account_source': 'detail',         # tk_no từ chi_tiet_data
    'credit_account_source': 'header',        # tk từ header
    'detail_source': 'children'               # Related name
}
```

### Pattern 3: Header-Only (Cả 2 từ header)

```python
# ✅ VD: PhieuThu đơn giản
{
    'debit_account_field': 'tk_tien',         # Tiền mặt
    'credit_account_field': 'tk_du',          # Công nợ
    'debit_account_source': 'header',         # Cả 2 từ header
    'credit_account_source': 'header',        # Cả 2 từ header
    'detail_source': None                     # Không có detail
}
```

### Pattern 4: Detail-Only (Cả 2 từ detail)

```python
# ✅ VD: Hóa đơn phức tạp
{
    'debit_account_field': 'tk_no',           # Tài khoản nợ
    'credit_account_field': 'tk_co',          # Tài khoản có
    'debit_account_source': 'detail',         # Cả 2 từ detail
    'credit_account_source': 'detail',        # Cả 2 từ detail
    'detail_source': 'chi_tiet'               # Có detail
}
```

## 🔧 Troubleshooting Common Issues

### 1. "Debit account not found in header"

**Nguyên nhân**: Config `debit_account_source: 'detail'` nhưng `detail_source: None`
**Giải pháp**: Đảm bảo khi account_source là 'detail' thì phải có detail_source

### 2. "Children count: 0"

**Nguyên nhân**: Serializer không xử lý đúng field name
**Giải pháp**: Kiểm tra field name trong request (VD: `chi_tiet_data` thay vì `child_items`)

### 3. "No journal entries created"

**Nguyên nhân**: `canCreate: False` hoặc điều kiện business logic
**Giải pháp**: Kiểm tra logic trong `_determine_accounting_mappings()`

**🎉 Pattern này đã được tối ưu hóa với Flexible Account Sources! Chúc bạn implement thành công!** 🚀
