"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for Bao Cao Ton <PERSON>ho (Inventory Report).

This ViewSet provides enterprise-grade inventory reporting with comprehensive filtering
and performance optimization, following 20 years of ERP experience.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view

from django_ledger.api.decorators.error_handling import api_exception_handler
from django_ledger.api.serializers.ton_kho.bao_cao_ton_kho.bao_cao_ton_kho import (
    BaoCaoTonKhoRequestSerializer,
    BaoCaoTonKhoResponseSerializer,
)
from django_ledger.api.views.common import ERPPagination
from django_ledger.services.ton_kho.bao_cao_ton_kho.bao_cao_ton_kho import (
    BaoCaoTonKhoService,
)


@extend_schema_view(
    get_report=extend_schema(
        summary="Get Inventory Report",
        description="Get current inventory balance report (Bao <PERSON>) with filtering and pagination via POST body data",
        request=BaoCaoTonKhoRequestSerializer,
        responses={200: BaoCaoTonKhoResponseSerializer(many=True)},
        tags=["Inventory Reports"],
    )
)
class BaoCaoTonKhoViewSet(viewsets.ViewSet):
    """
    ViewSet for Inventory Report (Bao Cao Ton Kho).

    Provides single endpoint for getting current inventory balance reports with filtering via POST body data.
    This follows enterprise ERP patterns for complex reporting with extensive filter capabilities.

    Features:
    - Current stock balance calculations as of report date
    - Comprehensive filtering options (warehouse, material, category, groups)
    - Performance-optimized queries for large datasets
    - Proper error handling and validation
    - Enterprise-grade pagination support
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = None  # Will be initialized when needed

    @api_exception_handler
    def get_report(self, request, entity_slug):
        """
        Generate inventory report with filtering via POST body data.

        This endpoint implements enterprise-grade inventory reporting with current
        stock balance calculations and comprehensive filtering capabilities.

        Parameters
        ----------
        request : Request
            The request object containing POST body data for filtering
        entity_slug : str
            The entity slug

        Returns
        -------
        Response
            The inventory report data with pagination
        """
        # Validate POST body data
        serializer = BaoCaoTonKhoRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "detail": "Invalid parameters",
                    "errors": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get validated data
        validated_data = serializer.validated_data

        # Initialize service if not already done
        if self.service is None:
            self.service = BaoCaoTonKhoService()

        # Generate report using service
        report_data = self.service.generate_report(
            entity_slug=entity_slug, filters=validated_data
        )

        # Apply pagination
        paginator = self.pagination_class()
        paginated_data = paginator.paginate_queryset(report_data, request)

        # Serialize response data
        response_serializer = BaoCaoTonKhoResponseSerializer(
            paginated_data, many=True
        )

        # Return paginated response
        return paginator.get_paginated_response(response_serializer.data)
