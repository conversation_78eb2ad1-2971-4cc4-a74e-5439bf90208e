"""
Sales Reports Views.

This module provides API views for all sales reports.
"""

try:
    from .bao_cao_tinh_trang_don_hang import BaoCaoTinhTrangDonHangViewSet
except ImportError as e:
    print(f"Warning: Sales order status report views import error: {e}")
    pass

try:
    from .bao_cao_so_sanh_ban_hang_hai_ky import BaoCaoSoSanhBanHangHaiKyViewSet
except ImportError as e:
    print(f"Warning: Sales comparison report views import error: {e}")
    pass

__all__ = [
    'BaoCaoTinhTrangDonHangViewSet',
    'BaoCaoSoSanhBanHangHaiKyViewSet',
]
