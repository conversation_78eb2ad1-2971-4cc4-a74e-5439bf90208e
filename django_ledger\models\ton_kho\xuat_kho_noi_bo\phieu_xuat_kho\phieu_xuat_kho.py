"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatKho (Warehouse Export) model implementation.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class PhieuXuatKhoModelQueryset(QuerySet):
    """
    A custom defined PhieuXuatKhoModelQueryset that will act as an interface to handling the DB queries to the  # noqa: E501
    PhieuXuatKhoModel.
    """

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Fetches a QuerySet of PhieuXuatKhoModels for a specific entity.

        Parameters
        __________
        entity_slug: str
            The entity slug to filter by.
        """
        return self.filter(entity_model__slug__exact=entity_slug)


class PhieuXuatKhoModelManager(Manager):
    """
    A custom defined PhieuXuatKhoModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    PhieuXuatKhoModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom PhieuNhapChiPhiMuaHangModelQueryset.
        """
        return PhieuXuatKhoModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuNhapChiPhiMuaHangModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModelQueryset
            A QuerySet of PhieuNhapChiPhiMuaHangModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)


class PhieuXuatKhoModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuXuatKhoModel database will inherit from.  # noqa: E501
    The PhieuXuatKhoModel inherits functionality from the following MixIns:

        1. :func:`ChungTuMixIn <django_ledger.models._mixins.chung_tu_mixins.ChungTuMixIn>`
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    entity_model: EntityModel
        The EntityModel associated with this warehouse export.

    nguoi_tao: str
        The creator of the warehouse export.

    ngay_tao: datetime
        The creation date of the warehouse export.

    ma_gd: str
        The transaction code.

    ma_kh: CustomerModel
        The customer code.

    ma_ngv: str
        The task assigner code.

    unit_id: EntityUnitModel
        The organizational unit.

    dien_giai: str
        The description.

    ma_nt: NgoaiTeModel
        The currency code.

    ty_gia: decimal
        The exchange rate.

    t_so_luong: decimal
        The total quantity.

    t_tien_nt: decimal
        The total amount in foreign currency.

    t_tien: decimal
        The total amount.

    status: str
        The status.

    transfer_yn: bool
        The transferred status.

    Note: Document fields (i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct, chung_tu)
    are inherited from ChungTuMixIn.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_('UUID'),
        help_text=_('Unique identifier for the record'),
    )
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity Model'),
        help_text=_('Entity that this warehouse export belongs to'),
    )
    nguoi_tao = models.CharField(
        max_length=50,
        verbose_name=_('Người tạo'),
        help_text=_('Creator of the warehouse export'),
    )
    ngay_tao = models.DateTimeField(
        verbose_name=_('Ngày tạo'),
        help_text=_('Creation date of the warehouse export'),
    )
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng'),
        related_name='phieu_xuat_kho',
        null=True,
        blank=True,
        help_text=_("Related customer"),
    )

    ma_ngv = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('Mã người giao việc'),
        help_text=_("Assignee code"),
    )
    ma_gd = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('Mã giao dịch'),
        help_text=_("Transaction code"),
    )
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.CASCADE,
        related_name='phieu_xuat_kho',
        null=True,
        blank=True,
        verbose_name=_('Đơn vị'),
        help_text=_("Related organizational unit"),
    )
    dien_giai = models.TextField(
        verbose_name=_('Diễn giải'),
        help_text=_("Detailed description of the export"),
    )

    # Progress information
    id_progress = models.IntegerField(
        verbose_name=_('ID tiến độ'),
        help_text=_("Progress tracking identifier"),
    )
    xprogress = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Chi tiết tiến độ'),
        help_text=_("Additional progress information"),
    )

    # Additional datetime info
    xdatetime2 = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Thông tin thời gian bổ sung'),
        help_text=_("Additional datetime information"),
    )
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã ngoại tệ'),
        related_name='phieu_xuat_kho',
        null=True,
        help_text=_("Currency used in the transaction"),
    )
    ty_gia = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tỷ giá'),
        help_text=_('Exchange rate'),
    )
    t_so_luong = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tổng số lượng'),
        help_text=_('Total quantity'),
    )
    t_tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ'),
        help_text=_('Total amount in foreign currency'),
    )
    t_tien = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tổng tiền'),
        help_text=_('Total amount'),
    )
    status = models.CharField(
        max_length=50, verbose_name=_('Trạng thái'), help_text=_('Status')
    )
    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_('Đã chuyển'),
        help_text=_('Transferred status'),
    )
    xfile = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Tệp đính kèm'),
        help_text=_("File reference"),
    )

    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho phiếu xuất kho này"),
        related_name="phieu_xuat_kho",
    )

    objects = PhieuXuatKhoModelManager.from_queryset(PhieuXuatKhoModelQueryset)()

    class Meta:
        abstract = True
        verbose_name = _('Phiếu Xuất Kho')
        verbose_name_plural = _('Phiếu Xuất Kho')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['ma_nt']),
            models.Index(fields=['unit_id']),
            models.Index(fields=['status']),
        ]
        ordering = ['-created']

    def __str__(self):  # noqa: C901
        return f'{self.so_ct}: {self.dien_giai}'


class PhieuXuatKhoModel(PhieuXuatKhoModelAbstract):
    """
    Base Warehouse Export Model Implementation
    """

    class Meta(PhieuXuatKhoModelAbstract.Meta):
        abstract = False
        db_table = 'phieu_xuat_kho'
