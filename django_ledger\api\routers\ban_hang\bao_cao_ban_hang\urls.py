"""
Sales Reports URLs (Module Level).

This module defines URL patterns for all sales reports.
"""

from django.urls import include, path

app_name = 'bao_cao_ban_hang'

urlpatterns = [
    path(
        'bao-cao-tinh-trang-don-hang/',
        include(
            'django_ledger.api.routers.ban_hang.bao_cao_ban_hang.bao_cao_tinh_trang_don_hang.urls'
        ),
    ),
    path(
        'bao-cao-so-sanh-ban-hang-hai-ky/',
        include(
            'django_ledger.api.routers.ban_hang.bao_cao_ban_hang.bao_cao_so_sanh_ban_hang_hai_ky.urls'
        ),
    ),
    path(
        'bao-cao-tong-hop-hang-ban-tra-lai/',
        include('django_ledger.api.routers.ban_hang.bao_cao_ban_hang.bao_cao_tong_hop_hang_ban_tra_lai.urls')
    ),
    # Future sales reports can be added here
    # path('bao-cao-doanh-thu/', include('...')),
    # path('bao-cao-khach-hang/', include('...')),
]
