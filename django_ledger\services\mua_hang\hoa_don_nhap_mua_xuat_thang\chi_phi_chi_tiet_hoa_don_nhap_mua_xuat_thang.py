"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiPhiChiTietHoaDonNhapMuaXuatThang Service implementation.
"""

from typing import Any, Dict  # noqa: F401
from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401


class ChiPhiChiTietHoaDonNhapMuaXuatThangService(BaseService):
    """
    Service class for ChiPhiChiTietHoaDonNhapMuaXuatThangModel.
    Provides business logic for detailed invoice cost operations.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the ChiPhiChiTietHoaDonNhapMuaXuatThangRepository.
        """
        super().__init__()
        self.repository = ChiPhiChiTietHoaDonNhapMuaXuatThangRepository()

    def get_by_invoice(self, hoa_don_uuid) -> QuerySet:  # noqa: C901
        """
        Get ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances for the specified invoice.
        """
        return self.repository.get_by_invoice(hoa_don_uuid)

    def get_by_id(self, uuid, hoa_don_uuid=None) -> ChiPhiChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Get a ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiPhiChiTietHoaDonNhapMuaXuatThangModel to retrieve.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiPhiChiTietHoaDonNhapMuaXuatThangModel
            The retrieved ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        return self.repository.get_by_id(uuid, hoa_don_uuid)

    def create(self, data: Dict[str, Any]) -> ChiPhiChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the ChiPhiChiTietHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ChiPhiChiTietHoaDonNhapMuaXuatThangModel
            The created ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        return self.repository.create(**data)

    def update(self, uuid, data: Dict[str, Any], hoa_don_uuid=None) -> ChiPhiChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiPhiChiTietHoaDonNhapMuaXuatThangModel to update.
        data : Dict[str, Any]
            The data to update the ChiPhiChiTietHoaDonNhapMuaXuatThangModel with.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiPhiChiTietHoaDonNhapMuaXuatThangModel
            The updated ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        instance = self.get_by_id(uuid, hoa_don_uuid)
        return self.repository.update(instance, **data)

    def delete(self, uuid, hoa_don_uuid=None) -> None:  # noqa: C901
        """
        Delete a ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiPhiChiTietHoaDonNhapMuaXuatThangModel to delete.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.
        """
        instance = self.get_by_id(uuid, hoa_don_uuid)
        self.repository.delete(instance)

    def filter_by_material(self, material_uuid, hoa_don_uuid=None) -> QuerySet:  # noqa: C901
        """
        Filter ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances by material.

        Parameters
        ----------
        material_uuid : UUID
            The UUID of the material to filter by.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        QuerySet
            A queryset of ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances for the specified material.
        """
        return self.repository.filter_by_material(material_uuid, hoa_don_uuid)

    def get_total_by_invoice(self, hoa_don_uuid) -> dict:  # noqa: C901
        """
        Get total detailed cost amounts for ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances by invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        dict
            A dictionary containing total detailed cost amounts.
        """
        return self.repository.get_total_by_invoice(hoa_don_uuid)
