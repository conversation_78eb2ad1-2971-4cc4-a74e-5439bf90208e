"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ThueHoaDonNhapMuaXuatThang Serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ThueHoaDonNhapMuaXuatThangModel,
)


class ThueHoaDonNhapMuaXuatThangSerializer(serializers.ModelSerializer):
    """
    Serializer for ThueHoaDonNhapMuaXuatThangModel.
    Used for read operations and API responses.
    """

    # Related object data
    hoa_don_data = serializers.SerializerMethodField()
    ma_thue_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()
    tk_thue_no_data = serializers.SerializerMethodField()
    tk_du_data = serializers.SerializerMethodField()
    ma_kh9_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_lsx_data = serializers.SerializerMethodField()

    class Meta:
        model = ThueHoaDonNhapMuaXuatThangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]

    def get_hoa_don_data(self, obj):  # noqa: C901
        """Get invoice data."""
        if obj.hoa_don:
            # Avoid circular import by importing here
            from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.hoa_don_nhap_mua_xuat_thang import (
                HoaDonNhapMuaXuatThangSerializer,
            )

            return HoaDonNhapMuaXuatThangSerializer(obj.hoa_don).data
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """Get tax data."""
        if obj.ma_thue:
            from django_ledger.api.serializers.tax import TaxModelSerializer

            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_ma_kh_data(self, obj):  # noqa: C901
        """Get customer data."""
        if obj.ma_kh:
            from django_ledger.api.serializers.customer import CustomerModelSerializer

            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_thue_no_data(self, obj):  # noqa: C901
        """Get tax debit account data."""
        if obj.tk_thue_no:
            from django_ledger.api.serializers.accounts import AccountModelSerializer

            return AccountModelSerializer(obj.tk_thue_no).data
        return None

    def get_tk_du_data(self, obj):  # noqa: C901
        """Get balance account data."""
        if obj.tk_du:
            from django_ledger.api.serializers.accounts import AccountModelSerializer

            return AccountModelSerializer(obj.tk_du).data
        return None

    def get_ma_kh9_data(self, obj):  # noqa: C901
        """Get customer 9 data."""
        if obj.ma_kh9:
            from django_ledger.api.serializers.customer import CustomerModelSerializer

            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        """Get payment method data."""
        if obj.ma_tt:
            from django_ledger.api.serializers.erp import HanThanhToanModelSerializer

            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """Get department data."""
        if obj.ma_bp:
            from django_ledger.api.serializers.erp import BoPhanModelSerializer

            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """Get task data."""
        if obj.ma_vv:
            # Assuming VuViecModel serializer exists
            return {"uuid": str(obj.ma_vv.uuid), "ma_vv": obj.ma_vv.ma_vv}
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """Get contract data."""
        if obj.ma_hd:
            # Assuming HopDongModel serializer exists
            return {"uuid": str(obj.ma_hd.uuid), "ma_hd": obj.ma_hd.ma_hd}
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """Get agreement data."""
        if obj.ma_ku:
            from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (
                KheUocModelSerializer,
            )

            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """Get fee data."""
        if obj.ma_phi:
            from django_ledger.api.serializers.erp import PhiModelSerializer

            return PhiModelSerializer(obj.ma_phi).data
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """Get payment batch data."""
        if obj.ma_dtt:
            from django_ledger.api.serializers.erp import DotThanhToanModelSerializer

            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """Get product data."""
        if obj.ma_sp:
            from django_ledger.api.serializers.erp import (
                VatTuSanPhamHangHoaModelSerializer,
            )

            return VatTuSanPhamHangHoaModelSerializer(obj.ma_sp).data
        return None

    def get_ma_lsx_data(self, obj):  # noqa: C901
        """Get production order data."""
        if obj.ma_lsx:
            # ma_lsx is now a CharField, not a ForeignKey
            return {"ma_lsx": obj.ma_lsx}
        return None


class ThueHoaDonNhapMuaXuatThangCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for ThueHoaDonNhapMuaXuatThangModel.
    Used for create and update operations.
    """

    class Meta:
        model = ThueHoaDonNhapMuaXuatThangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]
