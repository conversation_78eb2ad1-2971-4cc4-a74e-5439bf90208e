"""
Django Ledger - Debt Management Types

Module này định nghĩa các TypedDict và types được sử dụng chung
trong debt management package để tránh circular imports.

✅ ENHANCED với support cho detail-level conditional logic (Phase 1-3):
- ConditionOperator: Đ<PERSON>nh nghĩa các operators (gt, lt, eq, in, between, etc.)
- DetailConditions: Mapping field names to condition operators
- Enhanced AccountMappingConfig với detail_conditions support
- Utility types cho debugging và monitoring

Tạo bởi: Chuyên gia ERP & Lập trình (20 năm kinh nghiệm)
Ngày: 2024-12-19
Cập nhật: 2024-12-26 (Enhanced Conditional Logic)
"""

from typing import TypedDict, Any, Optional, Dict, Union, List


# ✅ ENHANCED CONDITIONAL LOGIC TYPES (Phase 1-3)

class ConditionOperator(TypedDict, total=False):
    """
    TypedDict định nghĩa operators cho enhanced conditional logic.

    Supports all comparison operators with flexible value types.

    Examples
    --------
    # Numeric comparison
    {'gt': 1000000}  # > 1M

    # Field comparison
    {'gte': 'tien1'}  # >= tien1 field

    # List membership
    {'in': ['A', 'B', 'VIP']}  # in list

    # Range check
    {'between': [10, 50]}  # between 10 and 50

    # Null check
    {'is_not_null': True}  # is not null
    """
    # Comparison operators
    eq: Any                    # Equal ==
    ne: Any                    # Not equal !=
    gt: Union[int, float, str] # Greater than >
    gte: Union[int, float, str] # Greater than or equal >=
    lt: Union[int, float, str] # Less than <
    lte: Union[int, float, str] # Less than or equal <=

    # List operators
    in_: List[Any]             # In list (using in_ to avoid keyword conflict)
    not_in: List[Any]          # Not in list

    # Null operators
    is_null: bool              # Is null
    is_not_null: bool          # Is not null

    # Range operator
    between: List[Union[int, float]]  # Between [min, max]


class DetailConditions(TypedDict, total=False):
    """
    TypedDict định nghĩa enhanced detail conditions.

    Maps field names to their condition operators.
    All conditions use AND logic.

    Examples
    --------
    {
        'thue_nt': {'gt': 0},                    # thue_nt > 0
        'tien2': {'gte': 'tien1'},               # tien2 >= tien1 (field comparison)
        'is_khuyenmai': {'eq': False},           # is_khuyenmai == False
        'category': {'in': ['A', 'B', 'VIP']},   # category in list
        'discount': {'between': [10, 50]}        # discount between 10-50%
    }
    """
    # Dynamic field mapping - any field name can be used
    # The actual fields will be determined at runtime based on the model


class JournalEntryMapping(TypedDict):
    """
    TypedDict định nghĩa mapping cho việc tạo bút toán.

    Attributes
    ----------
    account_field : str
        Tên field tài khoản trong chi tiết document (vd: 'tk_dt', 'tk_thue_co')
    amount_field : str
        Tên field số tiền trong chi tiết document (vd: 'tien2', 'thue')
    journal_type : str
        Loại bút toán (vd: 'DT0CK', 'THUE')
    source_document : Any
        Document nguồn (hóa đơn, phiếu thu, phiếu chi, etc.)
    debit_account : Any
        Tài khoản bên nợ
    chi_tiet_list : Any
        Danh sách chi tiết của document
    ledger : Any
        Sổ cái
    """
    account_field: str
    amount_field: str
    journal_type: str
    source_document: Any
    debit_account: Any
    chi_tiet_list: Any
    ledger: Any
    skip_audit_creation: bool


class AccountMappingConfig(TypedDict, total=False):
    """
    TypedDict định nghĩa cấu hình mapping tài khoản cho unified accounting methods.

    ✅ ENHANCED với support cho detail-level conditional logic (Phase 1-3).

    Attributes
    ----------
    journal_type : str
        Loại bút toán (vd: 'DT0CK', 'THUE', 'CONGNO')
    debit_account_field : str
        Tên field tài khoản bên nợ
    credit_account_field : str
        Tên field tài khoản bên có
    debit_account_source : Optional[str]
        Nguồn lấy debit account: 'header' hoặc 'detail' (mặc định 'header')
    credit_account_source : Optional[str]
        Nguồn lấy credit account: 'header' hoặc 'detail' (mặc định 'detail')
    amount_field : str
        Tên field số tiền
    detail_source : Optional[str]
        Tên related field chứa chi tiết (None nếu chỉ có header)
    canCreate : bool
        Flag để xác định có tạo bút toán này hay không dựa trên business logic
        True = tạo bút toán, False = skip bút toán này
    detail_conditions : Optional[Dict[str, ConditionOperator]]
        ✅ NEW: Enhanced conditional logic cho detail-level filtering
        Maps field names to condition operators for selective journal entry creation

    Examples
    --------
    # Basic mapping without conditions
    {
        'journal_type': 'DT0CK',
        'debit_account_field': 'tk',
        'credit_account_field': 'tk_dt',
        'amount_field': 'tien2',
        'detail_source': 'chi_tiet',
        'canCreate': True
    }

    # Enhanced mapping with conditions
    {
        'journal_type': 'high_value_dt',
        'debit_account_field': 'tk',
        'credit_account_field': 'tk_dt_high',
        'amount_field': 'tien2',
        'detail_source': 'chi_tiet',
        'detail_conditions': {
            'tien2': {'gt': 1000000},              # Revenue > 1M
            'thue_nt': {'gt': 0},                  # Has tax
            'is_khuyenmai': {'eq': False},         # Not promotional
            'category': {'in': ['VIP', 'PREMIUM']} # Premium categories
        },
        'canCreate': True
    }
    """
    # Required fields
    journal_type: str
    debit_account_field: str
    credit_account_field: str
    amount_field: str
    canCreate: bool

    # Optional fields
    debit_account_source: Optional[str]
    credit_account_source: Optional[str]
    detail_source: Optional[str]
    detail_conditions: Optional[Dict[str, ConditionOperator]]  # ✅ NEW


class SupportedOperators(TypedDict):
    """
    TypedDict định nghĩa các operators được support trong enhanced conditional logic.

    Maps operator names to their string representations for documentation.
    """
    eq: str          # '=='
    ne: str          # '!='
    gt: str          # '>'
    gte: str         # '>='
    lt: str          # '<'
    lte: str         # '<='
    in_: str         # 'in'
    not_in: str      # 'not in'
    is_null: str     # 'is None'
    is_not_null: str # 'is not None'
    between: str     # 'between'


class DocumentAccountingConfig(TypedDict):
    """
    TypedDict định nghĩa cấu hình tổng thể cho document accounting.

    ✅ ENHANCED với support cho detail-level conditional logic.

    Attributes
    ----------
    document_type : str
        Loại chứng từ
    ledger_name_template : str
        Template tên ledger
    account_mappings : List[AccountMappingConfig]
        Danh sách mapping configurations với enhanced conditional support

    Examples
    --------
    {
        'document_type': 'hóa đơn bán hàng',
        'ledger_name_template': 'HD-{so_ct}',
        'account_mappings': [
            {
                'journal_type': 'high_value_dt',
                'debit_account_field': 'tk',
                'credit_account_field': 'tk_dt_high',
                'amount_field': 'tien2',
                'detail_source': 'chi_tiet',
                'detail_conditions': {
                    'tien2': {'gt': 1000000},
                    'is_khuyenmai': {'eq': False}
                },
                'canCreate': True
            }
        ]
    }
    """
    document_type: str
    ledger_name_template: str
    account_mappings: List[AccountMappingConfig]


# ✅ UTILITY TYPES FOR ENHANCED CONDITIONAL LOGIC

class ConditionEvaluationResult(TypedDict):
    """
    TypedDict định nghĩa kết quả evaluation của conditions.

    Used for debugging and logging condition evaluation results.
    """
    field_name: str
    operator: str
    expected_value: Any
    actual_value: Any
    result: bool
    error_message: Optional[str]


class ConditionalAccountingStats(TypedDict):
    """
    TypedDict định nghĩa thống kê cho conditional accounting.

    Used for performance monitoring and debugging.
    """
    total_details_processed: int
    details_matched_conditions: int
    details_skipped: int
    journal_entries_created: int
    conditions_evaluated: int
    evaluation_errors: int
