"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the HoaDonMuaHangTrongNuocModel, which represents a Domestic Purchase Invoice  # noqa: E501
received from a Supplier/Vendor, on which the Vendor states the amount owed by the recipient  # noqa: E501
for the purposes of supplying goods and/or services within the domestic market.

This model inherits from BillModel to leverage the accounting workflow for bills.
"""

from django.db import models  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.bill import BillModel  # noqa: F401,


class HoaDonMuaHangTrongNuocModelQueryset(models.QuerySet):
    """
    A custom defined HoaDonMuaHangTrongNuocModel QuerySet.
    """

    def active(self):  # noqa: C901
        """
        Returns active HoaDonMuaHangTrongNuocModel instances.
        """
        return self.filter(status="1")

    def inactive(self):  # noqa: C901
        """
        Returns inactive HoaDonMuaHangTrongNuocModel instances.
        """
        return self.exclude(status="1")


class HoaDonMuaHangTrongNuocModelManager(models.Manager):
    """
    A custom defined HoaDonMuaHangTrongNuocModel Manager that will act as an interface to handle the  # noqa: E501
    HoaDonMuaHangTrongNuocModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom HoaDonMuaHangTrongNuocModelQueryset.
        """
        return HoaDonMuaHangTrongNuocModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns a QuerySet of HoaDonMuaHangTrongNuocModel associated with a specific EntityModel.  # noqa: E501

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug used for filtering the QuerySet.

        Returns
        -------
        HoaDonMuaHangTrongNuocModelQueryset
            A QuerySet of HoaDonMuaHangTrongNuocModel with applied filters.
        """
        qs = self.get_queryset()
        return qs.filter(entity_model__slug__exact=entity_slug)


class HoaDonMuaHangTrongNuocModelAbstract(BillModel):
    """
    This is the main abstract class which the HoaDonMuaHangTrongNuocModel database will inherit from.  # noqa: E501
    The HoaDonMuaHangTrongNuocModel inherits functionality from BillModel, which provides:  # noqa: E501

    1. Ledger management
    2. Journal entry creation
    3. Transaction management
    4. Payment tracking
    5. Status management

    This inheritance allows the HoaDonMuaHangTrongNuocModel to leverage the accounting workflow  # noqa: E501
    for bills, including journal entries, transactions, and ledger management.

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    entity_model : ForeignKey
        The EntityModel that this invoice belongs to.
    hdmh_yn : BooleanField
        Flag indicating if this is a purchase invoice.
    pn_yn : BooleanField
        Flag indicating if this is a receipt note.
    pc_tao_yn : BooleanField
        Flag indicating if a payment voucher has been created.
    ma_httt : CharField
        Payment method code.
    xt_yn : BooleanField
        Flag indicating if this has been processed.
    loai_ck : CharField
        Discount type.
    ck_tl_nt : DecimalField
        Discount percentage in foreign currency.
    ma_ngv : CharField
        Shipper code.
    ma_kh : CharField
        Customer (vendor) code.
    ten_kh : CharField
        Customer (vendor) name.
    ong_ba : CharField
        Contact person.
    ma_nvmh : CharField
        Purchasing staff code.
    e_mail : CharField
        Email.
    tk : CharField
        Account code.
    ma_tt : CharField
        Payment status code.
    dien_giai : TextField
        Description.
    unit_id : IntegerField
        Unit ID.
    i_so_ct : CharField
        Internal document number.
    ma_nk : CharField
        Warehouse code.
    so_ct : CharField
        Document number.
    ngay_ct : DateField
        Document date.
    ngay_lct : DateField
        Accounting date.
    so_ct0 : CharField
        Original document number.
    ngay_ct0 : DateField
        Original document date.
    so_ct2 : CharField
        Secondary document number.
    ma_nk_pn : CharField
        Receipt note warehouse code.
    so_pn : CharField
        Receipt note number.
    ngay_pn : DateField
        Receipt note date.
    i_so_pn : CharField
        Internal receipt note number.
    ma_nt : CharField
        Currency code.
    ty_gia : DecimalField
        Exchange rate.
    status : CharField
        Status.
    transfer_yn : BooleanField
        Flag indicating if this has been transferred.
    ma_gd : CharField
        Transaction code.
    pc_ngay_ct : DateField
        Payment voucher date.
    pc_ma_ct : CharField
        Payment voucher document code.
    pc_ma_nk : CharField
        Payment voucher warehouse code.
    pc_tknh : CharField
        Payment voucher bank account.
    pc_tk : CharField
        Payment voucher account.
    pc_t_tt_nt : DecimalField
        Payment voucher total in foreign currency.
    nguoi_tao : CharField
        Creator.
    ngay_tao : DateTimeField
        Creation date.
    """

    # Note: uuid field is inherited from BillModel
    # We're using entity_model from BillModel's ledger relationship

    # Invoice flags
    entity_model = models.ForeignKey(
        "django_ledger.EntityModel",
        on_delete=models.CASCADE,
        verbose_name=_("Entity Model"),
        related_name="hoa_don_mua_hang_trong_nuoc",
    )
    hdmh_yn = models.BooleanField(verbose_name=_("HDMH Yes/No"))
    pn_yn = models.BooleanField(verbose_name=_("PN Yes/No"))
    pc_tao_yn = models.BooleanField(verbose_name=_("PC Tạo Yes/No"))
    ma_httt = models.CharField(max_length=10, verbose_name=_("Mã HTTT"))
    xt_yn = models.BooleanField(verbose_name=_("XT Yes/No"))
    loai_ck = models.CharField(max_length=5, verbose_name=_("Loại CK"))
    ck_tl_nt = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("CK TL NT")
    )

    # Vendor information
    ma_ngv = models.CharField(max_length=20, verbose_name=_("Mã NGV"))
    ten_kh = models.CharField(max_length=255, verbose_name=_("Tên KH"))
    ong_ba = models.CharField(max_length=255, verbose_name=_("Ông/Bà"))
    ma_nvmh = models.ForeignKey(
        "django_ledger.NhanVienModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã NVMH"),
        related_name="hoa_don_mua_hang_trong_nuoc",
    )
    e_mail = models.CharField(max_length=255, verbose_name=_("Email"))
    # Account information
    tk = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        verbose_name=_("Tài khoản"),
        related_name="hoa_don_mua_hang_trong_nuoc",
    )
    # ten_tk = models.CharField(max_length=20, verbose_name=_("Tài khoản"))  # Field removed in migration 0041
    ma_tt = models.ForeignKey(
        "django_ledger.HanThanhToanModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã TT"),
        related_name="hoa_don_mua_hang_trong_nuoc",
    )
    # ten_tt = models.CharField(max_length=10, verbose_name=_("Mã TT"))  # Field removed in migration
    dien_giai = models.TextField(verbose_name=_("Diễn giải"))
    # Document information
    unit_id = models.ForeignKey(
        "django_ledger.EntityUnitModel",
        on_delete=models.CASCADE,
        verbose_name=_("Unit ID"),
        related_name="hoa_don_mua_hang_trong_nuoc",
    )
    i_so_ct = models.CharField(max_length=20, verbose_name=_("Số CT"))
    ma_nk = models.ForeignKey(
        "django_ledger.QuyenChungTu",
        on_delete=models.CASCADE,
        verbose_name=_("Mã NK"),
        related_name="hoa_don_mua_hang_trong_nuoc",
    )
    ten_nk = models.CharField(max_length=20, verbose_name=_("Mã NK"))
    so_ct = models.CharField(
        max_length=50,
        verbose_name=_("Số chứng từ"),
        help_text=_("Document number - manual input"),
    )
    ngay_ct = models.DateField(verbose_name=_("Ngày chứng từ"))
    ngay_lct = models.DateField(verbose_name=_("Ngày LCT"))
    so_ct0 = models.CharField(
        max_length=50,
        verbose_name=_("Số CT0"),
        help_text=_("Original document number - manual input"),
        blank=True,
    )
    ngay_ct0 = models.DateField(verbose_name=_("Ngày CT0"))
    so_ct2 = models.CharField(
        max_length=50,
        verbose_name=_("Số CT2"),
        help_text=_("Secondary document number - manual input"),
        blank=True,
    )

    # Receipt note information
    ma_nk_pn = models.ForeignKey(
        "django_ledger.QuyenChungTu",
        on_delete=models.CASCADE,
        verbose_name=_("Mã NK PN"),
        related_name="hoa_don_mua_hang_trong_nuoc_pn",
        blank=True,
        null=True,
    )
    ten_nk_pn = models.CharField(max_length=20, blank=True, verbose_name=_("Mã NK PN"))
    so_pn = models.CharField(max_length=50, verbose_name=_("Số PN"))
    ngay_pn = models.DateField(verbose_name=_("Ngày PN"))
    i_so_pn = models.CharField(max_length=20, verbose_name=_("Số PN"), blank=True)
    # Currency information
    ma_nt = models.ForeignKey(
        "django_ledger.NgoaiTeModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã NT"),
        related_name="hoa_don_mua_hang_trong_nuoc",
    )
    ty_gia = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Tỷ giá")
    )

    # Status information
    status = models.CharField(max_length=5, verbose_name=_("Trạng thái"))
    transfer_yn = models.BooleanField(verbose_name=_("Transfer Yes/No"))
    # Payment information
    ma_gd = models.CharField(max_length=10, verbose_name=_("Mã GD"))
    pc_ngay_ct = models.DateField(verbose_name=_("Ngày CT PC"), blank=True, null=True)
    pc_ma_ct = models.CharField(max_length=20, verbose_name=_("Mã CT PC"), blank=True)
    pc_ma_nk = models.CharField(max_length=20, verbose_name=_("Mã NK PC"), blank=True)
    pc_tknh = models.CharField(max_length=20, blank=True, verbose_name=_("TK NH PC"))
    pc_tk = models.CharField(max_length=20, verbose_name=_("TK PC"), blank=True)
    pc_t_tt_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("PC TT NT"),
        blank=True,
        null=True,
    )

    # Audit information
    nguoi_tao = models.CharField(max_length=50, verbose_name=_("Người tạo"), blank=True)
    ngay_tao = models.DateTimeField(verbose_name=_("Ngày tạo"), auto_now_add=True)
    objects = HoaDonMuaHangTrongNuocModelManager()

    class Meta:
        abstract = True
        verbose_name = _("Hóa đơn mua hàng trong nước")
        verbose_name_plural = _("Hóa đơn mua hàng trong nước")
        indexes = [
            models.Index(fields=["entity_model"]),
        ]

    def __str__(self):  # noqa: C901
        return f"{self.so_ct} - {self.ten_kh}"


class HoaDonMuaHangTrongNuocModel(HoaDonMuaHangTrongNuocModelAbstract):
    """
    Base HoaDonMuaHangTrongNuocModel from Abstract.
    """

    def save(self, *args, **kwargs):
        """
        Override save method to handle ChungTu processing properly.
        """
        # Check if we should skip ChungTu processing
        skip_chung_tu_processing = kwargs.pop('skip_chung_tu_processing', False)

        if skip_chung_tu_processing:
            # Use Django's default save method, bypassing ChungTuMixin
            from django.db import models
            models.Model.save(self, *args, **kwargs)
        else:
            # Use the parent's save method (includes ChungTuMixin)
            super().save(*args, **kwargs)

    class Meta(HoaDonMuaHangTrongNuocModelAbstract.Meta):
        abstract = False
        db_table = "hoa_don_mua_hang_trong_nuoc"
