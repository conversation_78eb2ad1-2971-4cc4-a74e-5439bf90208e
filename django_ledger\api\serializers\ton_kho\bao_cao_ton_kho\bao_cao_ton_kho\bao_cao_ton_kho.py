"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bao Cao Ton Kho (Inventory Report).

These serializers handle request validation and response formatting for the
inventory report API, ensuring data integrity and consistent responses.
"""

from rest_framework import serializers
from datetime import date


class BaoCaoTonKhoRequestSerializer(serializers.Serializer):
    """
    Serializer for Bao Cao Ton Kho request parameters.
    Validates all filter parameters from the original cURL request.

    Based on enterprise ERP experience, this includes comprehensive filtering
    capabilities for inventory reporting with current stock balance calculations.
    """

    # Report date (required) - using ngay_ct1 as the report date
    ngay_ct1 = serializers.DateField(
        required=True,
        help_text="Report date for inventory balance calculation (YYYY-MM-DD)"
    )

    # Warehouse and material filters
    ma_kho = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Warehouse UUID filter (null for all warehouses)"
    )
    ma_vt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material UUID filter (null for all materials)"
    )

    # Location and batch filters
    ma_lo = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Lot/batch UUID filter (null for all lots)"
    )
    ma_vi_tri = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Location UUID filter (null for all locations)"
    )

    # Material category and grouping
    ma_lvt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material category UUID filter (null for all categories)"
    )
    ton_kho_yn = serializers.BooleanField(
        required=False,
        default=True,
        help_text="Include only inventory-tracked materials"
    )

    # Material group filters
    nh_vt1 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 1 UUID filter (null for all)"
    )
    nh_vt2 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 2 UUID filter (null for all)"
    )
    nh_vt3 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 3 UUID filter (null for all)"
    )

    # Unit and reporting parameters
    ma_unit = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Unit UUID filter (null for all units)"
    )
    loai_du_lieu = serializers.IntegerField(
        required=False,
        default=1,
        help_text="Data type (1=Standard)"
    )
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )
    dvt = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True,
        help_text="List of unit of measure UUIDs filter (empty list for all UOMs)"
    )
    group_by = serializers.IntegerField(
        required=False,
        allow_null=True,
        help_text="Group by option: blank=none, 1=loai_vt, 2=nh_vt1, 3=nh_vt2, 4=nh_vt3, 5=tk_vt"
    )
    data_analysis_struct = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Data analysis structure"
    )



    def validate(self, data):
        """
        Validate the request parameters and business rules.
        """
        ngay_ct1 = data.get('ngay_ct1')

        # Validate report date is not in the future
        if ngay_ct1 and ngay_ct1 > date.today():
            raise serializers.ValidationError(
                "Report date cannot be in the future"
            )

        # Validate report date is not too old (performance consideration)
        if ngay_ct1:
            date_diff = (date.today() - ngay_ct1).days
            if date_diff > 1095:  # 3 years
                raise serializers.ValidationError(
                    "Report date cannot be more than 3 years old for performance reasons"
                )

        return data


class BaoCaoTonKhoResponseSerializer(serializers.Serializer):
    """
    Serializer for Bao Cao Ton Kho response data.
    Defines all fields that should be returned in the inventory report.

    This matches the exact field specification from the original system
    to ensure compatibility and consistent reporting.
    """

    stt = serializers.IntegerField(help_text="Sequential number")
    ma_kho = serializers.CharField(max_length=50, help_text="Warehouse code")
    ma_vt = serializers.CharField(max_length=50, help_text="Material code")
    nhom = serializers.CharField(max_length=100, help_text="Material group/category")
    dvt = serializers.CharField(max_length=50, help_text="Unit of measure")

    # Current balance (as of report date)
    ton_cuoi = serializers.DecimalField(
        max_digits=15, decimal_places=3, help_text="Current balance quantity"
    )
    du_cuoi = serializers.DecimalField(
        max_digits=15, decimal_places=2, help_text="Current balance amount"
    )

    # Descriptive fields
    ten_vt = serializers.CharField(max_length=255, help_text="Material name")
    ten_kho = serializers.CharField(max_length=255, help_text="Warehouse name")
