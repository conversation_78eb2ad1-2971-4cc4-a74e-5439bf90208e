"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapKhoService, which handles business logic
for the PhieuNhapKhoModel.
"""

import logging
from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.ton_kho.nhap_kho_noi_bo.phieu_nhap_kho import (  # noqa: F401,
    PhieuNhapKhoModel,
)
from django_ledger.repositories.ton_kho.nhap_kho_noi_bo.phieu_nhap_kho import (  # noqa: F401,
    PhieuNhapKhoRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.services.ton_kho.nhap_kho_noi_bo.phieu_nhap_kho.chi_tiet_phieu_nhap_kho import (  # noqa: F401,
    ChiTietPhieuNhapKhoService,
)

from django_ledger.utils_new.debt_management import CongNoCreation

class PhieuNhapKhoService(BaseService):
    """
    Service class for PhieuNhapKhoModel.
    Handles business logic for the model.
    """

    WAREHOUSE_RECEIPT_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'NHAP',                 # Nhập kho
            'debit_account_field': 'tk_vt',         # Tài khoản tiền - DEBIT
            'credit_account_field': 'tk_du',        # Tài khoản đối ứng - CREDIT
            'debit_account_source': 'detail',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'tien_nt',                 # Số tiền (detail)
            'detail_source': 'chi_tiet',            # Related name to detail (ChiTietPhieuNhapKhoModel)
            'canCreate': True                       # Default: always create entry
        }
    ]

    def __init__(self):
        super().__init__()
        self.repository = PhieuNhapKhoRepository()
        self.chi_tiet_service = ChiTietPhieuNhapKhoService()
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(self, phieu_nhap_kho: PhieuNhapKhoModel) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định mapping kế toán dựa trên ma_ngv và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Luôn tạo bút toán (theo yêu cầu) cho phiếu nhập kho
        - Support flexible business rules cho future enhancements

        Parameters
        ----------
        phieu_nhap_kho : PhieuNhapKhoModel
            Phiếu nhập kho để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với journal_type và canCreate được set
        """
        mappings = self.WAREHOUSE_RECEIPT_ACCOUNTING_CONFIG.copy()

        if hasattr(phieu_nhap_kho, 'pk') and phieu_nhap_kho.pk and hasattr(phieu_nhap_kho, 'chi_tiet'):
            try:
                phieu_status = getattr(phieu_nhap_kho, 'status', None)
                # Status 5: Đã duyệt, 8: Đã thanh toán
                if phieu_status not in ["5", "8"]:
                    for mapping in mappings:
                        mapping['canCreate'] = False
                else:
                    chi_tiet_list = phieu_nhap_kho.chi_tiet.all()
                    if chi_tiet_list.exists():
                        has_valid_entries = any(
                            getattr(ct, 'tien_nt', 0) > 0 and
                            getattr(ct, 'tk_du', None) is not None
                            for ct in chi_tiet_list
                        )

                        if not has_valid_entries:
                            for mapping in mappings:
                                mapping['canCreate'] = False
            except Exception as e:
                raise

        return mappings

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[PhieuNhapKhoModel]:  # noqa: C901
        """
        Retrieves a PhieuNhapKhoModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapKhoModel to retrieve.

        Returns
        -------
        Optional[PhieuNhapKhoModel]
            The PhieuNhapKhoModel with the given UUID, or None if not found.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists PhieuNhapKhoModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuNhapKhoModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuNhapKhoModel:  # noqa: C901
        """
        Creates a new PhieuNhapKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuNhapKhoModel.

        Returns
        -------
        PhieuNhapKhoModel
            The created PhieuNhapKhoModel instance.
        """
        # Process related data
        chi_tiet = data.pop('chi_tiet', [])
        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)
        # Create the PhieuNhapKhoModel instance
        instance = self.repository.create(entity_slug=entity_slug, data=data)
        # Process related data
        if chi_tiet:
            for chi_tiet_data in chi_tiet:
                self.chi_tiet_service.create(
                    phieu_nhap_kho_id=instance.uuid, data=chi_tiet_data
                )

        self._cong_no_service.create_document_accounting_entries(
            source_document=instance,
            document_type="phiếu nhập kho",
            account_mappings=self._determine_accounting_mappings(instance)
        )

        return instance

    @transaction.atomic
    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[PhieuNhapKhoModel]:  # noqa: C901
        """
        Updates an existing PhieuNhapKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapKhoModel to update.
        data : Dict[str, Any]
            The data to update the PhieuNhapKhoModel with.

        Returns
        -------
        Optional[PhieuNhapKhoModel]
            The updated PhieuNhapKhoModel instance, or None if not found.
        """
        # Process related data
        chi_tiet = data.pop('chi_tiet', [])
        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)
        # Update the PhieuNhapKhoModel instance
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        # Process related data - use complete replacement approach for PUT requests
        if instance and chi_tiet is not None:  # Check for None to allow empty list
            # For PUT requests, we completely replace all existing detail items
            # Step 1: Delete ALL existing chi_tiet items using bulk delete service method
            self.chi_tiet_service.delete_all_for_phieu_nhap_kho(
                phieu_nhap_kho_id=uuid
            )

            # Step 2: Create NEW chi_tiet items from the request data
            for chi_tiet_data in chi_tiet:
                # Remove UUID from data to ensure new creation (ignore any UUIDs in request)
                create_data = chi_tiet_data.copy()
                create_data.pop('uuid', None)
                self.chi_tiet_service.create(
                    phieu_nhap_kho_id=instance.uuid,
                    data=create_data,
                )

        # Refresh the instance from database to get updated data
        if instance:
            instance = self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

        if instance:
            self._cong_no_service.update_document_accounting_entries(
                source_document=instance,
                document_type="phiếu nhập kho",
                account_mappings=self._determine_accounting_mappings(instance)
            )

        return instance

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuNhapKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapKhoModel to delete.

        Returns
        -------
        bool
            True if the PhieuNhapKhoModel was deleted, False otherwise.
        """
        # Check if the instance exists
        instance = self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return False

        # First delete all related chi_tiet records to avoid cascade delete deadlock
        self.chi_tiet_service.delete_all_for_phieu_nhap_kho(
            phieu_nhap_kho_id=uuid
        )

        # Use repository bulk delete method to bypass model validation
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    def get_chi_tiet_data(
        self, phieu_nhap_kho_uuid: Optional[Union[str, UUID]] = None
    ) -> Dict[str, List]:  # noqa: C901
        """
        Get child data for serializer use.
        Instead of letting serializer query DB directly, service will fetch data and provide it to serializer.  # noqa: E501

        Args:
            phieu_nhap_kho_uuid: UUID of the parent object (if any)

        Returns:
            Dict[str, List]: Dictionary with parent UUID as key, list of child objects as value  # noqa: E501
        """
        result = {}
        if phieu_nhap_kho_uuid:
            # Case: Get data for a specific parent object
            children = self.chi_tiet_service.list_for_phieu_nhap_kho(
                phieu_nhap_kho_id=phieu_nhap_kho_uuid
            )
            result[str(phieu_nhap_kho_uuid)] = children
        else:
            pass

        return result

    def get_auto_generated_receipt_invoice_ids(
        self, ma_nk: str
    ) -> List[str]:  # noqa: C901
        """
        Get invoice IDs that already have auto-generated receipts with the specified ma_nk.

        Parameters
        ----------
        ma_nk : str
            Document book code to filter receipts by.

        Returns
        -------
        List[str]
            List of invoice UUIDs that already have auto-generated receipts.
        """
        auto_generated_receipt_invoice_ids = self.repository.model_class.objects.filter(
            tu_dong_tao=True, nguon_hoa_don_id__isnull=False, ma_nk=ma_nk
        ).values_list('nguon_hoa_don_id', flat=True)

        # Convert to string UUIDs for comparison
        return [str(uuid_val) for uuid_val in auto_generated_receipt_invoice_ids]