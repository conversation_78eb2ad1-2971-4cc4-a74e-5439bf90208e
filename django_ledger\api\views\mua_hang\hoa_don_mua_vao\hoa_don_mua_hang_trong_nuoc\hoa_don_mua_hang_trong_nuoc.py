"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Mua Hang Trong Nuoc (Domestic Purchase Invoice) view implementation.
"""

from rest_framework import status, viewsets  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiTietHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    HoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.thue_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ThueHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocService,
)


class HoaDonMuaHangTrongNuocModelViewSet(viewsets.ModelViewSet):
    """
    ViewSet for the HoaDonMuaHangTrongNuocModel.
    """

    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"
    http_method_names = [
        "get",
        "post",
        "put",
        "patch",
        "delete",
        "head",
        "options",
    ]
    # No need to register custom actions here, they are registered with @action decorators  # noqa: E501

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = HoaDonMuaHangTrongNuocService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the view.

        Returns
        -------
        QuerySet
            The queryset for the view.
        """
        entity_slug = self.kwargs.get("entity_slug")
        return self.service.get_all(entity_slug)

    def get_serializer_class(self):  # noqa: C901
        """
        Get the serializer class for the view.

        Returns
        -------
        Serializer
            The serializer class for the view.
        """
        if self.action in ["create", "update", "partial_update"]:
            return HoaDonMuaHangTrongNuocModelCreateUpdateSerializer
        return HoaDonMuaHangTrongNuocModelSerializer

    def get_serializer_context(self):  # noqa: C901
        """
        Get the serializer context for the view.

        Returns
        -------
        dict
            The serializer context for the view.
        """
        context = super().get_serializer_context()
        context["entity_slug"] = self.kwargs.get("entity_slug")
        return context

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List all HoaDonMuaHangTrongNuocModel instances for a specific entity.

        Parameters
        ----------
        request : Request
            The request object.
        args : tuple
            Additional arguments.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get("entity_slug")
        # Get filters from request
        status_filter = request.query_params.get("status")
        search_query = request.query_params.get("q")
        include_details = (
            request.query_params.get("include_details", "true").lower() == "true"
        )

        # Use service to get filtered data
        queryset = self.service.get_filtered_data(
            entity_slug=entity_slug,
            status_filter=status_filter,
            search_query=search_query,
        )

        # Paginate results
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = serializer.data
            # Include details if requested
            if include_details:
                for i, invoice in enumerate(response_data):
                    invoice_uuid = page[i].uuid
                    details = self.service.get_invoice_details(invoice_uuid)
                    # Add chi_tiet_hoa_don details
                    if details.get("chi_tiet"):
                        chi_tiet_serializer = (
                            ChiTietHoaDonMuaHangTrongNuocModelSerializer(
                                details["chi_tiet"], many=True
                            )
                        )
                        invoice["chi_tiet_hoa_don"] = chi_tiet_serializer.data
                    else:
                        invoice["chi_tiet_hoa_don"] = []
                    # Add chi_phi_hoa_don details
                    if details.get("chi_phi"):
                        chi_phi_serializer = (
                            ChiPhiHoaDonMuaHangTrongNuocModelSerializer(
                                details["chi_phi"], many=True
                            )
                        )
                        invoice["chi_phi_hoa_don"] = chi_phi_serializer.data
                    else:
                        invoice["chi_phi_hoa_don"] = []
                    # Add chi_phi_chi_tiet_hoa_don details
                    if details.get("chi_phi_chi_tiet"):
                        chi_phi_chi_tiet_serializer = (
                            ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer(
                                details["chi_phi_chi_tiet"], many=True
                            )
                        )
                        invoice["chi_phi_chi_tiet_hoa_don"] = (
                            chi_phi_chi_tiet_serializer.data
                        )
                    else:
                        invoice["chi_phi_chi_tiet_hoa_don"] = []
                    # Add thue_hoa_don details
                    if details.get("thue"):
                        thue_serializer = ThueHoaDonMuaHangTrongNuocModelSerializer(
                            details["thue"], many=True
                        )
                        invoice["thue_hoa_don"] = thue_serializer.data
                    else:
                        invoice["thue_hoa_don"] = []
            return self.get_paginated_response(response_data)

        serializer = self.get_serializer(queryset, many=True)
        response_data = serializer.data
        return Response(response_data)

    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new HoaDonMuaHangTrongNuocModel instance with details.

        Parameters
        ----------
        request : Request
            The request object.
        args : tuple
            Additional arguments.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get("entity_slug")
        # Validate data using serializer
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        try:
            # Use service to create instance with details
            instance, created_details = self.service.create_with_details(
                entity_slug=entity_slug, data=serializer.validated_data
            )

            # Return the created instance and details
            response_serializer = HoaDonMuaHangTrongNuocModelSerializer(instance)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:

            return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        args : tuple
            Additional arguments.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        # Validate data using serializer
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        # Use service to update instance
        updated_instance = self.service.update(
            uuid=instance.uuid, data=serializer.validated_data
        )

        # Return the updated instance
        response_serializer = HoaDonMuaHangTrongNuocModelSerializer(updated_instance)
        return Response(response_serializer.data)

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a HoaDonMuaHangTrongNuocModel instance with all its related details.

        Parameters
        ----------
        request : Request
            The request object.
        args : tuple
            Additional arguments.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object containing the invoice with its details.
        """
        instance = self.get_object()
        # Get the details using service
        details = self.service.get_invoice_details(instance.uuid)
        # Serialize the main instance
        serializer = self.get_serializer(instance)
        response_data = serializer.data
        # Serialize the details and add them to the response
        if details:
            # Add chi_tiet_hoa_don details
            if details.get("chi_tiet"):
                chi_tiet_serializer = ChiTietHoaDonMuaHangTrongNuocModelSerializer(
                    details["chi_tiet"], many=True
                )
                response_data["chi_tiet_hoa_don"] = chi_tiet_serializer.data
            # Add chi_phi_hoa_don details
            if details.get("chi_phi"):
                chi_phi_serializer = ChiPhiHoaDonMuaHangTrongNuocModelSerializer(
                    details["chi_phi"], many=True
                )
                response_data["chi_phi_hoa_don"] = chi_phi_serializer.data
            # Add chi_phi_chi_tiet_hoa_don details
            if details.get("chi_phi_chi_tiet"):
                chi_phi_chi_tiet_serializer = (
                    ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer(
                        details["chi_phi_chi_tiet"], many=True
                    )
                )
                response_data["chi_phi_chi_tiet_hoa_don"] = (
                    chi_phi_chi_tiet_serializer.data
                )

            # Add thue_hoa_don details
            if details.get("thue"):
                thue_serializer = ThueHoaDonMuaHangTrongNuocModelSerializer(
                    details["thue"], many=True
                )
                response_data["thue_hoa_don"] = thue_serializer.data
        return Response(response_data)

    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        args : tuple
            Additional arguments.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        instance = self.get_object()
        self.service.delete(instance.uuid)
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=["get"])
    def chi_tiet(self, request, **kwargs):  # noqa: C901
        """
        Get the details of a HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        instance = self.get_object()
        # Get the details using service
        details = self.service.get_invoice_details(instance.uuid)
        # Serialize the details
        chi_tiet_serializer = ChiTietHoaDonMuaHangTrongNuocModelSerializer(
            details["chi_tiet"], many=True
        )
        chi_phi_serializer = ChiPhiHoaDonMuaHangTrongNuocModelSerializer(
            details["chi_phi"], many=True
        )
        chi_phi_chi_tiet_serializer = (
            ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer(
                details["chi_phi_chi_tiet"], many=True
            )
        )
        thue_serializer = ThueHoaDonMuaHangTrongNuocModelSerializer(
            details["thue"], many=True
        )

        # Return the details
        return Response(
            {
                "chi_tiet_hoa_don": chi_tiet_serializer.data,
                "chi_phi_hoa_don": chi_phi_serializer.data,
                "chi_phi_chi_tiet_hoa_don": chi_phi_chi_tiet_serializer.data,
                "thue_hoa_don": thue_serializer.data,
            }
        )

    @action(detail=False, methods=["post"])
    def create_with_ledger(self, request, **kwargs):  # noqa: C901
        """
        Create a new HoaDonMuaHangTrongNuocModel with a ledger but without items.

        This endpoint implements the first step of the workflow:
        1. Create the bill with an empty items list
        2. Create the ledger and attach the bill_id to it

        Parameters
        ----------
        request : Request
            The request object containing the invoice data without items.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object containing the created invoice with its ledger.
        """
        entity_slug = self.kwargs.get("entity_slug")
        # Validate data using serializer
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        try:
            # Use service to create invoice with ledger
            instance = self.service.create_with_ledger(
                entity_slug=entity_slug, data=serializer.validated_data
            )

            # Return the created instance
            response_serializer = HoaDonMuaHangTrongNuocModelSerializer(instance)
            return Response(
                {
                    "message": "Invoice created with ledger successfully",
                    "invoice": response_serializer.data,
                    "ledger_uuid": str(instance.ledger.uuid),
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:

            return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def add_items(self, request, **kwargs):  # noqa: C901
        """
        Add items to an existing HoaDonMuaHangTrongNuocModel instance.

        This endpoint implements the second step of the workflow:
        3. Process transactions and journal entries
        4. Map the items back to the ledger

        Parameters
        ----------
        request : Request
            The request object containing the items to add.
            Expected format:
            {
                "chi_tiet_hoa_don": [...],  # Required
                "thue_hoa_don": [...],      # Optional
                "chi_phi_hoa_don": [...],   # Optional
                "chi_phi_chi_tiet_hoa_don": [...]    # Optional
            }
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object containing the added items.
        """
        instance = self.get_object()
        entity_slug = self.kwargs.get("entity_slug")
        # Extract items data from request
        items_data = {
            "chi_tiet_data": request.data.get("chi_tiet_hoa_don", []),
            "thue_data": request.data.get("thue_hoa_don", []),
            "chi_phi_data": request.data.get("chi_phi_hoa_don", []),
            "chi_phi_chi_tiet_data": request.data.get("chi_phi_chi_tiet_hoa_don", []),
        }

        # Validate that chi_tiet_data is provided
        if not items_data["chi_tiet_data"]:
            return Response(
                {"error": "chi_tiet_hoa_don is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Use service to add items and process accounting
            result = self.service.add_items_and_process_accounting(
                entity_slug=entity_slug,
                uuid=instance.uuid,
                items_data=items_data,
            )

            # Return the result
            return Response(
                {
                    "message": "Items added and accounting processed successfully",
                    "added_items": result["added_items"],
                    "journal_entry_uuid": result["journal_entry_uuid"],
                    "transaction_count": result["transaction_count"],
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:

            return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def process_accounting(self, request, **kwargs):  # noqa: C901
        """
        Process the accounting workflow for a HoaDonMuaHangTrongNuocModel instance.

        This endpoint processes the accounting workflow for an invoice:
        1. Creates a ledger if one doesn't exist
        2. Creates a journal entry with transactions
        3. Posts the journal entry
        4. Posts the ledger
        5. Updates the invoice status

        Parameters
        ----------
        request : Request
            The request object.
            Optional parameters:
            {
                "description": "Custom journal entry description"
            }
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object containing the processing results.
        """
        instance = self.get_object()
        entity_slug = self.kwargs.get("entity_slug")
        description = request.data.get("description")
        try:
            # Use service to process accounting
            result = self.service.process_accounting(
                entity_slug=entity_slug,
                uuid=instance.uuid,
                description=description,
            )

            # Return the result
            return Response(
                {
                    "message": "Accounting processed successfully",
                    "hoa_don_uuid": result["hoa_don_uuid"],
                    "hoa_don_so_ct": result["hoa_don_so_ct"],
                    "ledger_uuid": result["ledger_uuid"],
                    "journal_entry_uuid": result["journal_entry_uuid"],
                    "transaction_count": result["transaction_count"],
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:

            return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["get"])
    def search(self, request, **kwargs):  # noqa: C901
        """
        Search for HoaDonMuaHangTrongNuocModel instances.

        Parameters
        ----------
        request : Request
            The request object.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get("entity_slug")
        query = request.query_params.get("q", "")
        # Use service to search for invoices
        queryset = self.service.search(entity_slug, query)
        # Paginate results
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
