# In g:\Code\erp2\erp-be\django_ledger\api\views\ngan_sach\cap_nhat_ngan_sach\cap_nhat_ngan_sach.py  # noqa: E501
"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright EDMA Group Inc licensed under the GPLv3 Agreement.

API views for CapNhatNganSach model.
"""
import logging  # noqa: F401

from django.core.exceptions import ObjectDoesNotExist  # noqa: F401
from django.db import transaction  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,
from drf_spectacular.utils import (  # noqa: F401,
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status, viewsets  # noqa: F401,
from rest_framework.decorators import action  # noqa: F401,
from rest_framework.exceptions import ValidationError  # noqa: F401,
from rest_framework.response import Response  # noqa: F401,

from django_ledger.api.serializers.ngan_sach.cap_nhat_ngan_sach.cap_nhat_ngan_sach import (  # noqa: F401,
    CapNhatNganSachSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models import CapNhatNganSachModel  # noqa: F401,
from django_ledger.services.ngan_sach.cap_nhat_ngan_sach.cap_nhat_ngan_sach import (  # noqa: F401,
    CapNhatNganSachService,
)
from django_ledger.api.decorators.error_handling import api_exception_handler

logger = logging.getLogger(__name__)


@extend_schema_view(
    list=extend_schema(tags=["Cập nhật ngân sách"]),
    create=extend_schema(tags=["Cập nhật ngân sách"]),
    retrieve=extend_schema(tags=["Cập nhật ngân sách"]),
    update=extend_schema(tags=["Cập nhật ngân sách"]),
    partial_update=extend_schema(tags=["Cập nhật ngân sách"]),
    destroy=extend_schema(tags=["Cập nhật ngân sách"]),
)
class CapNhatNganSachViewSet(EntityRelatedViewSet):
    """
    A ViewSet for CapNhatNganSach model.
    """

    serializer_class = CapNhatNganSachSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = CapNhatNganSachService()

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List CapNhatNganSach instances.
        """
        entity_slug = self.kwargs['entity_slug']
        # Get query parameters
        search_query = request.query_params.get('search', None)
        status_param = request.query_params.get('status', None)
        nam = request.query_params.get('nam', None)
        ma_bp = request.query_params.get('ma_bp', None)
        ma_vv = request.query_params.get('ma_vv', None)
        ma_dv = request.query_params.get(
            'ma_dv', None
        )  # New parameter for entity unit
        # Get the queryset from the service
        instances = self.service.list(
            entity_slug=entity_slug,
            user_model=self.request.user,
            search_query=search_query,
            status=status_param,
            nam=int(nam) if nam and nam.isdigit() else None,
            ma_bp=ma_bp,
            ma_vv=ma_vv,
            ma_dv=ma_dv,  # Pass the entity unit filter to service
        )

        page = self.paginate_queryset(instances)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    def get_serializer_context(self):  # noqa: C901
        """
        Extra context provided to the serializer class.
        """
        context = super().get_serializer_context()
        context['entity_slug'] = self.kwargs['entity_slug']
        return context

    @api_exception_handler
    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a CapNhatNganSach instance with its chi_tiet.
        """
        entity_slug = self.kwargs['entity_slug']
        uuid = self.kwargs['uuid']
        try:
            # Get instance with prefetched related data using service
            instance = self.service.get(entity_slug=entity_slug, uuid=uuid)
            # Serialize the instance
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except ObjectDoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new CapNhatNganSach instance with optional chi_tiet.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Validate data with context
        serializer = self.get_serializer(
            data=request.data,
            context={'entity_slug': entity_slug, 'request': request},
        )
        serializer.is_valid(raise_exception=True)
        # Extract validated data
        validated_data = serializer.validated_data
        # Extract chi_tiet data if present
        chi_tiet_data = validated_data.pop('chi_tiet', [])
        # Use service to create the instance with details in a single transaction
        instance, _ = self.service.create_with_details(
            entity_slug=entity_slug,
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
        )

        # Serialize response
        response_serializer = self.get_serializer(instance)
        # Return response
        return Response(
            response_serializer.data, status=status.HTTP_201_CREATED
        )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a CapNhatNganSach instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        uuid = kwargs['uuid']
        # Get instance
        instance = self.service.get(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Validate data
        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=kwargs.get('partial', False),
        )
        serializer.is_valid(raise_exception=True)
        # Extract validated data
        validated_data = serializer.validated_data
        # Extract chi_tiet data if present
        chi_tiet_data = validated_data.pop('chi_tiet', [])
        # Use service to update the instance with details in a single transaction
        updated_instance, _ = self.service.update_with_details(
            entity_slug=entity_slug,
            uuid=uuid,
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
        )

        # Serialize response
        response_serializer = self.get_serializer(updated_instance)
        # Return response
        return Response(response_serializer.data)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a CapNhatNganSach instance along with its related chi_tiet records.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        uuid = kwargs['uuid']
        try:
            # Use service to delete the instance with details in a single transaction
            self.service.delete(entity_slug=entity_slug, uuid=uuid)
            return Response(status=status.HTTP_204_NO_CONTENT)
        except ObjectDoesNotExist:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
