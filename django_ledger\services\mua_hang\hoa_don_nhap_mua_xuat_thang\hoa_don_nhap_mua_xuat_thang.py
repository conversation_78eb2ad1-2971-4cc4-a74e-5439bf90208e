"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

HoaDonNhapMuaXuatThang Service implementation.
"""

from typing import Any, Dict, List  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401

from django_ledger.models import EntityModel  # noqa: F401
from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    HoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories._utils.chung_tu_item_utils.chung_tu_item_utils import (  # noqa: F401
    update_instance_with_chung_tu_fields,
)
from django_ledger.repositories.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    HoaDonNhapMuaXuatThangRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401
from django_ledger.utils_new.debt_management import CongNoCreation


class HoaDonNhapMuaXuatThangService(BaseService):
    """
    Service class for HoaDonNhapMuaXuatThangModel.
    Provides business logic for HoaDonNhapMuaXuatThangModel operations.
    """

    RECEIPT_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'CHIPHIXT',  # Thu từ hóa đơn (ma_ngv=1)
            'debit_account_field': 'tk_cpxt',  # Tài khoản tiền - DEBIT
            'credit_account_field': 'tk_vt',  # Tài khoản đối ứng - CREDIT
            'debit_account_source': 'detail',  # Lấy debit account từ header
            'credit_account_source': 'detail',  # Lấy credit account từ detail
            'amount_field': 'tien0',  # Số tiền (detail)
            'detail_source': 'chi_tiet',  # Related name to detail (PhieuThuChiTiet)
            'canCreate': True,  # Default: always create entry
        },
        {
            'journal_type': 'CONGNO',  # Thu từ hóa đơn (ma_ngv=1)
            'debit_account_field': 'tk_vt',  # Tài khoản tiền - DEBIT
            'credit_account_field': 'tk',  # Tài khoản đối ứng - CREDIT
            'debit_account_source': 'header',  # Lấy debit account từ header
            'credit_account_source': 'detail',  # Lấy credit account từ detail
            'amount_field': 'tien0',  # Số tiền (detail)
            'detail_source': 'chi_tiet',  # Related name to detail (PhieuThuChiTiet)
            'canCreate': True,  # Default: always create entry
        },
    ]

    def _determine_accounting_mappings(
        self, HoaDonNhapMuaXuatThang: HoaDonNhapMuaXuatThangModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định mapping kế toán dựa trên ma_ngv và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional journal type dựa trên ma_ngv (1=THUHD, 2=THUKH)
        - Luôn tạo bút toán (theo yêu cầu)
        - Support flexible business rules cho future enhancements

        Parameters
        ----------
        phieu_thu : PhieuThuModel
            Phiếu thu để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với journal_type và canCreate được set
        """
        # Get base configuration
        mappings = self.get_accounting_configuration()

        # ✅ BUSINESS LOGIC: Luôn tạo bút toán (theo yêu cầu)
        for mapping in mappings:
            mapping['canCreate'] = True

        # ✅ QUY TẮC NGHIỆP VỤ BỔ SUNG: Kiểm tra điều kiện cơ bản
        # Chỉ kiểm tra validation nếu phieu_thu đã có ID (đã được save)
        if (
            hasattr(HoaDonNhapMuaXuatThang, 'pk')
            and HoaDonNhapMuaXuatThang.pk
            and hasattr(HoaDonNhapMuaXuatThang, 'chi_tiet')
        ):
            try:
                chi_tiet_list = HoaDonNhapMuaXuatThang.chi_tiet.all()
                # Chỉ validate nếu có children records
                if chi_tiet_list.exists():
                    has_valid_entries = any(
                        getattr(ct, 'tien0', 0) > 0
                        and getattr(ct, 'tk_cpxt', None)
                        is not None | getattr(ct, 'tk_vt', None)
                        is not None
                        for ct in chi_tiet_list
                    )

                    if not has_valid_entries:
                        for mapping in mappings:
                            mapping['canCreate'] = False
                # Nếu không có children records, vẫn cho phép tạo bút toán
                # (có thể là header-only hoặc children sẽ được tạo sau)
            except Exception as e:
                # Log error nhưng không fail - cho phép tạo bút toán
                pass

        return mappings

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the HoaDonNhapMuaXuatThangRepository and related services.
        """
        super().__init__()
        self.repository = HoaDonNhapMuaXuatThangRepository(
            model_class=HoaDonNhapMuaXuatThangModel
        )
        # Initialize related services
        from django_ledger.services.mua_hang.hoa_don_nhap_mua_xuat_thang import (
            ChiPhiChiTietHoaDonNhapMuaXuatThangService,
            ChiPhiHoaDonNhapMuaXuatThangService,
            ChiTietHoaDonNhapMuaXuatThangService,
            ThueHoaDonNhapMuaXuatThangService,
        )

        self.chi_tiet_service = ChiTietHoaDonNhapMuaXuatThangService()
        self.chi_phi_service = ChiPhiHoaDonNhapMuaXuatThangService()
        self.chi_phi_chi_tiet_service = ChiPhiChiTietHoaDonNhapMuaXuatThangService()
        self.thue_service = ThueHoaDonNhapMuaXuatThangService()
        self._cong_no_service = CongNoCreation()

    def get_by_id(self, uuid) -> HoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Retrieves a HoaDonNhapMuaXuatThangModel by its UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonNhapMuaXuatThangModel to retrieve.

        Returns
        -------
        HoaDonNhapMuaXuatThangModel
            The retrieved HoaDonNhapMuaXuatThangModel.
        """
        return self.repository.get_by_id(uuid)

    def list(
        self, entity_slug=None, user_model=None, **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Lists HoaDonNhapMuaXuatThangModel instances with optional filtering.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply to the queryset.

        Returns
        -------
        QuerySet
            A queryset of HoaDonNhapMuaXuatThangModel instances.
        """
        return self.repository.list(
            entity_slug=entity_slug, user_model=user_model, **kwargs
        )

    @transaction.atomic
    def create_invoice(
        self,
        entity_model,
        hoa_don_data,
        chi_tiet_data=None,
        chi_phi_data=None,
        chi_phi_chi_tiet_data=None,
        thue_data=None,
    ) -> HoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Creates a new HoaDonNhapMuaXuatThangModel instance with optional related data.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to associate with the HoaDonNhapMuaXuatThangModel.
        hoa_don_data : dict
            Data for creating the HoaDonNhapMuaXuatThangModel.
        chi_tiet_data : list, optional
            List of dictionaries containing data for creating ChiTietHoaDonNhapMuaXuatThangModel instances, by default None.
        chi_phi_data : list, optional
            List of dictionaries containing data for creating ChiPhiHoaDonNhapMuaXuatThangModel instances, by default None.
        chi_phi_chi_tiet_data : list, optional
            List of dictionaries containing data for creating ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances, by default None.
        thue_data : list, optional
            List of dictionaries containing data for creating ThueHoaDonNhapMuaXuatThangModel instances, by default None.

        Returns
        -------
        HoaDonNhapMuaXuatThangModel
            The created HoaDonNhapMuaXuatThangModel.
        """
        # Create the main invoice
        hoa_don = self.repository.create(entity_model=entity_model, **hoa_don_data)

        # Create related details if provided using services
        if chi_tiet_data:
            for item_data in chi_tiet_data:
                item_data['hoa_don'] = hoa_don
                self.chi_tiet_service.create(item_data)

        if chi_phi_data:
            for cost_data in chi_phi_data:
                cost_data['hoa_don'] = hoa_don
                self.chi_phi_service.create(cost_data)

        if chi_phi_chi_tiet_data:
            for detailed_cost_data in chi_phi_chi_tiet_data:
                detailed_cost_data['hoa_don'] = hoa_don
                self.chi_phi_chi_tiet_service.create(detailed_cost_data)

        if thue_data:
            for tax_data in thue_data:
                tax_data['hoa_don'] = hoa_don
                self.thue_service.create(tax_data)

        # tạo bút toán
        self._cong_no_service.create_document_accounting_entries(
            source_document=hoa_don,
            document_type="hóa đơn nhập mua xuất tháng",
            account_mappings=self._determine_accounting_mappings(hoa_don),
        )

        return hoa_don

    @transaction.atomic
    def update_invoice(
        self,
        uuid,
        hoa_don_data,
        chi_tiet_data=None,
        chi_phi_data=None,
        chi_phi_chi_tiet_data=None,
        thue_data=None,
    ) -> HoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Updates an existing HoaDonNhapMuaXuatThangModel instance with optional related data.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonNhapMuaXuatThangModel to update.
        hoa_don_data : dict
            Data for updating the HoaDonNhapMuaXuatThangModel.
        chi_tiet_data : list, optional
            List of dictionaries containing data for updating ChiTietHoaDonNhapMuaXuatThangModel instances, by default None.
        chi_phi_data : list, optional
            List of dictionaries containing data for updating ChiPhiHoaDonNhapMuaXuatThangModel instances, by default None.
        chi_phi_chi_tiet_data : list, optional
            List of dictionaries containing data for updating ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances, by default None.
        thue_data : list, optional
            List of dictionaries containing data for updating ThueHoaDonNhapMuaXuatThangModel instances, by default None.

        Returns
        -------
        HoaDonNhapMuaXuatThangModel
            The updated HoaDonNhapMuaXuatThangModel.
        """
        # Update the main invoice
        hoa_don = self.repository.update(uuid, **hoa_don_data)

        # Update related details if provided
        if chi_tiet_data is not None:
            # Clear existing and create new
            hoa_don.chi_tiet.all().delete()
            for item_data in chi_tiet_data:
                item_data['hoa_don'] = hoa_don
                self.chi_tiet_service.create(item_data)

        if chi_phi_data is not None:
            hoa_don.chi_phi.all().delete()
            for cost_data in chi_phi_data:
                cost_data['hoa_don'] = hoa_don
                self.chi_phi_service.create(cost_data)

        if chi_phi_chi_tiet_data is not None:
            hoa_don.chi_phi_vat_tu.all().delete()
            for detailed_cost_data in chi_phi_chi_tiet_data:
                detailed_cost_data['hoa_don'] = hoa_don
                self.chi_phi_chi_tiet_service.create(detailed_cost_data)

        if thue_data is not None:
            hoa_don.thue.all().delete()
            for tax_data in thue_data:
                tax_data['hoa_don'] = hoa_don
                self.thue_service.create(tax_data)

        if hoa_don.ledger:
            try:
                self._cong_no_service.update_document_accounting_entries(
                    source_document=hoa_don,
                    document_type="hóa đơn nhập mua xuất tháng",
                    account_mappings=self._determine_accounting_mappings(hoa_don),
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                raise Exception(
                    f"Failed to update accounting entry for HoaDonNhapMuaXuatThang {hoa_don.so_ct}: {str(e)}"
                ) from e

        return hoa_don

    def update(
        self, uuid, data: Dict[str, Any], entity_slug: str = None
    ) -> HoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a HoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonNhapMuaXuatThangModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonNhapMuaXuatThangModel with.
        entity_slug : str, optional
            The entity slug to filter by, by default None.

        Returns
        -------
        HoaDonNhapMuaXuatThangModel
            The updated HoaDonNhapMuaXuatThangModel instance.
        """
        instance = self.get_by_id(uuid, entity_slug)
        return self.repository.update(instance, **data)

    def delete(self, uuid, entity_slug: str = None) -> None:  # noqa: C901
        """
        Delete a HoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonNhapMuaXuatThangModel to delete.
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        """
        instance = self.get_by_id(uuid, entity_slug)
        self.repository.delete(instance)

    def filter_by_status(self, status: str, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Filter HoaDonNhapMuaXuatThangModel instances by status.

        Parameters
        ----------
        status : str
            The status to filter by.
        entity_slug : str
            The entity slug to filter by.

        Returns
        -------
        QuerySet
            A queryset of HoaDonNhapMuaXuatThangModel instances with the specified status.
        """
        return self.repository.filter_by_status(status, entity_slug)

    def filter_by_customer(
        self, customer_uuid, entity_slug: str
    ) -> QuerySet:  # noqa: C901
        """
        Filter HoaDonNhapMuaXuatThangModel instances by customer.

        Parameters
        ----------
        customer_uuid : UUID
            The UUID of the customer to filter by.
        entity_slug : str
            The entity slug to filter by.

        Returns
        -------
        QuerySet
            A queryset of HoaDonNhapMuaXuatThangModel instances for the specified customer.
        """
        return self.repository.filter_by_customer(customer_uuid, entity_slug)

    def filter_by_date_range(
        self, start_date, end_date, entity_slug: str
    ) -> QuerySet:  # noqa: C901
        """
        Filter HoaDonNhapMuaXuatThangModel instances by date range.

        Parameters
        ----------
        start_date : date
            The start date of the range.
        end_date : date
            The end date of the range.
        entity_slug : str
            The entity slug to filter by.

        Returns
        -------
        QuerySet
            A queryset of HoaDonNhapMuaXuatThangModel instances within the specified date range.
        """
        return self.repository.filter_by_date_range(start_date, end_date, entity_slug)

    def get_total_amount_by_entity(self, entity_slug: str) -> dict:  # noqa: C901
        """
        Get total amounts for HoaDonNhapMuaXuatThangModel instances by entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug to filter by.

        Returns
        -------
        dict
            A dictionary containing total amounts.
        """
        return self.repository.get_total_amount_by_entity(entity_slug)

    def list(
        self, entity_slug=None, user_model=None, **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        List HoaDonNhapMuaXuatThangModel instances with optional filtering.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply to the queryset.

        Returns
        -------
        QuerySet
            A queryset of HoaDonNhapMuaXuatThangModel instances.
        """
        return self.repository.list(
            entity_slug=entity_slug, user_model=user_model, **kwargs
        )

    def create_with_details(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> HoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new HoaDonNhapMuaXuatThangModel instance with related details.

        Parameters
        ----------
        entity_slug : str
            The slug of the entity to create the invoice for.
        data : Dict[str, Any]
            The data to create the HoaDonNhapMuaXuatThangModel with, including related details.

        Returns
        -------
        HoaDonNhapMuaXuatThangModel
            The created HoaDonNhapMuaXuatThangModel instance with related details.
        """
        # Extract related data
        chi_tiet_data = data.pop('chi_tiet', [])
        chi_phi_data = data.pop('chi_phi', [])
        chi_phi_vat_tu_data = data.pop('chi_phi_vat_tu', [])
        thue_data = data.pop('thue', [])

        # Create main invoice
        invoice = self.create(entity_slug, data)

        # Create related details using their respective services
        from django_ledger.services.mua_hang.hoa_don_nhap_mua_xuat_thang import (
            ChiPhiChiTietHoaDonNhapMuaXuatThangService,
            ChiPhiHoaDonNhapMuaXuatThangService,
            ChiTietHoaDonNhapMuaXuatThangService,
            ThueHoaDonNhapMuaXuatThangService,
        )

        chi_tiet_service = ChiTietHoaDonNhapMuaXuatThangService()
        chi_phi_service = ChiPhiHoaDonNhapMuaXuatThangService()
        chi_phi_vat_tu_service = ChiPhiChiTietHoaDonNhapMuaXuatThangService()
        thue_service = ThueHoaDonNhapMuaXuatThangService()

        # Create details
        for detail_data in chi_tiet_data:
            detail_data['hoa_don'] = invoice
            chi_tiet_service.create(detail_data)

        # Create costs
        for cost_data in chi_phi_data:
            cost_data['hoa_don'] = invoice
            chi_phi_service.create(cost_data)

        # Create detailed costs
        for detailed_cost_data in chi_phi_vat_tu_data:
            detailed_cost_data['hoa_don'] = invoice
            chi_phi_vat_tu_service.create(detailed_cost_data)

        # Create taxes
        for tax_data in thue_data:
            tax_data['hoa_don'] = invoice
            thue_service.create(tax_data)

        # Refresh from database to get related objects
        invoice.refresh_from_db()
        return invoice

    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Lấy cấu hình kế toán cho phiếu thu.

        Returns:
            List[Dict[str, Any]]: Danh sách mapping configuration
        """
        return self.RECEIPT_ACCOUNTING_CONFIG.copy()
