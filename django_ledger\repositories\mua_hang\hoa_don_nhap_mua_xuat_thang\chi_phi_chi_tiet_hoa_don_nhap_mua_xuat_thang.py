"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiPhiChiTietHoaDonNhapMuaXuatThang Repository implementation.
"""

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401


class ChiPhiChiTietHoaDonNhapMuaXuatThangRepository(BaseRepository):
    """
    Repository class for ChiPhiChiTietHoaDonNhapMuaXuatThangModel.
    Handles data access operations for detailed invoice costs.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the ChiPhiChiTietHoaDonNhapMuaXuatThangModel.
        """
        super().__init__(model_class=ChiPhiChiTietHoaDonNhapMuaXuatThangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for ChiPhiChiTietHoaDonNhapMuaXuatThangModel with optimized relationships.

        Returns
        -------
        QuerySet
            The base queryset for ChiPhiChiTietHoaDonNhapMuaXuatThangModel with related objects.
        """
        return (
            self.model_class.objects.all()
            .select_related(
                "hoa_don",
                "ma_cp",
                "ma_vt",
            )
        )

    def get_by_invoice(self, hoa_don_uuid) -> QuerySet:  # noqa: C901
        """
        Get ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances for the specified invoice.
        """
        return self.get_queryset().filter(hoa_don__uuid=hoa_don_uuid)

    def get_by_id(self, uuid, hoa_don_uuid=None) -> ChiPhiChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Get a ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiPhiChiTietHoaDonNhapMuaXuatThangModel to retrieve.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiPhiChiTietHoaDonNhapMuaXuatThangModel
            The retrieved ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.

        Raises
        ------
        ObjectDoesNotExist
            If the ChiPhiChiTietHoaDonNhapMuaXuatThangModel with the given UUID does not exist.
        """
        qs = self.get_queryset()
        if hoa_don_uuid:
            qs = self.get_by_invoice(hoa_don_uuid)
        return qs.get(uuid=uuid)

    def create(self, **kwargs) -> ChiPhiChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        **kwargs : dict
            The data to create the ChiPhiChiTietHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ChiPhiChiTietHoaDonNhapMuaXuatThangModel
            The created ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        return self.model_class.objects.create(**kwargs)

    def update(self, instance: ChiPhiChiTietHoaDonNhapMuaXuatThangModel, **kwargs) -> ChiPhiChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        instance : ChiPhiChiTietHoaDonNhapMuaXuatThangModel
            The instance to update.
        **kwargs : dict
            The data to update the instance with.

        Returns
        -------
        ChiPhiChiTietHoaDonNhapMuaXuatThangModel
            The updated ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(self, instance: ChiPhiChiTietHoaDonNhapMuaXuatThangModel) -> None:  # noqa: C901
        """
        Delete a ChiPhiChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        instance : ChiPhiChiTietHoaDonNhapMuaXuatThangModel
            The instance to delete.
        """
        instance.delete()

    def filter_by_material(self, material_uuid, hoa_don_uuid=None) -> QuerySet:  # noqa: C901
        """
        Filter ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances by material.

        Parameters
        ----------
        material_uuid : UUID
            The UUID of the material to filter by.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        QuerySet
            A queryset of ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances for the specified material.
        """
        qs = self.get_queryset()
        if hoa_don_uuid:
            qs = self.get_by_invoice(hoa_don_uuid)
        return qs.filter(ma_vt__uuid=material_uuid)

    def get_total_by_invoice(self, hoa_don_uuid) -> dict:  # noqa: C901
        """
        Get total detailed cost amounts for ChiPhiChiTietHoaDonNhapMuaXuatThangModel instances by invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        dict
            A dictionary containing total detailed cost amounts.
        """
        from django.db.models import Sum
        
        qs = self.get_by_invoice(hoa_don_uuid)
        return qs.aggregate(
            total_cost=Sum('tien_cp'),
            total_cost_nt=Sum('tien_cp_nt'),
        )
