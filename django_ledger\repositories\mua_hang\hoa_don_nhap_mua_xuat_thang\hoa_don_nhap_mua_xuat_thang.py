"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

HoaDonNhapMuaXuatThang Repository implementation.
"""

import uuid
from typing import Optional  # noqa: F401

from django.core.exceptions import ObjectDoesNotExist  # noqa: F401
from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    HoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories._utils.chung_tu_item_utils.chung_tu_item_utils import (  # noqa: F401
    convert_chung_tu_uuids_to_model_instances,
    update_instance_with_chung_tu_fields,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401


class HoaDonNhapMuaXuatThangRepository(BaseRepository):
    """
    Repository class for HoaDonNhapMuaXuatThangModel.
    Provides methods to interact with the database for HoaDonNhapMuaXuatThangModel.
    """

    def __init__(self, model_class=None):  # noqa: C901
        """
        Initialize the repository with the HoaDonNhapMuaXuatThangModel.
        """
        super().__init__(model_class or HoaDonNhapMuaXuatThangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for HoaDonNhapMuaXuatThangModel.

        Returns
        -------
        QuerySet
            The base queryset for HoaDonNhapMuaXuatThangModel.
        """
        return self.model_class.objects.all()

    def get_by_id(
        self, uuid, entity_slug=None, user_model=None
    ) -> HoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Retrieves a HoaDonNhapMuaXuatThangModel by its UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonNhapMuaXuatThangModel to retrieve.
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.

        Returns
        -------
        HoaDonNhapMuaXuatThangModel
            The retrieved HoaDonNhapMuaXuatThangModel.

        Raises
        ------
        ObjectDoesNotExist
            If the HoaDonNhapMuaXuatThangModel with the given UUID does not exist.
        """
        qs = self.get_queryset()
        if entity_slug and user_model:
            qs = self.model_class.objects.for_entity(
                entity_slug=entity_slug, user_model=user_model
            )

        return qs.get(uuid=uuid)

    def list(
        self, entity_slug=None, user_model=None, **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Lists HoaDonNhapMuaXuatThangModel instances with optional filtering.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply to the queryset.

        Returns
        -------
        QuerySet
            A queryset of HoaDonNhapMuaXuatThangModel instances.
        """
        qs = self.get_queryset()
        if entity_slug and user_model:
            qs = self.model_class.objects.for_entity(
                entity_slug=entity_slug, user_model=user_model
            )

        # Apply additional filters from kwargs
        if kwargs:
            qs = qs.filter(**kwargs)

        return qs

    def create(
        self, entity_model, **kwargs
    ) -> HoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Creates a new HoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to associate with the HoaDonNhapMuaXuatThangModel.
        **kwargs : dict
            Additional fields to set on the HoaDonNhapMuaXuatThangModel.

        Returns
        -------
        HoaDonNhapMuaXuatThangModel
            The created HoaDonNhapMuaXuatThangModel.
        """
        # Extract ChungTu fields from original data BEFORE UUID conversion
        chung_tu_fields = {}
        chung_tu_field_names = ['i_so_ct', 'ma_nk', 'so_ct', 'ngay_ct', 'ngay_lct']
        kwargs_copy = kwargs.copy()

        for field_name in chung_tu_field_names:
            if field_name in kwargs_copy:
                chung_tu_fields[field_name] = kwargs_copy.pop(field_name)

        # Convert UUIDs to model instances for ChungTu fields
        convert_chung_tu_uuids_to_model_instances(chung_tu_fields)

        # Convert UUID strings to model instances for remaining data
        processed_kwargs = self.convert_uuids_to_model_instances(kwargs_copy)

        # Create the instance without ChungTu fields first
        hoa_don = self.model_class(entity_model=entity_model, **processed_kwargs)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(hoa_don, field_name, value)

        # Save the instance
        hoa_don.save()
        return hoa_don

    def update(self, uuid, **kwargs) -> HoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Updates an existing HoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonNhapMuaXuatThangModel to update.
        **kwargs : dict
            Fields to update on the HoaDonNhapMuaXuatThangModel.

        Returns
        -------
        HoaDonNhapMuaXuatThangModel
            The updated HoaDonNhapMuaXuatThangModel.

        Raises
        ------
        ObjectDoesNotExist
            If the HoaDonNhapMuaXuatThangModel with the given UUID does not exist.
        """
        instance = self.get_by_id(uuid)

        # Use utility function to handle ChungTu field updates
        return update_instance_with_chung_tu_fields(
            instance=instance,
            data=kwargs,
            convert_uuids_func=self.convert_uuids_to_model_instances,
        )

    def delete(self, uuid) -> bool:  # noqa: C901
        """
        Deletes a HoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonNhapMuaXuatThangModel to delete.

        Returns
        -------
        bool
            True if the deletion was successful, False otherwise.

        Raises
        ------
        ObjectDoesNotExist
            If the HoaDonNhapMuaXuatThangModel with the given UUID does not exist.
        """
        hoa_don = self.get_by_id(uuid)
        hoa_don.delete()
        return True

    def get_by_customer(
        self, customer_id, entity_slug=None, user_model=None
    ) -> QuerySet:  # noqa: C901
        """
        Retrieves HoaDonNhapMuaXuatThangModel instances for a specific customer.

        Parameters
        ----------
        customer_id : UUID
            The UUID of the customer to filter by.
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.

        Returns
        -------
        QuerySet
            A queryset of HoaDonNhapMuaXuatThangModel instances for the specified customer.
        """
        qs = self.get_queryset()
        if entity_slug and user_model:
            qs = self.model_class.objects.for_entity(
                entity_slug=entity_slug, user_model=user_model
            )

        return qs.filter(ma_kh_id=customer_id)

    def get_by_status(
        self, status, entity_slug=None, user_model=None
    ) -> QuerySet:  # noqa: C901
        """
        Retrieves HoaDonNhapMuaXuatThangModel instances with a specific status.

        Parameters
        ----------
        status : str
            The status to filter by.
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.

        Returns
        -------
        QuerySet
            A queryset of HoaDonNhapMuaXuatThangModel instances with the specified status.
        """
        qs = self.get_queryset()
        if entity_slug and user_model:
            qs = self.model_class.objects.for_entity(
                entity_slug=entity_slug, user_model=user_model
            )

        return qs.filter(status=status)

    def convert_chung_tu_uuids_to_model_instances(
        self, data: dict
    ) -> None:  # noqa: C901
        """
        Convert UUID strings to model instances for ChungTu fields specifically.

        Parameters
        ----------
        data: dict
            The data dictionary containing ChungTu fields to process.
        """
        from django_ledger.models import (  # noqa: F401,
            ChungTu,
            EntityUnitModel,
            QuyenChungTu,
        )

        # Handle ma_nk field (QuyenChungTu reference)
        if 'ma_nk' in data and (
            isinstance(data['ma_nk'], str) or isinstance(data['ma_nk'], uuid.UUID)
        ):
            try:
                data['ma_nk'] = QuyenChungTu.objects.get(uuid__exact=data['ma_nk'])
            except QuyenChungTu.DoesNotExist:
                pass

        # Handle chung_tu field (ChungTu reference)
        if 'chung_tu' in data and (
            isinstance(data['chung_tu'], str) or isinstance(data['chung_tu'], uuid.UUID)
        ):
            try:
                data['chung_tu'] = ChungTu.objects.get(uuid__exact=data['chung_tu'])
            except ChungTu.DoesNotExist:
                pass

        # Handle unit_id field (EntityUnitModel reference)
        if 'unit_id' in data and (
            isinstance(data['unit_id'], str) or isinstance(data['unit_id'], uuid.UUID)
        ):
            try:
                data['unit_id'] = EntityUnitModel.objects.get(
                    uuid__exact=data['unit_id']
                )
            except EntityUnitModel.DoesNotExist:
                pass
