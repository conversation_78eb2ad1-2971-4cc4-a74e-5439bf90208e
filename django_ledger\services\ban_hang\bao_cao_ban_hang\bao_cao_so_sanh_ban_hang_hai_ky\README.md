# Sales Comparison Report Between Two Periods

## Overview

This module provides comprehensive sales comparison reporting functionality between two time periods, supporting multiple grouping options and cross-category grouping for detailed analysis. The system supports both same-category grouping (e.g., customers grouped by customer groups) and cross-category grouping (e.g., customers grouped by materials).

## Key Components

### 1. Service Layer (Modular Architecture)
- **`bao_cao_so_sanh_ban_hang_hai_ky.py`**: Main service class (274 lines)
- **`filters.py`**: UUID-based filtering logic (272 lines)
- **`utils.py`**: Data aggregation and processing utilities (280 lines)
- **`constants.py`**: Configuration constants for detail_by options (237 lines)

### 2. API Layer
- **Serializer**: Validates request parameters and formats responses
- **ViewSet**: Handles HTTP requests and pagination
- **URL Routing**: RESTful endpoint configuration

### 3. Module Benefits
- **Maintainability**: Each file < 300 lines, focused responsibilities
- **Reusability**: Filters and utils can be used across modules
- **Testability**: Isolated components for easier unit testing
- **Scalability**: Easy to extend with new entity types or filters

## Entity Codes & Query Levels

The report supports multiple grouping options through the `detail_by` parameter with updated entity codes. Entities are classified by their query level:

### **Invoice Level Entities** (Query từ hóa đơn)
These entities are queried from the invoice level (`HoaDonBanHangModel`):

### **Detail Level Entities** (Query từ chi tiết hóa đơn)
These entities are queried from the invoice detail level (`ChiTietHoaDonBanHangModel`):

#### Customer Groups (200-230) - **Invoice Level**
- **200**: Khách hàng (CustomerModel) → `hoa_don_ban_hang.ma_kh`
- **210**: Nhóm khách hàng 1
- **220**: Nhóm khách hàng 2
- **230**: Nhóm khách hàng 3

#### Organization (700) - **Invoice Level**
- **700**: Đơn vị (EntityUnitModel) → `hoa_don_ban_hang.unit_id`

#### Staff (910) - **Invoice Level**
- **910**: Nhân viên bán hàng (NhanVienModel) → `hoa_don_ban_hang.ma_nvbh`

#### Material Groups (300-340) - **Detail Level**
- **300**: Vật tư (VatTuModel) → `chi_tiet_hoa_don.ma_vt`
- **320**: Nhóm vật tư 1
- **330**: Nhóm vật tư 2
- **340**: Nhóm vật tư 3

#### Organization (810-820) - **Detail Level**
- **810**: Bộ phận (BoPhanModel) → `chi_tiet_hoa_don.ma_bp`
- **820**: Vụ việc (VuViecModel) → `chi_tiet_hoa_don.ma_vv`

#### Contracts & Agreements (830-840) - **Detail Level**
- **830**: Hợp đồng (ContractModel) → `chi_tiet_hoa_don.ma_hd`
- **840**: Khế ước (KheUocModel) → `chi_tiet_hoa_don.ma_ku`

#### Cost Management (850) - **Detail Level**
- **850**: Phí (PhiModel) → `chi_tiet_hoa_don.ma_phi`

#### Production (860) - **Detail Level**
- **860**: Sản phẩm (SanPhamModel) → `chi_tiet_hoa_don.ma_sp`

### **Query Level Benefits**

#### **Invoice Level Entities** (3 entities)
- ✅ **Performance**: Fewer joins required
- ✅ **Consistency**: One value per invoice
- ✅ **Efficiency**: Direct access from invoice record

#### **Detail Level Entities** (7 entities)
- ✅ **Granularity**: Multiple values per invoice possible
- ✅ **Flexibility**: Different values per line item
- ✅ **Detail**: Line-level analysis capabilities

### **Cross-Level Combinations**
The system supports cross-level grouping between invoice and detail level entities:
- **Invoice → Detail**: e.g., Customers (200) grouped by Materials (300)
- **Detail → Invoice**: e.g., Materials (300) grouped by Customers (200)
- **Same Level**: e.g., Customers (200) grouped by Units (700)

## Cross-Category Grouping

The system supports advanced cross-category grouping through the `group_by` parameter, allowing you to group one entity type by another entity type.

### Supported Cross-Category Combinations

#### Customers grouped by other entities
- **detail_by=200, group_by=300**: Customers grouped by Materials
- **detail_by=200, group_by=810**: Customers grouped by Departments
- **detail_by=200, group_by=820**: Customers grouped by Tasks
- **detail_by=200, group_by=910**: Customers grouped by Sales Staff

#### Materials grouped by other entities
- **detail_by=300, group_by=200**: Materials grouped by Customers
- **detail_by=300, group_by=810**: Materials grouped by Departments
- **detail_by=300, group_by=820**: Materials grouped by Tasks
- **detail_by=300, group_by=910**: Materials grouped by Sales Staff

#### Departments grouped by other entities
- **detail_by=810, group_by=200**: Departments grouped by Customers
- **detail_by=810, group_by=300**: Departments grouped by Materials
- **detail_by=810, group_by=820**: Departments grouped by Tasks
- **detail_by=810, group_by=910**: Departments grouped by Sales Staff

#### And 40+ more combinations...

### Cross-Category Response Structure

Cross-category grouping returns a hierarchical structure with group headers and detail items:

```json
{
  "results": [
    {
      "ma": "VT001",
      "ten": "Gao Nep",
      "sl_kn": 10.0,
      "tien_kn": 500000.0,
      "is_group_header": true    // Group header (Material)
    },
    {
      "ma": "CUST001",
      "ten": "Customer Name",
      "sl_kn": 10.0,
      "tien_kn": 500000.0,
      "is_group_header": false   // Detail item (Customer)
    }
  ]
}
```

## API Usage

### Endpoint
```
POST /api/entities/{entity_slug}/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/
```

### Request Format
```json
{
    "ngay_ct1": "********",    // Period 1 start date
    "ngay_ct2": "********",    // Period 1 end date
    "ngay_kt1": "********",    // Period 2 start date
    "ngay_kt2": "********",    // Period 2 end date
    "detail_by": "200",        // Grouping option (default: "200" for customers)
    "group_by": "300"          // Cross-category grouping (optional)
}
```

### Response Format
```json
{
  "results": [
    {
      "ma": "CUST001",           // Item/Customer/Warehouse code
      "ten": "Customer Name",    // Item/Customer/Warehouse name
      "nhom": "",                // Group code
      "ten_nhom": "",            // Group name
      "sl_kn": 10.0,            // Period 1 quantity
      "tien_kn": 500000.0,      // Period 1 amount
      "sl_kt": 15.0,            // Period 2 quantity
      "tien_kt": 750000.0,      // Period 2 amount
      "sl_cl": -5.0,            // Quantity difference (Period 1 - Period 2)
      "sl_tl": -33.33,          // Quantity ratio (%)
      "tien_cl": -250000.0,     // Amount difference
      "tien_nt_cl": -250000.0,  // Foreign currency amount difference
      "tien_tl": -33.33         // Amount ratio (%)
    }
  ],
  "count": 1,
  "next": null,
  "previous": null
}
```

## Examples

### Customer Analysis (Updated Codes)
```bash
curl -X POST "http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/" \
-H "Authorization: Token your_token" \
-H "Content-Type: application/json" \
-d '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "200"
}'
```

### Material Analysis (Updated Codes)
```bash
curl -X POST "http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/" \
-H "Authorization: Token your_token" \
-H "Content-Type: application/json" \
-d '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "300"
}'
```

### Department Analysis (Updated Codes)
```bash
curl -X POST "http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/" \
-H "Authorization: Token your_token" \
-H "Content-Type: application/json" \
-d '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "810"
}'
```

### Cross-Category Grouping Examples

#### Customers grouped by Materials
```bash
curl -X POST "http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/" \
-H "Authorization: Token your_token" \
-H "Content-Type: application/json" \
-d '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "200",
    "group_by": "300"
}'
```

#### Materials grouped by Customers
```bash
curl -X POST "http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/" \
-H "Authorization: Token your_token" \
-H "Content-Type: application/json" \
-d '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "300",
    "group_by": "200"
}'
```

#### Customers grouped by Sales Staff
```bash
curl -X POST "http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/" \
-H "Authorization: Token your_token" \
-H "Content-Type: application/json" \
-d '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "200",
    "group_by": "910"
}'
```

## UUID-Based Filtering

The API supports comprehensive UUID-based filtering for all entity types. All filter fields accept UUID values corresponding to their respective models.

### Supported UUID Filters

#### Customer Filters
- **ma_kh**: Customer UUID (CustomerModel) - Direct filter at invoice level
- **nh_kh1**: Customer Group 1 UUID - Complex filter (sub-query customers first)
- **nh_kh2**: Customer Group 2 UUID - Complex filter (sub-query customers first)
- **nh_kh3**: Customer Group 3 UUID - Complex filter (sub-query customers first)
- **rg_code**: Region UUID - Complex filter (sub-query customers first)

#### Product Filters
- **ma_vt**: Material UUID (VatTuModel) - Direct filter at detail level
- **ma_lvt**: Material Type UUID - Complex filter (sub-query materials first)
- **nh_vt1**: Material Group 1 UUID - Complex filter (sub-query materials first)
- **nh_vt2**: Material Group 2 UUID - Complex filter (sub-query materials first)
- **nh_vt3**: Material Group 3 UUID - Complex filter (sub-query materials first)

#### Organization Filters
- **ma_kho**: Warehouse UUID (KhoHangModel)
- **ma_unit**: Unit UUID (EntityUnitModel)
- **ma_bp**: Department UUID (BoPhanModel)
- **ma_vv**: Task UUID (VuViecModel)

#### Contract & Agreement Filters
- **ma_hd**: Contract UUID (ContractModel)
- **ma_ku**: Agreement UUID (KheUocModel)
- **ma_dtt**: Payment Object UUID (DoiTuongThanhToanModel)

#### Production Filters
- **ma_sp**: Product UUID (SanPhamModel) - Detail level filter
- **ma_lsx**: Production Order UUID (LenhSanXuatModel) - Detail level filter
- **ma_nvbh**: Sales Staff UUID (NhanVienModel) - Invoice level filter

#### Cost & Fee Filters
- **ma_phi**: Fee UUID (PhiModel) - Detail level filter
- **ma_cp0**: Cost UUID (ChiPhiModel) - Detail level filter

#### Filters Not Currently Supported
- **tk_vt, tk_dt, tk_gv**: Account filters (future implementation)
- **ma_lo, ma_vi_tri**: Batch/location filters (future implementation)
- **so_ct1, so_ct2**: Document number filters (future implementation)
- **dien_giai**: Description filter (future implementation)

### UUID Filter Examples

#### Customer UUID Filter
```bash
curl --location 'http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/' \
--header 'authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
--header 'content-type: application/json' \
--data '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "200",
    "ma_kh": "82946c1345424fddabd61d417b70f851"
}'
```

#### Complex Customer Group Filter
```bash
curl --location 'http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/' \
--header 'authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
--header 'content-type: application/json' \
--data '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "200",
    "nh_kh1": "customer-group-1-uuid",
    "rg_code": "region-uuid"
}'
```

#### Complex Material Group Filter
```bash
curl --location 'http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/' \
--header 'authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
--header 'content-type: application/json' \
--data '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "300",
    "nh_vt1": "material-group-1-uuid",
    "ma_lvt": "material-type-uuid",
    "ma_kho": "warehouse-uuid"
}'
```

#### Multiple UUID Filters
```bash
curl --location 'http://localhost:8002/api/entities/tutimi-dnus2xnc/erp/ban-hang/bao-cao-ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/' \
--header 'authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
--header 'content-type: application/json' \
--data '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "ngay_kt1": "********",
    "ngay_kt2": "********",
    "detail_by": "300",
    "ma_vt": "98fa3252d27b4a769a8bc06a05b4dd2e",
    "ma_kho": "some-warehouse-uuid",
    "ma_bp": "some-department-uuid"
}'
```

### UUID Format Requirements
- **Format**: 32-character hexadecimal string without hyphens
- **Example**: `82946c1345424fddabd61d417b70f851`
- **Not**: `82946c13-4542-4fdd-abd6-1d417b70f851`

## Technical Implementation

### Data Sources
- **HoaDonBanHang**: Sales invoices
- **HoaDonDichVu**: Service invoices
- **ChiTietHoaDonBanHang**: Sales invoice details
- **ChiTietHoaDonModel**: Service invoice details

### Key Features
- **Null-safe aggregation**: Uses Coalesce for handling null values
- **Mixed data type support**: Handles FloatField/IntegerField calculations
- **Soft delete awareness**: Filters out deleted records
- **Dynamic grouping**: Supports multiple entity types
- **Period comparison**: Calculates differences and ratios
- **Pagination**: Supports large datasets

### Performance Considerations
- Uses database-level aggregation for efficiency
- Implements proper indexing on date and entity fields
- Optimizes queries with select_related for foreign keys
- Handles large datasets with pagination

## Constants Configuration

The `constants.py` file provides:
- **DETAIL_BY_CONFIG**: Main configuration mapping
- **Helper functions**: Validation and utility functions
- **Group mappings**: Categorized options for easier management

### Adding New Detail_by Options
1. Add new entry to `DETAIL_BY_CONFIG` in `constants.py`
2. Update aggregation logic in service if needed
3. Test with sample data
4. Update documentation

## Testing

### Sample Test Commands
```bash
# Test customer grouping
detail_by="100"

# Test material grouping
detail_by="200"

# Test warehouse grouping
detail_by="700"

# Test staff grouping
detail_by="1200"
```

### Expected Behavior
- Valid detail_by options return structured data
- Invalid detail_by options return validation errors
- Empty datasets return empty results (not errors)
- Null values are handled gracefully

## Mermaid Diagrams

### Module Architecture
```mermaid
graph TB
    A[API Request] --> B[ViewSet]
    B --> C[Serializer]
    C --> D[Main Service]
    D --> E[Constants]
    D --> F[Filters Module]
    D --> G[Utils Module]
    F --> H[UUID Filtering]
    F --> I[Field Validation]
    G --> J[Data Aggregation]
    G --> K[Comparison Calculations]
    D --> L[HoaDonBanHang]
    D --> M[HoaDonDichVu]
    L --> N[Database]
    M --> N
    N --> O[Response Data]
```

### Component Diagram
```mermaid
graph TB
    subgraph "Service Layer"
        A[bao_cao_so_sanh_ban_hang_hai_ky.py<br/>274 lines]
        B[filters.py<br/>272 lines]
        C[utils.py<br/>280 lines]
        D[constants.py<br/>237 lines]
    end

    A --> B
    A --> C
    A --> D
    B --> D
    C --> D

    subgraph "Responsibilities"
        E[Main Logic & Orchestration]
        F[UUID Filtering & Validation]
        G[Data Processing & Aggregation]
        H[Configuration & Constants]
    end

    A --> E
    B --> F
    C --> G
    D --> H
```

### Data Flow
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Service
    participant Database

    Client->>API: POST with periods & detail_by
    API->>Service: generate_report()
    Service->>Database: Query HoaDonBanHang
    Service->>Database: Query HoaDonDichVu
    Database-->>Service: Aggregated data
    Service->>Service: Calculate comparisons
    Service-->>API: Report data
    API-->>Client: JSON response
```

## Cross-Category Implementation Flow

### Three-Step Process
The cross-category grouping follows a specific three-step flow as requested:

1. **Từ group_by** → Kiểm tra xem hóa đơn/chi tiết có match được query đó không
2. **Có data đó** → Kết hợp với detail_by để kiểm tra hóa đơn và chi tiết có match không
3. **Format group mapping response** → Tạo response theo structure mong muốn

### Implementation Details

#### Step 1: Query by Group_by Entity
```python
def _query_by_group_by_entity(entity, period, start_key, end_key, group_by, filters):
    """
    Query invoice details based on group_by entity type to find matching records.

    Examples:
    - group_by="200" (Customers): Filter by customer-related criteria
    - group_by="300" (Materials): Filter by material-related criteria
    - group_by="810" (Departments): Filter by department-related criteria
    """
```

#### Step 2: Combine with Detail_by
```python
def _combine_group_by_with_detail_by(details_p1, details_p2, detail_by, group_by):
    """
    Combine group_by matched data with detail_by to create grouped structure.

    Returns: {group_key: {detail_key: {period_data, info}}}
    """
```

#### Step 3: Format Response
```python
def _format_group_mapping_response(combined_data, detail_by, group_by):
    """
    Format the combined data into the final response structure with group headers
    and detail items in sequential order.
    """
```

### Cross-Category Entity Code Mapping

| **Entity** | **Old Code** | **New Code** | **Description** |
|------------|--------------|--------------|-----------------|
| Khách hàng | 100 | **200** | CustomerModel |
| Nhóm khách hàng 1,2,3 | 101,102,103 | **210,220,230** | Customer Groups |
| Vật tư | 200 | **300** | VatTuModel |
| Nhóm vật tư 1,2,3 | 201,202,203 | **320,330,340** | Material Groups |
| Đơn vị | 300 | **700** | EntityUnitModel |
| Bộ phận | 400 | **810** | BoPhanModel |
| Vụ việc | 500 | **820** | VuViecModel |
| Hợp đồng | 600 | **830** | ContractModel |
| Khế ước | 800 | **840** | KheUocModel |
| Phí | 900 | **850** | PhiModel |
| Sản phẩm | 1000 | **860** | SanPhamModel |
| Nhân viên bán hàng | 1200 | **910** | NhanVienModel |

### Cross-Category Examples

#### Working Combinations
- ✅ **Customers grouped by Materials**: `detail_by=200, group_by=300`
- ✅ **Materials grouped by Customers**: `detail_by=300, group_by=200`
- ✅ **Customers grouped by Departments**: `detail_by=200, group_by=810`
- ✅ **Departments grouped by Customers**: `detail_by=810, group_by=200`
- ✅ **Materials grouped by Sales Staff**: `detail_by=300, group_by=910`
- ✅ **Sales Staff grouped by Materials**: `detail_by=910, group_by=300`
- ✅ **And 40+ more combinations...**

---

**Author**: AI Assistant
**Date**: July 2, 2025
**Version**: 2.0.0 - Updated with Cross-Category Grouping & New Entity Codes
