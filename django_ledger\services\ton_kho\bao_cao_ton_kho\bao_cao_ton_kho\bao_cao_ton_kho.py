"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for Bao Cao <PERSON> (Inventory Report) business logic.

This service implements enterprise-grade inventory reporting with current stock balance calculations,
following 20 years of ERP experience in inventory management systems.
"""

from typing import Any, Dict, List
from decimal import Decimal

from django_ledger.services.base import BaseService


class BaoCaoTonKhoService(BaseService):
    """
    Service class for handling Inventory Report (Bao Cao Ton Kho) business logic.
    This service orchestrates calls to other services to generate current inventory balance reports.

    Based on 20 years of ERP experience, this implements enterprise-grade inventory reporting
    with proper current stock balance calculations and comprehensive filtering.
    """

    def __init__(self):
        """
        Initialize the service with required services.
        """
        super().__init__()
        # Initialize services lazily to avoid circular imports
        self._stock_transaction_service = None
        self._warehouse_stock_audit_service = None

    @property
    def stock_transaction_service(self):
        """Lazy initialization of stock_transaction_service."""
        if self._stock_transaction_service is None:
            from django_ledger.services.stock_transaction import StockTransactionService
            self._stock_transaction_service = StockTransactionService()
        return self._stock_transaction_service

    @property
    def warehouse_stock_audit_service(self):
        """Lazy initialization of warehouse_stock_audit_service."""
        if self._warehouse_stock_audit_service is None:
            from django_ledger.services.warehouse_stock_audit import WarehouseStockAuditService
            self._warehouse_stock_audit_service = WarehouseStockAuditService()
        return self._warehouse_stock_audit_service

    def generate_report(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate inventory report with the given filters.

        This is the main entry point for the report generation. It implements
        enterprise-grade inventory reporting logic with current stock balance calculations.
        Following the same pattern as tong_hop_nhap_xuat_ton.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters including date, warehouse, material, etc.

        Returns
        -------
        List[Dict[str, Any]]
            Report data list with current inventory balances including summary record
        """
        try:
            # Extract report date (using ngay_ct1 as the report date)
            report_date = filters.get('ngay_ct1')

            if not report_date:
                return []

            # Get current balances as of report date using WarehouseStockAuditService
            # Following the same pattern as tong_hop_nhap_xuat_ton
            current_balances = self.warehouse_stock_audit_service.get_current_balances_with_filters(
                entity_slug, filters, report_date
            )

            # Process and format data for response with summary record
            report_data = self._process_inventory_report_optimized(
                current_balances, filters
            )

            return report_data

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(
                f"Error generating inventory report: {str(e)}", exc_info=True
            )
            # Return empty list instead of raising exception
            return []

    def _process_inventory_report_optimized(
        self, current_balances: List[Dict[str, Any]], filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Process current inventory balances into report format using database-aggregated results.
        Following the same pattern as tong_hop_nhap_xuat_ton with summary record, totals, and group_by functionality.

        This method implements enterprise-grade data processing with proper error handling
        and performance optimization for large datasets.

        Parameters
        ----------
        current_balances : List[Dict[str, Any]]
            Current stock balances from WarehouseStockAuditService
        filters : Dict[str, Any]
            Filter parameters including group_by option

        Returns
        -------
        List[Dict[str, Any]]
            Processed report data ready for API response with summary record and optional grouping
        """
        try:
            from decimal import Decimal

            # Initialize totals for summary record
            total_ton_cuoi = Decimal('0')
            total_du_cuoi = Decimal('0')

            # Process individual records
            processed_records = []
            stt = 1

            for balance_data in current_balances:
                # Transform individual record
                record = {
                    'stt': stt,
                    'ma_kho': str(balance_data.get('ma_kho', '')),
                    'ma_vt': str(balance_data.get('ma_vt', '')),
                    'nhom': str(balance_data.get('nhom', '')),
                    'dvt': str(balance_data.get('dvt', '')),
                    'ton_cuoi': Decimal(str(balance_data.get('ton_cuoi', 0))),
                    'du_cuoi': Decimal(str(balance_data.get('du_cuoi', 0))),
                    'ten_vt': str(balance_data.get('ten_vt', '')),
                    'ten_kho': str(balance_data.get('ten_kho', '')),
                }

                # Add to totals
                total_ton_cuoi += record['ton_cuoi']
                total_du_cuoi += record['du_cuoi']

                processed_records.append(record)
                stt += 1

            # Apply group_by grouping if specified (following tong_hop_nhap_xuat_ton pattern)
            grouped_records = self._apply_group_by(processed_records, filters.get('group_by'))

            # Recalculate totals based on final processed records
            final_totals = self._calculate_filtered_totals(grouped_records)

            # Create summary record with stt = 0 (following tong_hop_nhap_xuat_ton pattern)
            summary_record = {
                'stt': 0,
                'ma_kho': '',
                'ma_vt': '',
                'nhom': '',
                'dvt': '',
                'ton_cuoi': final_totals['total_ton_cuoi'],
                'du_cuoi': final_totals['total_du_cuoi'],
                'ten_vt': 'Tổng cộng',
                'ten_kho': '',
            }

            # Combine summary + grouped records (summary first, following pattern)
            result = [summary_record] + grouped_records

            return result

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error processing inventory report data: {str(e)}", exc_info=True)
            return []

    def _calculate_filtered_totals(self, filtered_records: List[Dict[str, Any]]) -> Dict[str, Decimal]:
        """
        Calculate totals for filtered records.
        Following the same pattern as tong_hop_nhap_xuat_ton.

        Parameters
        ----------
        filtered_records : List[Dict[str, Any]]
            Filtered detail records

        Returns
        -------
        Dict[str, Decimal]
            Calculated totals for summary row
        """
        totals = {
            'total_ton_cuoi': Decimal('0'),
            'total_du_cuoi': Decimal('0'),
        }

        for record in filtered_records:
            totals['total_ton_cuoi'] += record.get('ton_cuoi', Decimal('0'))
            totals['total_du_cuoi'] += record.get('du_cuoi', Decimal('0'))

        return totals

    def _apply_group_by(self, records: List[Dict[str, Any]], group_by: int = None) -> List[Dict[str, Any]]:
        """
        Apply group_by grouping to records following tong_hop_nhap_xuat_ton pattern.

        Parameters
        ----------
        records : List[Dict[str, Any]]
            Records to group
        group_by : int, optional
            Grouping type: None/blank=none, 1=loai_vt, 2=nh_vt1, 3=nh_vt2, 4=nh_vt3, 5=tk_vt

        Returns
        -------
        List[Dict[str, Any]]
            Grouped records
        """
        if group_by is None or not records:
            # No grouping, return original records
            return records

        # Determine grouping field based on group_by value
        group_field_mapping = {
            1: 'ma_lvt',   # Group by material category (loại vật tư)
            2: 'nh_vt1',   # Group by material group 1
            3: 'nh_vt2',   # Group by material group 2
            4: 'nh_vt3',   # Group by material group 3
            5: 'tk_gv',    # Group by material account (tài khoản giá vốn)
        }

        if group_by not in group_field_mapping:
            # Invalid group_by value, return original records
            return records

        group_field = group_field_mapping[group_by]

        # Group records by the specified field (following tong_hop_nhap_xuat_ton pattern)
        from collections import defaultdict
        groups = defaultdict(list)

        for record in records:
            # Get group key from the record
            group_key = self._get_group_key_from_record(record, group_field)
            groups[group_key].append(record)

        # Create grouped records with sub-summaries and details
        grouped_records = []
        stt = 1

        # Sort groups for consistent ordering
        sorted_groups = sorted(groups.items(), key=lambda x: x[0] or '')

        for group_key, group_records in sorted_groups:
            if not group_records:
                continue

            # Create sub-summary record for this group
            # Aggregate numeric fields within the same group
            total_ton_cuoi = sum(record['ton_cuoi'] for record in group_records)
            total_du_cuoi = sum(record['du_cuoi'] for record in group_records)

            # Create sub-summary record
            sub_summary_record = {
                'stt': stt,
                'ma_kho': '',  # Clear warehouse code for sub-summary
                'ten_kho': '',  # Clear warehouse name for sub-summary
                'ma_vt': '',   # Clear material code for sub-summary
                'ten_vt': self._get_group_display_name(group_key, group_field),
                'dvt': '',     # Clear unit for sub-summary
                'nhom': group_key or '',  # Show group key in nhom field
                'ton_cuoi': total_ton_cuoi,
                'du_cuoi': total_du_cuoi,
            }

            grouped_records.append(sub_summary_record)
            stt += 1

            # Add detail records for this group
            # Sort detail records within group by name (ten_kho, ten_vt)
            sorted_detail_records = sorted(group_records, key=lambda x: (x['ten_kho'], x['ten_vt']))

            for detail_record in sorted_detail_records:
                detail_record_copy = detail_record.copy()
                detail_record_copy['stt'] = stt
                grouped_records.append(detail_record_copy)
                stt += 1

        return grouped_records

    def _get_group_key_from_record(self, record: Dict[str, Any], group_field: str) -> str:
        """
        Get group key from record based on group field.
        Uses the same field mapping as tong_hop_nhap_xuat_ton.
        """
        # Map group fields to data field names (following tong_hop_nhap_xuat_ton pattern)
        field_mapping = {
            'ma_lvt': 'ma_vt__ma_lvt',
            'nh_vt1': 'ma_vt__nh_vt1',
            'nh_vt2': 'ma_vt__nh_vt2',
            'nh_vt3': 'ma_vt__nh_vt3',
            'tk_gv': 'ma_vt__tk_gv'
        }

        aggregated_field = field_mapping.get(group_field)
        if aggregated_field:
            return str(record.get(aggregated_field, '')) or 'Unknown'
        else:
            return 'Unknown'

    def _get_group_display_name(self, group_key: str, group_field: str) -> str:
        """
        Get display name for the group.
        """
        if not group_key or group_key == 'Unknown':
            return f"{group_field} không xác định"
        return group_key




