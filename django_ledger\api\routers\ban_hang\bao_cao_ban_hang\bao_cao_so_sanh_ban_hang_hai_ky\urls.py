"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bao Cao So Sanh Ban Hang Hai Ky (Sales Comparison Report Between Two Periods) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.ban_hang.bao_cao_ban_hang.bao_cao_so_sanh_ban_hang_hai_ky import (
    BaoCaoSoSanhBanHangHaiKyViewSet
)

# URL patterns - Single endpoint for sales comparison report with filters as POST body data
urlpatterns = [
    # Sales Comparison Report endpoint - returns report directly with filter POST body data
    path(
        "", 
        BaoCaoSoSanhBanHangHaiKyViewSet.as_view({"post": "get_report", "get": "options"}), 
        name="bao-cao-so-sanh-ban-hang-hai-ky-report"
    ),
]
