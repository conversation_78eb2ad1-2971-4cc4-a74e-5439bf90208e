"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for Tinh Ton Kho Tuc Thoi (Real-time Inventory Calculation) API.
"""

from drf_spectacular.utils import (
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from django_ledger.api.serializers.ton_kho.tinh_gia_hang_ton_kho.tinh_ton_kho_tuc_thoi import (
    TinhTonKhoTucThoiRequestSerializer,
)
from django_ledger.services.ton_kho.tinh_gia_hang_ton_kho.tinh_ton_kho_tuc_thoi import (
    TinhTonKhoTucThoiService,
)


@extend_schema_view(
    calculate=extend_schema(
        summary="Tính Tồn Kho Tức Thời",
        description="Tính toán tồn kho theo thời gian thực cho vật tư và kho hàng được chỉ định",
        request=TinhTonKhoTucThoiRequestSerializer,
        responses={200: {"message": "Chương trình đã thực hiện xong"}},
    )
)
class TinhTonKhoTucThoiViewSet(viewsets.ViewSet):
    """
    ViewSet for Real-time Inventory Calculation (Tinh Ton Kho Tuc Thoi).

    Provides endpoint for calculating current inventory levels for specified
    material and warehouse combinations.
    """

    permission_classes = [permissions.IsAuthenticated]

    def __init__(self, **kwargs):
        """Initialize the ViewSet."""
        super().__init__(**kwargs)
        self.service = None  # Will be initialized when needed

    @action(detail=False, methods=['post'])
    def calculate(self, request, entity_slug):
        """
        Calculate real-time inventory for specified material and warehouse.

        Parameters
        ----------
        request : Request
            The request object containing POST body data for calculation
        entity_slug : str
            The entity slug for data isolation

        Returns
        -------
        Response
            The inventory calculation results
        """
        try:
            # Validate POST body data with entity context
            serializer = TinhTonKhoTucThoiRequestSerializer(
                data=request.data,
                context={'entity_slug': entity_slug, 'request': request}
            )
            
            if not serializer.is_valid():
                return Response(
                    {
                        "detail": "Tham số không hợp lệ",
                        "errors": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get validated data
            validated_data = serializer.validated_data
            
            # Initialize service if not already done
            if self.service is None:
                self.service = TinhTonKhoTucThoiService()

            # Perform inventory calculation using service
            calculation_result = self.service.inventory_calculation(
                entity_slug=entity_slug,
                nam=validated_data['nam'],
                ma_vt=validated_data['ma_vt'],
                ma_kho=validated_data['ma_kho']
            )

            # Return simple success message
            return Response(
                {"message": "Chương trình đã thực hiện xong"},
                status=status.HTTP_200_OK
            )

        except ValueError as e:
            return Response(
                {
                    "detail": "Lỗi tính toán tồn kho",
                    "error": str(e),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {
                    "detail": "Lỗi hệ thống",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
