"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiPhiChiTietHoaDonNhapMuaXuatThang Serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangModel,
)


class ChiPhiChiTietHoaDonNhapMuaXuatThangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiPhiChiTietHoaDonNhapMuaXuatThangModel.
    Used for read operations and API responses.
    """

    # Related object data
    hoa_don_data = serializers.SerializerMethodField()
    ma_cp_data = serializers.SerializerMethodField()
    ma_vt_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiPhiChiTietHoaDonNhapMuaXuatThangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]

    def get_hoa_don_data(self, obj):  # noqa: C901
        """Get invoice data."""
        if obj.hoa_don:
            # Avoid circular import by importing here
            from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.hoa_don_nhap_mua_xuat_thang import (
                HoaDonNhapMuaXuatThangSerializer,
            )
            return HoaDonNhapMuaXuatThangSerializer(obj.hoa_don).data
        return None

    def get_ma_cp_data(self, obj):  # noqa: C901
        """Get cost data."""
        if obj.ma_cp:
            # Assuming ChiPhiModel serializer exists
            return {"uuid": str(obj.ma_cp.uuid), "ma_cp": obj.ma_cp.ma_cp}
        return None

    def get_ma_vt_data(self, obj):  # noqa: C901
        """Get material data."""
        if obj.ma_vt:
            from django_ledger.api.serializers.erp import VatTuSanPhamHangHoaModelSerializer
            return VatTuSanPhamHangHoaModelSerializer(obj.ma_vt).data
        return None


class ChiPhiChiTietHoaDonNhapMuaXuatThangCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiPhiChiTietHoaDonNhapMuaXuatThangModel.
    Used for create and update operations.
    """

    class Meta:
        model = ChiPhiChiTietHoaDonNhapMuaXuatThangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]
