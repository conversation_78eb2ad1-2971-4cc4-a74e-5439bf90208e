"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ThueHoaDonNhapMuaXuatThang ViewSet implementation.
"""

from rest_framework import status  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401
from rest_framework.viewsets import ModelViewSet  # noqa: F401

from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ThueHoaDonNhapMuaXuatThangCreateUpdateSerializer,
    ThueHoaDonNhapMuaXuatThangSerializer,
)
from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ThueHoaDonNhapMuaXuatThangModel,
)
from django_ledger.services.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ThueHoaDonNhapMuaXuatThangService,
)


class ThueHoaDonNhapMuaXuatThangViewSet(ModelViewSet):
    """
    ViewSet for ThueHoaDonNhapMuaXuatThangModel.
    Provides CRUD operations for invoice taxes.
    """

    serializer_class = ThueHoaDonNhapMuaXuatThangSerializer
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):  # noqa: C901
        """Initialize the viewset with the service."""
        super().__init__(**kwargs)
        self.service = ThueHoaDonNhapMuaXuatThangService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            The queryset filtered by invoice UUID if provided.
        """
        hoa_don_uuid = self.kwargs.get('hoa_don_uuid')
        if hoa_don_uuid:
            return self.service.get_by_invoice(hoa_don_uuid)
        return ThueHoaDonNhapMuaXuatThangModel.objects.none()

    def get_serializer_class(self):  # noqa: C901
        """
        Get the appropriate serializer class based on the action.

        Returns
        -------
        Serializer
            The serializer class for the current action.
        """
        if self.action in ['create', 'update', 'partial_update']:
            return ThueHoaDonNhapMuaXuatThangCreateUpdateSerializer
        return ThueHoaDonNhapMuaXuatThangSerializer

    def perform_create(self, serializer):  # noqa: C901
        """
        Perform the creation of a new ThueHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        serializer : ThueHoaDonNhapMuaXuatThangCreateUpdateSerializer
            The serializer with validated data.
        """
        hoa_don_uuid = self.kwargs.get('hoa_don_uuid')
        if hoa_don_uuid:
            from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (
                HoaDonNhapMuaXuatThangModel,
            )

            hoa_don = HoaDonNhapMuaXuatThangModel.objects.get(uuid=hoa_don_uuid)
            serializer.save(hoa_don=hoa_don)
        else:
            serializer.save()

    @action(detail=True, methods=['post'])
    def calculate_tax(self, request, hoa_don_uuid=None, pk=None):  # noqa: C901
        """
        Calculate and update tax amounts for the tax line.

        Parameters
        ----------
        request : Request
            The HTTP request.
        hoa_don_uuid : UUID
            The invoice UUID.
        pk : UUID
            The tax UUID.

        Returns
        -------
        Response
            The response with updated tax amounts.
        """
        try:
            tax = self.get_object()

            # Get data from request
            data = {
                't_tien_nt': request.data.get('t_tien_nt', tax.t_tien_nt),
                't_tien': request.data.get('t_tien', tax.t_tien),
                'thue_suat': request.data.get('thue_suat', tax.thue_suat),
            }

            # Calculate tax amounts
            calculated_data = self.service.calculate_tax_amounts(data)

            # Update the tax
            updated_tax = self.service.update(tax.uuid, calculated_data, hoa_don_uuid)

            serializer = self.get_serializer(updated_tax)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def by_tax_code(self, request, hoa_don_uuid=None):  # noqa: C901
        """
        Get taxes filtered by tax code.

        Parameters
        ----------
        request : Request
            The HTTP request.
        hoa_don_uuid : UUID
            The invoice UUID.

        Returns
        -------
        Response
            The response with filtered taxes.
        """
        tax_uuid = request.query_params.get('tax_uuid')
        if not tax_uuid:
            return Response(
                {'error': 'tax_uuid parameter is required'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            taxes = self.service.filter_by_tax_code(tax_uuid, hoa_don_uuid)
            page = self.paginate_queryset(taxes)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(taxes, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def totals(self, request, hoa_don_uuid=None):  # noqa: C901
        """
        Get total tax amounts for all taxes in the invoice.

        Parameters
        ----------
        request : Request
            The HTTP request.
        hoa_don_uuid : UUID
            The invoice UUID.

        Returns
        -------
        Response
            The response with total tax amounts.
        """
        try:
            if not hoa_don_uuid:
                return Response(
                    {'error': 'hoa_don_uuid is required'},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            totals = self.service.get_total_by_invoice(hoa_don_uuid)
            return Response(totals, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
