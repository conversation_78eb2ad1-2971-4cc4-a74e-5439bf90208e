"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Ban Hang (Sales Invoice) service implementation.
"""

from django.core.exceptions import ObjectDoesNotExist  # noqa: F401
from django.db import transaction
from django.db.models import QuerySet
from datetime import date
from typing import Optional, List

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    HoaDonBanHangModel,
)
from django_ledger.repositories.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    HoaDonBanHangRepository,
)
from django_ledger.services.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang.chi_tiet_hoa_don_ban_hang import (  # noqa: F401,
    ChiTietHoaDonBanHangService,
)
from django_ledger.services.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang.thong_tin_thanh_toan_hoa_don_ban_hang import (  # noqa: F401,
    ThongTinThanhToanHoaDonBanHangService,
)
from django_ledger.services.base import BaseService

# ✅ UNIFIED SERVICES: Replace legacy utils with unified services
from django_ledger.utils_new.debt_management import CongNoCreation
from django_ledger.utils_new.inventory_management import TonKhoCreation
from typing import List, Dict, Any



class HoaDonBanHangService(BaseService):
    """
    Service class for HoaDonBanHangModel.
    Provides business logic for HoaDonBanHangModel operations.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Hóa đơn bán hàng accounting mappings
    SALES_INVOICE_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'DT0CK',                # Doanh thu
            'debit_account_field': 'tk',            # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_dt',        # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'header',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'tien2',                # Thành tiền (detail)
            'detail_source': 'chi_tiet',            # Related name
            'canCreate': True                       # Default: always create revenue entry
        },
        {
            'journal_type': 'THUE',                 # Thuế
            'debit_account_field': 'tk',            # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_thue_co',   # Tài khoản thuế - CREDIT
            'debit_account_source': 'header',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'thue',                 # Tiền thuế (detail)
            'detail_source': 'chi_tiet',            # Related name
            'canCreate': True                       # Default: always create tax entry
        }
    ]

    # ✅ PREDEFINED CONFIGURATION: Hóa đơn bán hàng stock mappings
    SALES_INVOICE_STOCK_CONFIG = [
        {
            'transaction_type': 'OUT',              # Xuất kho (bán hàng)
            'detail_source': 'chi_tiet',           # Related name
            'quantity_field': 'so_luong',          # Số lượng (detail) - correct field name
            'price_field': 'gia',                  # Đơn giá (detail)
            'canCreate': True                      # Default: always create stock entry
            # Hardcoded fields: ma_kho, ma_vt, ma_lo, ma_vi_tri, ma_nt
        }
    ]

    def __init__(self):  # noqa: C901
        super().__init__()
        self.hoa_don_repository = HoaDonBanHangRepository(
            model_class=HoaDonBanHangModel
        )
        self.chi_tiet_service = ChiTietHoaDonBanHangService()
        self.thanh_toan_service = ThongTinThanhToanHoaDonBanHangService()
        self._cong_no_service = CongNoCreation()
        self._ton_kho_service = TonKhoCreation()

    def _determine_accounting_mappings(self, hoa_don: HoaDonBanHangModel) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional accounting entry creation dựa trên document status
        - Flexible business rules cho different scenarios
        - Support complex approval workflows

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.SALES_INVOICE_ACCOUNTING_CONFIG.copy()

        # ✅ BUSINESS LOGIC: Determine canCreate based on invoice status and business rules

        # Example business logic - customize theo requirements
        if hasattr(hoa_don, 'status'):
            status = getattr(hoa_don, 'status', '1')  # Default APPROVED for backward compatibility

            if status in ['0', '4']:
                # Draft/Pending invoices - không tạo bút toán
                for mapping in mappings:
                    mapping['canCreate'] = False

            elif status in ['1', '2']:
                # Approved invoices - tạo đầy đủ bút toán
                for mapping in mappings:
                    mapping['canCreate'] = True

            else:
                # Unknown status - default behavior
                for mapping in mappings:
                    mapping['canCreate'] = True
        else:
            # No status field - default behavior (backward compatibility)
            for mapping in mappings:
                mapping['canCreate'] = True

        # ✅ ADDITIONAL BUSINESS RULES: Add more complex logic here

        # Example: Skip tax entry nếu không có thuế
        if hasattr(hoa_don, 'chi_tiet'):
            try:
                chi_tiet_list = hoa_don.chi_tiet.all()
                has_tax = any(getattr(ct, 'thue', 0) > 0 for ct in chi_tiet_list)

                if not has_tax:
                    for mapping in mappings:
                        if mapping['journal_type'] == 'THUE':
                            mapping['canCreate'] = False
            except:
                pass  # Ignore errors in business logic evaluation

        return mappings

    def _determine_stock_mappings(self, hoa_don: HoaDonBanHangModel) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định stock mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional stock entry creation dựa trên document status
        - Flexible business rules cho different scenarios
        - Support complex approval workflows

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách stock mappings với canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.SALES_INVOICE_STOCK_CONFIG.copy()

        # ✅ BUSINESS LOGIC: Determine canCreate based on invoice status and business rules

        # Example business logic - customize theo requirements
        if hasattr(hoa_don, 'status'):
            status = getattr(hoa_don, 'status', '1')  # Default APPROVED for backward compatibility

            if status in ['0', '4']:
                # Draft or Cancelled invoices - không tạo stock entries
                for mapping in mappings:
                    mapping['canCreate'] = False

            elif status in ['1', '2', '3']:
                # Approved invoices - tạo stock entries
                for mapping in mappings:
                    mapping['canCreate'] = True

            else:
                # Unknown status - default behavior
                for mapping in mappings:
                    mapping['canCreate'] = True
        else:
            # No status field - default behavior (backward compatibility)
            for mapping in mappings:
                mapping['canCreate'] = True

        # ✅ ADDITIONAL BUSINESS RULES: Add more complex logic here

        # Example: Skip stock entry nếu không có chi tiết có ma_kho và ma_vt
        if hasattr(hoa_don, 'chi_tiet'):
            try:
                chi_tiet_list = hoa_don.chi_tiet.all()
                has_stock_items = any(
                    hasattr(ct, 'ma_kho') and hasattr(ct, 'ma_vt') and
                    getattr(ct, 'ma_kho', None) and getattr(ct, 'ma_vt', None)
                    for ct in chi_tiet_list
                )

                if not has_stock_items:
                    for mapping in mappings:
                        mapping['canCreate'] = False
            except:
                pass  # Ignore errors in business logic evaluation

        return mappings

    def get_by_id(
        self, uuid, entity_slug=None, user_model=None
    ) -> HoaDonBanHangModel:  # noqa: F811,
        """
        Retrieves a HoaDonBanHangModel by its UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonBanHangModel to retrieve.
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.

        Returns
        -------
        HoaDonBanHangModel
            The retrieved HoaDonBanHangModel.

        Raises
        ------
        ObjectDoesNotExist
            If the HoaDonBanHangModel with the given UUID does not exist.
        """
        return self.hoa_don_repository.get_by_id(uuid, entity_slug, user_model)

    def list(
        self, entity_slug=None, user_model=None, **kwargs
    ) -> QuerySet:  # noqa: F811,
        """
        Lists HoaDonBanHangModel instances with optional filtering.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply to the queryset.

        Returns
        -------
        QuerySet
            A queryset of HoaDonBanHangModel instances.
        """
        return self.hoa_don_repository.list(entity_slug, user_model, **kwargs)

    @transaction.atomic
    def create_invoice(  # noqa: C901
        self,
        entity_model,
        hoa_don_data,
        chi_tiet_data=None,
        thanh_toan_data=None,
    ) -> HoaDonBanHangModel:
        """
        ✅ ENHANCED: Tạo HoaDonBanHangModel mới với đầy đủ accounting và stock entries.

        Tự động tạo cả bút toán kế toán (accounting entries) và giao dịch kho (stock entries)
        cho hóa đơn bán hàng sử dụng unified services.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và inventory
        - Status-based business logic cho entry creation
        - Complete audit trail cho cả financial và inventory impacts
        - Transaction safety với automatic rollback

        Parameters
        ----------
        entity_model : EntityModel
            Entity model để liên kết với HoaDonBanHangModel.
        hoa_don_data : dict
            Dữ liệu cho HoaDonBanHangModel.
        chi_tiet_data : list, optional
            Danh sách dictionary chứa dữ liệu cho ChiTietHoaDonBanHangModel, mặc định None.
        thanh_toan_data : list, optional
            Danh sách dictionary chứa dữ liệu cho ThongTinThanhToanHoaDonBanHangModel, mặc định None.

        Returns
        -------
        HoaDonBanHangModel
            HoaDonBanHangModel đã tạo với đầy đủ accounting và stock entries tự động.

        Raises
        ------
        Exception
            Nếu có lỗi trong quá trình tạo invoice, accounting entries, hoặc stock entries
        """
        # Create the invoice
        hoa_don = self.hoa_don_repository.create(
            entity_model=entity_model, **hoa_don_data
        )

        # Create invoice details if provided
        if chi_tiet_data:
            self.chi_tiet_service.bulk_create(
                hoa_don_ban_hang=hoa_don, chi_tiet_data_list=chi_tiet_data
            )

        # Create payment information if provided
        if thanh_toan_data:
            self.thanh_toan_service.bulk_create(
                hoa_don_ban_hang=hoa_don,
                thanh_toan_data_list=thanh_toan_data,
            )

        # ✅ ENHANCED: Tạo đầy đủ cả accounting và stock entries cho hóa đơn
        # Sử dụng unified method để đảm bảo consistency và atomic transaction
        complete_entries_created = self._cong_no_service.create_document_accounting_entries(
            source_document=hoa_don,
            document_type="hóa đơn bán hàng",
            account_mappings=self._determine_accounting_mappings(hoa_don)
        )
        if not complete_entries_created:
            # Raise exception để rollback transaction
            raise Exception(f"Không thể tạo complete entries cho hóa đơn {hoa_don.uuid}. Transaction đã được rollback.")

        return hoa_don

    @transaction.atomic
    def update_invoice(
        self, uuid, hoa_don_data, chi_tiet_data=None, thanh_toan_data=None
    ) -> HoaDonBanHangModel:  # noqa: C901
        """
        Updates an existing HoaDonBanHangModel instance with optional related data.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonBanHangModel to update.
        hoa_don_data : dict
            Data for updating the HoaDonBanHangModel.
        chi_tiet_data : list, optional
            List of dictionaries containing data for updating ChiTietHoaDonBanHangModel instances, by default None.  # noqa: E501
        thanh_toan_data : list, optional
            List of dictionaries containing data for updating ThongTinThanhToanHoaDonBanHangModel instances, by default None.  # noqa: E501

        Returns
        -------
        HoaDonBanHangModel
            The updated HoaDonBanHangModel.

        Raises
        ------
        ObjectDoesNotExist
            If the HoaDonBanHangModel with the given UUID does not exist.
        """
        # Update the invoice
        hoa_don = self.hoa_don_repository.update(uuid, **hoa_don_data)
        # Update invoice details if provided
        if chi_tiet_data:
            self.chi_tiet_service.update_for_hoa_don(
                hoa_don_id=uuid, chi_tiet_data_list=chi_tiet_data
            )

        # Update payment information if provided
        if thanh_toan_data:
            self.thanh_toan_service.update_for_hoa_don(
                hoa_don_id=uuid, thanh_toan_data_list=thanh_toan_data
            )

        # ✅ ENHANCED: Cập nhật đầy đủ cả accounting và stock entries cho hóa đơn
        # Sử dụng unified method để đảm bảo consistency và atomic transaction
        complete_entries_updated = self._cong_no_service.update_document_accounting_entries(
            source_document=hoa_don,
            document_type="hóa đơn bán hàng",
            account_mappings=self._determine_accounting_mappings(hoa_don)
        )
        if not complete_entries_updated:
            # Raise exception để rollback transaction
            raise Exception(f"Không thể cập nhật complete entries cho hóa đơn {hoa_don.uuid}. Transaction đã được rollback.")

        return hoa_don

    def delete_invoice(self, uuid) -> bool:  # noqa: C901
        """
        Deletes a HoaDonBanHangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonBanHangModel to delete.

        Returns
        -------
        bool
            True if the deletion was successful, False otherwise.

        Raises
        ------
        ObjectDoesNotExist
            If the HoaDonBanHangModel with the given UUID does not exist.
        """
        return self.hoa_don_repository.delete(uuid)

    def get_invoice_related_data(self, hoa_don_id, data_type='both'):
        """
        Retrieves related data for a specific HoaDonBanHangModel.

        Parameters
        ----------
        hoa_don_id : UUID
            The UUID of the HoaDonBanHangModel to filter by.
        data_type : str, optional
            Type of data to retrieve: 'details', 'payments', or 'both' (default).

        Returns
        -------
        QuerySet or dict
            - If data_type='details': QuerySet of ChiTietHoaDonBanHangModel
            - If data_type='payments': QuerySet of ThongTinThanhToanHoaDonBanHangModel
            - If data_type='both': dict with 'details' and 'payments' keys
        """
        if data_type == 'details':
            return self.chi_tiet_service.get_by_hoa_don(hoa_don_id)
        elif data_type == 'payments':
            return self.thanh_toan_service.get_by_hoa_don(hoa_don_id)
        elif data_type == 'both':
            return {
                'details': self.chi_tiet_service.get_by_hoa_don(hoa_don_id),
                'payments': self.thanh_toan_service.get_by_hoa_don(hoa_don_id)
            }
        else:
            raise ValueError(f"Invalid data_type: {data_type}. Must be 'details', 'payments', or 'both'.")

    def filter_invoices(
        self,
        customer_id=None,
        status=None,
        entity_slug=None,
        user_model=None,
        **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Retrieves HoaDonBanHangModel instances with flexible filtering.

        Parameters
        ----------
        customer_id : UUID, optional
            The UUID of the customer to filter by.
        status : str, optional
            The status to filter by.
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply.

        Returns
        -------
        QuerySet
            A queryset of HoaDonBanHangModel instances matching the filters.
        """

        filters = {}

        if customer_id:
            filters['ma_kh'] = customer_id
        if status:
            filters['status'] = status

        # Add any additional filters
        filters.update(kwargs)

        return self.hoa_don_repository.list(entity_slug, user_model, **filters)

    def get_unprocessed_invoices(
        self,
        entity_slug: str,
        user_model,
        ngay_ct1: date,
        ngay_ct2: date,
        unit_id: str,
        ma_gd: str,
        auto_generated_receipt_invoice_ids: Optional[List[str]] = None,
        ma_kh: Optional[str] = None,
    ) -> QuerySet:
        """
        Get invoices that do NOT have corresponding auto-generated warehouse receipts.

        This method delegates to the repository layer for data access.

        Parameters
        ----------
        entity_slug : str
            The entity slug to filter by.
        user_model : UserModel
            The user model to check permissions.
        ngay_ct1 : date
            Start date for filtering invoices.
        ngay_ct2 : date
            End date for filtering invoices.
        unit_id : str
            Business unit ID to filter by.
        ma_gd : str
            Transaction type (BH, NB, XK, PO).
        auto_generated_receipt_invoice_ids : Optional[List[str]]
            List of invoice UUIDs that already have auto-generated receipts.
            If provided, these invoices will be excluded from the result.
        ma_kh : Optional[str]
            Customer code to filter by (optional).

        Returns
        -------
        QuerySet
            A queryset of unprocessed HoaDonBanHangModel instances.
        """
        return self.hoa_don_repository.get_unprocessed_invoices(
            entity_slug=entity_slug,
            user_model=user_model,
            ngay_ct1=ngay_ct1,
            ngay_ct2=ngay_ct2,
            unit_id=unit_id,
            ma_gd=ma_gd,
            auto_generated_receipt_invoice_ids=auto_generated_receipt_invoice_ids,
            ma_kh=ma_kh,
        )

    def create_debt_entry(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ ENHANCED: Tạo bút toán công nợ (journal entry) cho hóa đơn bán hàng.

        Sử dụng Unified Accounting Service thay vì ButToanHoaDonBanHangUtils riêng lẻ.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Flexible account mapping configuration
        - Support cả doanh thu và thuế trong một lần gọi
        - Consistent audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để tạo bút toán công nợ.

        Returns
        -------
        bool
            True nếu tạo bút toán thành công, False nếu thất bại.
        """
        try:
            # ✅ BUSINESS LOGIC: Determine accounting mappings based on document status
            account_mappings = self._determine_accounting_mappings(hoa_don)

            return self._cong_no_service.create_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán hàng",
                account_mappings=account_mappings
            )

        except Exception as e:
            # ✅ THROW EXCEPTION: Fail fast để dễ debug và đảm bảo consistency
            raise Exception(f"Lỗi tạo bút toán hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}") from e



    def update_debt_entry(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ ENHANCED: Cập nhật bút toán công nợ (journal entry) cho hóa đơn bán hàng.

        Sử dụng Unified Accounting Service với delete-and-recreate pattern.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Smart recalculation chỉ khi cần thiết
        - Affected accounts tracking
        - Proper audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để cập nhật bút toán công nợ.

        Returns
        -------
        bool
            True nếu cập nhật bút toán thành công, False nếu thất bại.
        """
        try:

            # ✅ BUSINESS LOGIC: Determine accounting mappings based on document status
            account_mappings = self._determine_accounting_mappings(hoa_don)

            return self._cong_no_service.update_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán hàng",
                account_mappings=account_mappings
            )

        except Exception as e:
            # ✅ THROW EXCEPTION: Fail fast để dễ debug và đảm bảo consistency
            raise Exception(f"Lỗi cập nhật bút toán hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}") from e

    def create_stock_entry(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ ENHANCED: Tạo stock entries cho hóa đơn bán hàng.

        Sử dụng Unified Inventory Service để tạo stock transactions.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Flexible stock mapping configuration
        - Support xuất kho cho bán hàng
        - Consistent stock audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để tạo stock entries.

        Returns
        -------
        bool
            True nếu tạo stock entries thành công, False nếu thất bại.
        """
        try:
            # ✅ BUSINESS LOGIC: Determine stock mappings based on document status
            stock_mappings = self._determine_stock_mappings(hoa_don)

            return self._ton_kho_service.create_document_stock_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán hàng",
                stock_mappings=stock_mappings
            )

        except Exception as e:
            # ✅ THROW EXCEPTION: Fail fast để dễ debug và đảm bảo consistency
            raise Exception(f"Lỗi tạo stock entries hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}") from e

    def update_stock_entry(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ ENHANCED: Cập nhật stock entries cho hóa đơn bán hàng.

        Sử dụng Unified Inventory Service với delete-and-recreate pattern.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Smart stock recalculation chỉ khi cần thiết
        - Affected products tracking
        - Proper stock audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để cập nhật stock entries.

        Returns
        -------
        bool
            True nếu cập nhật stock entries thành công, False nếu thất bại.
        """
        try:
            # ✅ BUSINESS LOGIC: Determine stock mappings based on document status
            stock_mappings = self._determine_stock_mappings(hoa_don)

            return self._ton_kho_service.update_document_stock_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán hàng",
                stock_mappings=stock_mappings
            )

        except Exception as e:
            # ✅ THROW EXCEPTION: Fail fast để dễ debug và đảm bảo consistency
            raise Exception(f"Lỗi cập nhật stock entries hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}") from e

    @transaction.atomic
    def create_complete_entries(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ UNIFIED METHOD: Tạo đầy đủ cả accounting và stock entries cho hóa đơn bán hàng.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và inventory
        - Consistent business logic application
        - Proper error handling và rollback
        - Complete audit trail

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để tạo đầy đủ entries.

        Returns
        -------
        bool
            True nếu tạo đầy đủ entries thành công.

        Raises
        ------
        Exception
            Nếu có lỗi trong quá trình tạo accounting hoặc stock entries
        """
        try:
            # ✅ STEP 1: Create accounting entries
            accounting_success = self.create_debt_entry(hoa_don)
            if not accounting_success:
                raise Exception("Failed to create accounting entries")

            # ✅ STEP 2: Create stock entries
            stock_success = self.create_stock_entry(hoa_don)
            if not stock_success:
                raise Exception("Failed to create stock entries")

            return True

        except Exception as e:
            # ✅ ATOMIC ROLLBACK: Transaction will rollback both accounting and stock entries
            raise Exception(f"Lỗi tạo complete entries hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}") from e

    @transaction.atomic
    def update_complete_entries(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ UNIFIED METHOD: Cập nhật đầy đủ cả accounting và stock entries cho hóa đơn bán hàng.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và inventory updates
        - Consistent business logic application
        - Proper error handling và rollback
        - Complete audit trail maintenance

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để cập nhật đầy đủ entries.

        Returns
        -------
        bool
            True nếu cập nhật đầy đủ entries thành công.

        Raises
        ------
        Exception
            Nếu có lỗi trong quá trình cập nhật accounting hoặc stock entries
        """
        try:
            # ✅ STEP 1: Update accounting entries
            accounting_success = self.update_debt_entry(hoa_don)
            if not accounting_success:
                raise Exception("Failed to update accounting entries")

            # ✅ STEP 2: Update stock entries
            stock_success = self.update_stock_entry(hoa_don)
            if not stock_success:
                raise Exception("Failed to update stock entries")

            return True

        except Exception as e:
            # ✅ ATOMIC ROLLBACK: Transaction will rollback both accounting and stock updates
            raise Exception(f"Lỗi cập nhật complete entries hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}") from e
