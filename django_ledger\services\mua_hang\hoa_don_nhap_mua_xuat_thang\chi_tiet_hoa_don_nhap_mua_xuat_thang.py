"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietHoaDonNhapMuaXuatThang Service implementation.
"""

from typing import Any, Dict  # noqa: F401
from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiTietHoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiTietHoaDonNhapMuaXuatThangRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401


class ChiTietHoaDonNhapMuaXuatThangService(BaseService):
    """
    Service class for ChiTietHoaDonNhapMuaXuatThangModel.
    Provides business logic for invoice detail operations.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the ChiTietHoaDonNhapMuaXuatThangRepository.
        """
        super().__init__()
        self.repository = ChiTietHoaDonNhapMuaXuatThangRepository()

    def get_by_invoice(self, hoa_don_uuid) -> QuerySet:  # noqa: C901
        """
        Get ChiTietHoaDonNhapMuaXuatThangModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonNhapMuaXuatThangModel instances for the specified invoice.
        """
        return self.repository.get_by_invoice(hoa_don_uuid)

    def get_by_id(self, uuid, hoa_don_uuid=None) -> ChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Get a ChiTietHoaDonNhapMuaXuatThangModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiTietHoaDonNhapMuaXuatThangModel to retrieve.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiTietHoaDonNhapMuaXuatThangModel
            The retrieved ChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        return self.repository.get_by_id(uuid, hoa_don_uuid)

    def create(self, data: Dict[str, Any]) -> ChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the ChiTietHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ChiTietHoaDonNhapMuaXuatThangModel
            The created ChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        return self.repository.create(**data)

    def update(self, uuid, data: Dict[str, Any], hoa_don_uuid=None) -> ChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiTietHoaDonNhapMuaXuatThangModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietHoaDonNhapMuaXuatThangModel with.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiTietHoaDonNhapMuaXuatThangModel
            The updated ChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        instance = self.get_by_id(uuid, hoa_don_uuid)
        return self.repository.update(instance, **data)

    def delete(self, uuid, hoa_don_uuid=None) -> None:  # noqa: C901
        """
        Delete a ChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiTietHoaDonNhapMuaXuatThangModel to delete.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.
        """
        instance = self.get_by_id(uuid, hoa_don_uuid)
        self.repository.delete(instance)

    def filter_by_material(self, material_uuid, hoa_don_uuid=None) -> QuerySet:  # noqa: C901
        """
        Filter ChiTietHoaDonNhapMuaXuatThangModel instances by material.

        Parameters
        ----------
        material_uuid : UUID
            The UUID of the material to filter by.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonNhapMuaXuatThangModel instances for the specified material.
        """
        return self.repository.filter_by_material(material_uuid, hoa_don_uuid)

    def filter_by_warehouse(self, warehouse_uuid, hoa_don_uuid=None) -> QuerySet:  # noqa: C901
        """
        Filter ChiTietHoaDonNhapMuaXuatThangModel instances by warehouse.

        Parameters
        ----------
        warehouse_uuid : UUID
            The UUID of the warehouse to filter by.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonNhapMuaXuatThangModel instances for the specified warehouse.
        """
        return self.repository.filter_by_warehouse(warehouse_uuid, hoa_don_uuid)

    def get_total_by_invoice(self, hoa_don_uuid) -> dict:  # noqa: C901
        """
        Get total amounts for ChiTietHoaDonNhapMuaXuatThangModel instances by invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        dict
            A dictionary containing total amounts.
        """
        return self.repository.get_total_by_invoice(hoa_don_uuid)

    def calculate_line_totals(self, data: Dict[str, Any]) -> Dict[str, Any]:  # noqa: C901
        """
        Calculate line totals for a ChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing quantity, price, and tax information.

        Returns
        -------
        Dict[str, Any]
            The data with calculated totals.
        """
        # Get values with defaults
        so_luong = data.get('so_luong', 0)
        gia_nt = data.get('gia_nt', 0)
        gia = data.get('gia', 0)
        thue_suat = data.get('thue_suat', 0)
        
        # Calculate amounts
        data['tien_nt'] = so_luong * gia_nt
        data['tien'] = so_luong * gia
        
        # Calculate tax
        data['thue_nt'] = data['tien_nt'] * (thue_suat / 100)
        data['thue'] = data['tien'] * (thue_suat / 100)
        
        # Calculate totals including tax
        data['tt_nt'] = data['tien_nt'] + data['thue_nt']
        data['tt'] = data['tien'] + data['thue']
        
        return data

    def create_with_calculations(self, data: Dict[str, Any]) -> ChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ChiTietHoaDonNhapMuaXuatThangModel instance with automatic calculations.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the ChiTietHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ChiTietHoaDonNhapMuaXuatThangModel
            The created ChiTietHoaDonNhapMuaXuatThangModel instance with calculated totals.
        """
        # Calculate totals
        data = self.calculate_line_totals(data)
        
        # Create the instance
        return self.create(data)

    def update_with_calculations(self, uuid, data: Dict[str, Any], hoa_don_uuid=None) -> ChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ChiTietHoaDonNhapMuaXuatThangModel instance with automatic calculations.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiTietHoaDonNhapMuaXuatThangModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietHoaDonNhapMuaXuatThangModel with.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiTietHoaDonNhapMuaXuatThangModel
            The updated ChiTietHoaDonNhapMuaXuatThangModel instance with calculated totals.
        """
        # Calculate totals
        data = self.calculate_line_totals(data)
        
        # Update the instance
        return self.update(uuid, data, hoa_don_uuid)
