"""
Sales Reports Services.

This module provides business logic services for all sales reports.
"""

try:
    from .bao_cao_tinh_trang_don_hang import BaoCaoTinhTrangDonHangService
except ImportError as e:
    print(f"Warning: Sales reports services import error: {e}")
    pass

try:
    from .bao_cao_so_sanh_ban_hang_hai_ky import BaoCaoSoSanhBanHangHaiKyService
except ImportError as e:
    print(f"Warning: Sales comparison report service import error: {e}")
    pass

__all__ = [
    'BaoCaoTinhTrangDonHangService',
    'BaoCaoSoSanhBanHangHaiKyService',
]
