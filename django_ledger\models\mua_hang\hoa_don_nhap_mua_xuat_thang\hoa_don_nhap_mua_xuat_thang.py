"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Contributed to by:
<PERSON> <<EMAIL>>
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Q  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class HoaDonNhapMuaXuatThangModelManager(models.Manager):
    """
    A custom defined HoaDonNhapMuaXuatThangModel Manager that will act as an interface to handle the  # noqa: E501
    HoaDonNhapMuaXuatThangModel database queries.
    """

    def for_entity(self, entity_slug, user_model):  # noqa: C901
        """
        Returns a QuerySet of HoaDonNhapMuaXuatThangModel associated with a specific EntityModel & UserModel.  # noqa: E501
        May pass an instance of EntityModel or a String representing the EntityModel slug.  # noqa: E501

        Parameters
        ----------
        entity_slug: str or EntityModel
            The entity slug or EntityModel used for filtering the QuerySet.
        user_model
            The UserModel to check for entity access permissions.

        Returns
        -------
        HoaDonNhapMuaXuatThangModelQuerySet
            A QuerySet of HoaDonNhapMuaXuatThangModel with applied filters.
        """
        qs = self.get_queryset()
        if isinstance(entity_slug, str):
            qs = qs.filter(
                Q(entity_model__slug__exact=entity_slug)
                & (
                    Q(entity_model__admin=user_model)
                    | Q(entity_model__managers__in=[user_model])
                )
            )
        else:
            qs = qs.filter(
                Q(entity_model=entity_slug)
                & (
                    Q(entity_model__admin=user_model)
                    | Q(entity_model__managers__in=[user_model])
                )
            )
        return qs


class HoaDonNhapMuaXuatThangModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the HoaDonNhapMuaXuatThangModel database will inherit from.  # noqa: E501
    The HoaDonNhapMuaXuatThangModel inherits functionality from the ChungTuMixIn and CreateUpdateMixIn.

        1. :func:`ChungTuMixIn <django_ledger.models._mixins.chung_tu_mixins.ChungTuMixIn>`
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    entity_model : ForeignKey
        The EntityModel that this invoice belongs to.
    action : CharField
        Action type.
    question_ids : CharField
        Question IDs.
    ma_ngv : ForeignKey
        Employee code.
    ma_kh : ForeignKey
        Customer code.
    ma_kh_x : ForeignKey
        Customer X code.
    tk : ForeignKey
        Account code.
    so_ct0 : CharField
        Document number 0.
    ngay_ct0 : DateField
        Document date 0.
    so_ct2 : CharField
        Document number 2.
    dien_giai : TextField
        Description.
    id : IntegerField
        Original ID.
    unit_id : ForeignKey
        Unit ID.
    i_so_ct : CharField
        Internal document number.
    ma_nk : ForeignKey
        Warehouse code.
    xdatetime2 : CharField
        Extended datetime 2.
    ma_nt : ForeignKey
        Currency code.
    ty_gia : DecimalField
        Exchange rate.
    status : CharField
        Status.
    transfer_yn : BooleanField
        Transfer flag.
    ma_tt : ForeignKey
        Payment method code.
    ma_gd : ForeignKey
        Transaction code.
    t_cp_nt : DecimalField
        Total cost in foreign currency.
    t_cp : DecimalField
        Total cost.
    t_tien_nt0 : DecimalField
        Total amount in foreign currency 0.
    t_tien0 : DecimalField
        Total amount 0.
    t_thue_nt : DecimalField
        Total tax in foreign currency.
    t_thue : DecimalField
        Total tax.
    t_tien_nt : DecimalField
        Total amount in foreign currency.
    t_tien : DecimalField
        Total amount.
    t_so_luong : DecimalField
        Total quantity.
    t_tt_nt : DecimalField
        Total payment in foreign currency.
    t_tt : DecimalField
        Total payment.
    xfile : CharField
        Attached file.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)

    # Entity relationship for multi-tenancy
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity Model'),
        related_name='hoa_don_nhap_mua_xuat_thang',
    )

    # Employee and customer information
    ma_ngv = models.CharField(
        max_length=10,
        verbose_name=_('Mã nhân viên'),
        help_text=_('Employee code'),
        null=True,
        blank=True,
    )
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng'),
        help_text=_('Customer code'),
        related_name='hoa_don_nhap_mua_xuat_thang_kh',
        null=True,
        blank=True,
    )
    ma_kh_x = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng X'),
        help_text=_('Customer X code'),
        related_name='hoa_don_nhap_mua_xuat_thang_kh_x',
        null=True,
        blank=True,
    )

    # Account information
    tk = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản'),
        help_text=_('Account'),
        related_name='hoa_don_nhap_mua_xuat_thang_tk',
        null=True,
        blank=True,
    )

    # Document information
    so_ct0 = models.CharField(
        max_length=20,
        verbose_name=_('Số chứng từ 0'),
        help_text=_('Document number 0'),
    )
    ngay_ct0 = models.DateField(
        verbose_name=_('Ngày chứng từ 0'),
        help_text=_('Document date 0'),
    )
    so_ct2 = models.CharField(
        max_length=20,
        verbose_name=_('Số chứng từ 2'),
        help_text=_('Document number 2'),
    )
    dien_giai = models.TextField(
        verbose_name=_('Diễn giải'),
        help_text=_('Description'),
    )

    # ID and unit information
    id = models.IntegerField(
        verbose_name=_('ID gốc'),
        help_text=_('Original ID'),
        null=True,
        blank=True,
    )
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.CASCADE,
        verbose_name=_('ID đơn vị'),
        help_text=_('Unit ID'),
        related_name='hoa_don_nhap_mua_xuat_thang_unit',
        null=True,
        blank=True,
    )

    # Extended datetime
    xdatetime2 = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('Thời gian X'),
        help_text=_('Extended datetime 2'),
    )

    # Currency information
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã ngoại tệ'),
        help_text=_('Currency code'),
        related_name='hoa_don_nhap_mua_xuat_thang_nt',
    )
    ty_gia = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        verbose_name=_('Tỷ giá'),
        help_text=_('Exchange rate'),
    )

    # Status and classification
    status = models.CharField(
        max_length=10,
        verbose_name=_('Trạng thái'),
        help_text=_('Status'),
    )
    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_('Chuyển khoản'),
        help_text=_('Transfer flag'),
    )
    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thanh toán'),
        help_text=_('Payment method code'),
        related_name='hoa_don_nhap_mua_xuat_thang_tt',
        null=True,
        blank=True,
    )
    ma_gd = models.CharField(
        max_length=10,
        verbose_name=_('Mã giao dịch'),
        help_text=_('Transaction code'),
        blank=True,
    )

    # Total amounts in various currencies
    t_cp_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng chi phí ngoại tệ'),
        help_text=_('Total cost in foreign currency'),
    )
    t_cp = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng chi phí'),
        help_text=_('Total cost'),
    )
    t_tien_nt0 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ 0'),
        help_text=_('Total amount in foreign currency 0'),
    )
    t_tien0 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền 0'),
        help_text=_('Total amount 0'),
    )
    t_thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng thuế ngoại tệ'),
        help_text=_('Total tax in foreign currency'),
    )
    t_thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng thuế'),
        help_text=_('Total tax'),
    )
    t_tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ'),
        help_text=_('Total amount in foreign currency'),
    )
    t_tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền'),
        help_text=_('Total amount'),
    )
    t_so_luong = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        verbose_name=_('Tổng số lượng'),
        help_text=_('Total quantity'),
    )
    t_tt_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thanh toán ngoại tệ'),
        help_text=_('Total payment in foreign currency'),
    )
    t_tt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thanh toán'),
        help_text=_('Total payment'),
    )

    # File attachment
    xfile = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('File đính kèm'),
        help_text=_('Attached file'),
    )

    # Ledger relationship for 1:1 mapping with invoice
    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho hóa đơn này"),
        related_name="hoa_don_nhap_mua_xuat_thang",
    )

    objects = HoaDonNhapMuaXuatThangModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Hóa Đơn Nhập Mua Xuất Tháng')
        verbose_name_plural = _('Hóa Đơn Nhập Mua Xuất Tháng')
        ordering = ['-created']  # Removed -ngay_ct since it's now in ChungTuItem
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ledger']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['ma_nt']),
            models.Index(fields=['status']),
            models.Index(fields=['created']),
            models.Index(fields=['updated']),
        ]
        # Removed unique_together with so_ct since it's now in ChungTuItem

    def __str__(self):  # noqa: C901
        return f"{self.so_ct} - {self.ma_kh}"


class HoaDonNhapMuaXuatThangModel(HoaDonNhapMuaXuatThangModelAbstract):
    """
    Base HoaDonNhapMuaXuatThangModel from Abstract.
    """

    class Meta(HoaDonNhapMuaXuatThangModelAbstract.Meta):
        abstract = False
        db_table = "hoa_don_nhap_mua_xuat_thang"
