"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Data processing utilities for Sales Comparison Report Between Two Periods.
"""

from decimal import Decimal
from typing import Any, Dict, List

from django.db.models import Decimal<PERSON>ield, ExpressionWrapper, F, Sum
from django.db.models.functions import Coalesce


class SalesComparisonUtils:
    """
    Data processing utilities for sales comparison report.

    Handles aggregation, combination, and calculation logic.
    """

    @staticmethod
    def build_aggregation(queryset, config: Dict[str, Any], hoa_don_prefix: str):
        """
        Build dynamic aggregation based on join_field.

        Parameters
        ----------
        queryset : QuerySet
            Base queryset to aggregate
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG
        hoa_don_prefix : str
            Prefix for hoa don fields

        Returns
        -------
        QuerySet
            Aggregated queryset
        """
        # Build values list based on join_field
        values_list = SalesComparisonUtils._build_values_list(config, hoa_don_prefix)

        return (
            queryset.annotate(
                tien_calculated=ExpressionWrapper(
                    Coalesce(F('so_luong'), 0, output_field=DecimalField())
                    * Coalesce(F('gia_nt2'), 0, output_field=DecimalField()),
                    output_field=DecimalField(max_digits=15, decimal_places=2),
                )
            )
            .values(*values_list)
            .annotate(
                so_luong=Sum(Coalesce(F('so_luong'), 0, output_field=DecimalField())),
                tien_amount=Sum('tien_calculated'),
            )
        )

    @staticmethod
    def _build_values_list(config: Dict[str, Any], hoa_don_prefix: str) -> List[str]:
        """
        Build values list for aggregation based on join_field.

        Parameters
        ----------
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG
        hoa_don_prefix : str
            Prefix for hoa don fields

        Returns
        -------
        List[str]
            List of field names for .values()
        """
        join_field = config['join_field']
        ma_field = config['ma_field']
        ten_field = config['ten_field']

        # Always include date field
        date_field = f'{hoa_don_prefix}__chung_tu_item__ngay_ct'

        # Build field paths based on join_field
        if join_field == 'ma_vt':
            # For VatTu - direct relationship
            if hoa_don_prefix == 'hoa_don':
                # HoaDonDichVu uses ma_dv instead of ma_vt
                ma_field_path = f'ma_dv__{ma_field}'
                ten_field_path = f'ma_dv__{ten_field}'
            else:
                ma_field_path = f'{join_field}__{ma_field}'
                ten_field_path = f'{join_field}__{ten_field}'
        elif join_field == 'ma_kh':
            # For Customer - through hoa_don relationship
            ma_field_path = f'{hoa_don_prefix}__{join_field}__{ma_field}'
            ten_field_path = f'{hoa_don_prefix}__{join_field}__{ten_field}'
        else:
            # For other join fields - try direct relationship first
            ma_field_path = f'{join_field}__{ma_field}'
            ten_field_path = f'{join_field}__{ten_field}'

        return [ma_field_path, ten_field_path, date_field]

    @staticmethod
    def combine_and_aggregate_data(
        hdbh_data: List[Dict], hddv_data: List[Dict], config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Combine and aggregate data from both invoice types.

        Parameters
        ----------
        hdbh_data : List[Dict]
            HoaDonBanHang aggregated data
        hddv_data : List[Dict]
            HoaDonDichVu aggregated data
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG

        Returns
        -------
        List[Dict[str, Any]]
            Combined and aggregated data
        """
        # Combine data from both sources
        combined_dict = {}

        # Process HoaDonBanHang data
        for item in hdbh_data:
            key, ten = SalesComparisonUtils._extract_key_and_name(
                item, config, 'hoa_don_ban_hang'
            )
            if key:
                if key not in combined_dict:
                    combined_dict[key] = {'ma': key, 'ten': ten, 'data_points': []}
                combined_dict[key]['data_points'].append(item)

        # Process HoaDonDichVu data
        for item in hddv_data:
            key, ten = SalesComparisonUtils._extract_key_and_name(
                item, config, 'hoa_don'
            )
            if key:
                if key not in combined_dict:
                    combined_dict[key] = {'ma': key, 'ten': ten, 'data_points': []}
                combined_dict[key]['data_points'].append(item)

        return list(combined_dict.values())

    @staticmethod
    def _extract_key_and_name(
        item: Dict, config: Dict[str, Any], hoa_don_prefix: str
    ) -> tuple:
        """
        Extract key and name from aggregated item based on join_field.

        Parameters
        ----------
        item : Dict
            Aggregated data item
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG
        hoa_don_prefix : str
            Prefix for hoa don fields

        Returns
        -------
        tuple
            (key, name) tuple
        """
        join_field = config['join_field']
        ma_field = config['ma_field']
        ten_field = config['ten_field']

        # Build field paths (same logic as _build_values_list)
        if join_field == 'ma_vt':
            if hoa_don_prefix == 'hoa_don':
                ma_field_path = f'ma_dv__{ma_field}'
                ten_field_path = f'ma_dv__{ten_field}'
            else:
                ma_field_path = f'{join_field}__{ma_field}'
                ten_field_path = f'{join_field}__{ten_field}'
        elif join_field == 'ma_kh':
            ma_field_path = f'{hoa_don_prefix}__{join_field}__{ma_field}'
            ten_field_path = f'{hoa_don_prefix}__{join_field}__{ten_field}'
        else:
            ma_field_path = f'{join_field}__{ma_field}'
            ten_field_path = f'{join_field}__{ten_field}'

        # Extract values from item
        key = item.get(ma_field_path)
        ten = item.get(ten_field_path, '')

        # Fallback to generic key if no specific key found
        if not key:
            key = 'TOTAL'
            ten = 'Tổng cộng'

        return key, ten

    @staticmethod
    def process_comparison_calculations(
        combined_data: List[Dict], period_1: Dict[str, Any], period_2: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Process comparison calculations for combined data.

        Parameters
        ----------
        combined_data : List[Dict]
            Combined data from both periods
        period_1 : Dict[str, Any]
            Period 1 date range
        period_2 : Dict[str, Any]
            Period 2 date range

        Returns
        -------
        List[Dict[str, Any]]
            Data with comparison calculations
        """
        result = []

        for item in combined_data:
            # Initialize aggregated values
            sl_kn = sl_kt = tien_kn = tien_kt = Decimal('0.0')

            # Process each data point
            for data_point in item.get('data_points', []):
                date_value = data_point.get(
                    'hoa_don_ban_hang__chung_tu_item__ngay_ct'
                ) or data_point.get('hoa_don__chung_tu_item__ngay_ct')

                so_luong = Decimal(str(data_point.get('so_luong', 0) or 0))
                tien_amount = Decimal(str(data_point.get('tien_amount', 0) or 0))

                if date_value:
                    if period_1['start_date'] <= date_value <= period_1['end_date']:
                        # Period 1 (kỳ này)
                        sl_kn += so_luong
                        tien_kn += tien_amount
                    elif period_2['start_date'] <= date_value <= period_2['end_date']:
                        # Period 2 (kỳ trước)
                        sl_kt += so_luong
                        tien_kt += tien_amount

            # Calculate differences and ratios
            sl_cl = sl_kn - sl_kt
            tien_cl = tien_kn - tien_kt

            # Calculate ratios (avoid division by zero)
            sl_tl = (sl_cl / sl_kt * 100) if sl_kt != 0 else Decimal('0.0')
            tien_tl = (tien_cl / tien_kt * 100) if tien_kt != 0 else Decimal('0.0')

            # Build result item
            result_item = {
                'ma': item['ma'],
                'ten': item['ten'],
                'nhom': '',
                'ten_nhom': '',
                'sl_kn': float(sl_kn),
                'tien_kn': float(tien_kn),
                'sl_kt': float(sl_kt),
                'tien_kt': float(tien_kt),
                'sl_cl': float(sl_cl),
                'sl_tl': float(sl_tl),
                'tien_cl': float(tien_cl),
                'tien_nt_cl': float(tien_cl),  # Assuming same as tien_cl for now
                'tien_tl': float(tien_tl),
            }

            result.append(result_item)

        return result

    @staticmethod
    def apply_group_by(
        records: List[Dict[str, Any]], detail_by: str, group_by: str = None
    ) -> List[Dict[str, Any]]:
        """
        Apply group_by grouping to records following tong_hop_nhap_xuat_ton_theo_kho pattern.

        Parameters
        ----------
        records : List[Dict[str, Any]]
            Records to group
        detail_by : str
            The detail_by code (e.g., "100" for customers)
        group_by : str, optional
            The group_by code (e.g., "101" for customer group 1)

        Returns
        -------
        List[Dict[str, Any]]
            Grouped records with group headers and detail items
        """
        if group_by is None or not records:
            # No grouping, return original records
            return records

        # Import here to avoid circular imports
        from django_ledger.models import GroupModel

        from .constants import get_group_field_name, get_group_type

        # Get group field name from mapping
        group_field = get_group_field_name(detail_by, group_by)
        if not group_field:
            # Invalid combination, return original records
            return records

        # Get group type for filtering Group model
        group_type = get_group_type(group_by)
        if not group_type:
            # No group type mapping, return original records
            return records

        # Get group name mapping from appropriate model
        group_name_mapping = {}
        try:
            if group_by == "200":  # Material cross-category grouping
                from django_ledger.models import VatTuModel

                materials = VatTuModel.objects.all()
                for material in materials:
                    group_name_mapping[str(material.uuid)] = {
                        'ma': material.ma_vt,
                        'ten': material.ten_vt,
                    }
                    # Also map by ma_vt for easier lookup
                    group_name_mapping[material.ma_vt] = {
                        'ma': material.ma_vt,
                        'ten': material.ten_vt,
                    }
            else:
                # Regular group model filtering
                groups = GroupModel.objects.filter(loai_nhom=group_type)
                for group in groups:
                    group_name_mapping[str(group.uuid)] = {
                        'ma': group.ma_nhom,
                        'ten': group.ten_phan_nhom,
                    }
        except Exception:
            # If model query fails, continue without group names
            pass

        # Group records by the specified field
        from collections import defaultdict

        groups = defaultdict(list)

        for record in records:
            # Get group key from the record
            group_key = SalesComparisonUtils._get_group_key_from_record(
                record, group_field
            )
            groups[group_key].append(record)

        # Create grouped records with group headers and details
        grouped_records = []

        # Sort groups for consistent ordering (by group ma/key)
        sorted_groups = sorted(groups.items(), key=lambda x: x[0] or '')

        for group_key, group_records in sorted_groups:
            if not group_records:
                continue

            # Create group header with aggregated totals
            group_header = SalesComparisonUtils._create_group_header(
                group_key, group_records, group_name_mapping
            )
            grouped_records.append(group_header)

            # Add detail records for this group
            # Sort detail records within group by 'ma' field
            sorted_detail_records = sorted(group_records, key=lambda x: x.get('ma', ''))

            for detail_record in sorted_detail_records:
                grouped_records.append(detail_record)

        return grouped_records

    @staticmethod
    def _get_group_key_from_record(record: Dict[str, Any], group_field: str) -> str:
        """
        Get group key from record based on group field.

        For cross-category grouping, this extracts the appropriate field value
        that will be used to group records.

        Parameters
        ----------
        record : Dict[str, Any]
            Record to extract group key from
        group_field : str
            Field name to use for grouping (e.g., "ma_vt", "customer_group1")

        Returns
        -------
        str
            Group key for grouping
        """
        # Handle different group field types
        if group_field == 'ma_vt':
            # Grouping by material - get material UUID/code
            group_key = record.get('ma_vt') or record.get('ma')
        elif group_field in ['customer_group1', 'customer_group2', 'customer_group3']:
            # Grouping by customer group - get from customer data
            group_key = record.get(f'{group_field}_data', {}).get('uuid')
            if not group_key:
                group_key = record.get(group_field)
        elif group_field == 'region':
            # Grouping by region - get from customer or other entity
            group_key = record.get('region_data', {}).get('uuid')
            if not group_key:
                group_key = record.get('region')
        else:
            # Default: try to get the field directly
            group_key = record.get(group_field)

        return str(group_key) if group_key is not None else 'Unknown'

    @staticmethod
    def _create_group_header(
        group_key: str,
        group_records: List[Dict[str, Any]],
        group_name_mapping: Dict[str, Dict[str, str]],
    ) -> Dict[str, Any]:
        """
        Create group header record with aggregated totals.

        Parameters
        ----------
        group_key : str
            The group key
        group_records : List[Dict[str, Any]]
            Records in this group
        group_name_mapping : Dict[str, Dict[str, str]]
            Mapping from group UUID to group info

        Returns
        -------
        Dict[str, Any]
            Group header record with aggregated data
        """
        # Aggregate numeric fields within the same group
        total_sl_kn = sum(float(record.get('sl_kn', 0)) for record in group_records)
        total_tien_kn = sum(float(record.get('tien_kn', 0)) for record in group_records)
        total_sl_kt = sum(float(record.get('sl_kt', 0)) for record in group_records)
        total_tien_kt = sum(float(record.get('tien_kt', 0)) for record in group_records)

        # Calculate comparison fields
        sl_cl = total_sl_kn - total_sl_kt
        tien_cl = total_tien_kn - total_tien_kt
        sl_tl = (sl_cl / total_sl_kt * 100) if total_sl_kt != 0 else 0
        tien_tl = (tien_cl / total_tien_kt * 100) if total_tien_kt != 0 else 0

        # Get group display info
        group_info = group_name_mapping.get(group_key, {})
        group_ma = group_info.get('ma', group_key)
        group_ten = group_info.get('ten', f'Nhóm {group_key}')

        # Create group header record
        group_header = {
            'ma': group_ma,
            'ten': group_ten,
            'sl_kn': float(total_sl_kn),
            'tien_kn': float(total_tien_kn),
            'sl_kt': float(total_sl_kt),
            'tien_kt': float(total_tien_kt),
            'sl_cl': float(sl_cl),
            'sl_tl': float(sl_tl),
            'tien_cl': float(tien_cl),
            'tien_nt_cl': float(tien_cl),  # Assuming same as tien_cl
            'tien_tl': float(tien_tl),
            'is_group_header': True,  # Indicator for group header
        }

        return group_header

    @staticmethod
    def apply_cross_category_grouping(
        entity,
        filters: Dict[str, Any],
        config: Dict[str, Any],
        detail_by: str,
        group_by: str,
    ) -> List[Dict[str, Any]]:
        """
        Apply cross-category grouping logic following the correct flow:

        1. Từ group_by → Kiểm tra xem hóa đơn/chi tiết có match được query đó không
        2. Có data đó → Kết hợp với detail_by để kiểm tra hóa đơn và chi tiết có match không
        3. Format group mapping response → Tạo response theo structure mong muốn

        Parameters
        ----------
        entity : EntityModel
            The entity
        filters : Dict[str, Any]
            Filter parameters
        config : Dict[str, Any]
            Configuration from DETAIL_BY_CONFIG
        detail_by : str
            The detail_by code (e.g., "100" for customers)
        group_by : str
            The group_by code (e.g., "200" for materials)

        Returns
        -------
        List[Dict[str, Any]]
            Cross-category grouped results
        """
        import logging

        # Import modular components
        from .filters import SalesComparisonFilters
        from .mapping import SalesComparisonMapping

        logger = logging.getLogger(__name__)
        logger.info(
            f"Cross-category grouping flow: detail_by={detail_by}, group_by={group_by}"
        )

        # Get period filters
        period_1 = filters.get('period_1', {})
        period_2 = filters.get('period_2', {})

        try:
            # STEP 1: Từ group_by → Kiểm tra xem hóa đơn/chi tiết có match được query đó không
            logger.info("Step 1: Query from group_by to find matching invoices/details")

            group_by_matched_details_p1 = (
                SalesComparisonFilters.query_by_group_by_entity(
                    entity, period_1, 'ngay_ct1', 'ngay_ct2', group_by, filters
                )
            )
            group_by_matched_details_p2 = (
                SalesComparisonFilters.query_by_group_by_entity(
                    entity, period_2, 'ngay_kt1', 'ngay_kt2', group_by, filters
                )
            )

            logger.info(
                f"Group_by query found {group_by_matched_details_p1.count()} details in period 1, "
                f"{group_by_matched_details_p2.count()} in period 2"
            )

            if (
                group_by_matched_details_p1.count() == 0
                and group_by_matched_details_p2.count() == 0
            ):
                logger.info("No data found for group_by query")
                return []

            # STEP 2: Có data đó → Kết hợp với detail_by để kiểm tra hóa đơn và chi tiết có match không
            logger.info(
                "Step 2: Combine with detail_by to check invoice/detail matches"
            )

            combined_data = (
                SalesComparisonMapping.combine_group_by_with_detail_by_internal(
                    group_by_matched_details_p1,
                    group_by_matched_details_p2,
                    detail_by,
                    group_by,
                )
            )

            logger.info(f"Combined data has {len(combined_data)} group entries")

            if not combined_data:
                logger.info("No combined data after detail_by matching")
                return []

            # STEP 3: Format group mapping response → Tạo response theo structure mong muốn
            logger.info("Step 3: Format group mapping response")

            result = SalesComparisonMapping.format_group_mapping_response_internal(
                combined_data, detail_by, group_by
            )

            logger.info(f"Generated {len(result)} records with cross-category grouping")
            return result

        except Exception as e:
            logger.error(f"Error in cross-category grouping flow: {str(e)}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    @staticmethod
    def _extract_keys_and_info(detail, detail_by: str, group_by: str) -> tuple:
        """
        Extract group key, detail key, and info from invoice detail.

        Parameters
        ----------
        detail : ChiTietHoaDonBanHangModel
            Invoice detail record
        detail_by : str
            Detail by code
        group_by : str
            Group by code

        Returns
        -------
        tuple
            (group_key, detail_key, group_info, detail_info)
        """
        group_key = None
        detail_key = None
        group_info = None
        detail_info = None

        # Extract group_by key and info
        if group_by == "200":  # Materials
            if detail.ma_vt:
                group_key = str(detail.ma_vt.uuid)
                group_info = {
                    'ma': detail.ma_vt.ma_vt,
                    'ten': detail.ma_vt.ten_vt,
                    'uuid': group_key,
                }
        elif group_by == "400":  # Departments
            if detail.ma_bp:
                group_key = str(detail.ma_bp.uuid)
                group_info = {
                    'ma': detail.ma_bp.ma_bp,
                    'ten': detail.ma_bp.ten_bp,
                    'uuid': group_key,
                }
        elif group_by == "500":  # Tasks
            if detail.ma_vv:
                group_key = str(detail.ma_vv.uuid)
                group_info = {
                    'ma': detail.ma_vv.ma_vu_viec,
                    'ten': detail.ma_vv.ten_vu_viec,
                    'uuid': group_key,
                }
        # Add more group_by cases as needed

        # Extract detail_by key and info
        if detail_by == "100":  # Customers
            if detail.hoa_don_ban_hang.ma_kh:
                detail_key = str(detail.hoa_don_ban_hang.ma_kh.uuid)
                detail_info = {
                    'ma': detail.hoa_don_ban_hang.ma_kh.customer_code,
                    'ten': detail.hoa_don_ban_hang.ma_kh.customer_name,
                    'uuid': detail_key,
                }
        elif detail_by == "200":  # Materials
            if detail.ma_vt:
                detail_key = str(detail.ma_vt.uuid)
                detail_info = {
                    'ma': detail.ma_vt.ma_vt,
                    'ten': detail.ma_vt.ten_vt,
                    'uuid': detail_key,
                }
        elif detail_by == "400":  # Departments
            if detail.ma_bp:
                detail_key = str(detail.ma_bp.uuid)
                detail_info = {
                    'ma': detail.ma_bp.ma_bp,
                    'ten': detail.ma_bp.ten_bp,
                    'uuid': detail_key,
                }
        # Add more detail_by cases as needed

        return group_key, detail_key, group_info, detail_info
