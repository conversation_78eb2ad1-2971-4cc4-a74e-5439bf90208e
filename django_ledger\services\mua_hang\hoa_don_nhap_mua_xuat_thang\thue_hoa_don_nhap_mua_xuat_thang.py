"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ThueHoaDonNhapMuaXuatThang Service implementation.
"""

from typing import Any, Dict  # noqa: F401
from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ThueHoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ThueHoaDonNhapMuaXuatThangRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401


class ThueHoaDonNhapMuaXuatThangService(BaseService):
    """
    Service class for ThueHoaDonNhapMuaXuatThangModel.
    Provides business logic for invoice tax operations.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the ThueHoaDonNhapMuaXuatThangRepository.
        """
        super().__init__()
        self.repository = ThueHoaDonNhapMuaXuatThangRepository()

    def get_by_invoice(self, hoa_don_uuid) -> QuerySet:  # noqa: C901
        """
        Get ThueHoaDonNhapMuaXuatThangModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ThueHoaDonNhapMuaXuatThangModel instances for the specified invoice.
        """
        return self.repository.get_by_invoice(hoa_don_uuid)

    def get_by_id(self, uuid, hoa_don_uuid=None) -> ThueHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Get a ThueHoaDonNhapMuaXuatThangModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ThueHoaDonNhapMuaXuatThangModel to retrieve.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ThueHoaDonNhapMuaXuatThangModel
            The retrieved ThueHoaDonNhapMuaXuatThangModel instance.
        """
        return self.repository.get_by_id(uuid, hoa_don_uuid)

    def create(self, data: Dict[str, Any]) -> ThueHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ThueHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the ThueHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ThueHoaDonNhapMuaXuatThangModel
            The created ThueHoaDonNhapMuaXuatThangModel instance.
        """
        return self.repository.create(**data)

    def update(self, uuid, data: Dict[str, Any], hoa_don_uuid=None) -> ThueHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ThueHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ThueHoaDonNhapMuaXuatThangModel to update.
        data : Dict[str, Any]
            The data to update the ThueHoaDonNhapMuaXuatThangModel with.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ThueHoaDonNhapMuaXuatThangModel
            The updated ThueHoaDonNhapMuaXuatThangModel instance.
        """
        instance = self.get_by_id(uuid, hoa_don_uuid)
        return self.repository.update(instance, **data)

    def delete(self, uuid, hoa_don_uuid=None) -> None:  # noqa: C901
        """
        Delete a ThueHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ThueHoaDonNhapMuaXuatThangModel to delete.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.
        """
        instance = self.get_by_id(uuid, hoa_don_uuid)
        self.repository.delete(instance)

    def filter_by_tax_code(self, tax_uuid, hoa_don_uuid=None) -> QuerySet:  # noqa: C901
        """
        Filter ThueHoaDonNhapMuaXuatThangModel instances by tax code.

        Parameters
        ----------
        tax_uuid : UUID
            The UUID of the tax to filter by.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        QuerySet
            A queryset of ThueHoaDonNhapMuaXuatThangModel instances for the specified tax.
        """
        return self.repository.filter_by_tax_code(tax_uuid, hoa_don_uuid)

    def get_total_by_invoice(self, hoa_don_uuid) -> dict:  # noqa: C901
        """
        Get total tax amounts for ThueHoaDonNhapMuaXuatThangModel instances by invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        dict
            A dictionary containing total tax amounts.
        """
        return self.repository.get_total_by_invoice(hoa_don_uuid)

    def calculate_tax_amounts(self, data: Dict[str, Any]) -> Dict[str, Any]:  # noqa: C901
        """
        Calculate tax amounts for a ThueHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing base amount and tax rate information.

        Returns
        -------
        Dict[str, Any]
            The data with calculated tax amounts.
        """
        # Get values with defaults
        t_tien_nt = data.get('t_tien_nt', 0)
        t_tien = data.get('t_tien', 0)
        thue_suat = data.get('thue_suat', 0)
        
        # Calculate tax amounts
        data['t_thue_nt'] = t_tien_nt * (thue_suat / 100)
        data['t_thue'] = t_tien * (thue_suat / 100)
        
        return data

    def create_with_calculations(self, data: Dict[str, Any]) -> ThueHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ThueHoaDonNhapMuaXuatThangModel instance with automatic tax calculations.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the ThueHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ThueHoaDonNhapMuaXuatThangModel
            The created ThueHoaDonNhapMuaXuatThangModel instance with calculated tax amounts.
        """
        # Calculate tax amounts
        data = self.calculate_tax_amounts(data)
        
        # Create the instance
        return self.create(data)

    def update_with_calculations(self, uuid, data: Dict[str, Any], hoa_don_uuid=None) -> ThueHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ThueHoaDonNhapMuaXuatThangModel instance with automatic tax calculations.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ThueHoaDonNhapMuaXuatThangModel to update.
        data : Dict[str, Any]
            The data to update the ThueHoaDonNhapMuaXuatThangModel with.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ThueHoaDonNhapMuaXuatThangModel
            The updated ThueHoaDonNhapMuaXuatThangModel instance with calculated tax amounts.
        """
        # Calculate tax amounts
        data = self.calculate_tax_amounts(data)
        
        # Update the instance
        return self.update(uuid, data, hoa_don_uuid)
