"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the HoaDonMuaHangTrongNuocRepository, which handles data access for the  # noqa: E501
HoaDonMuaHangTrongNuocModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import Q, QuerySet  # noqa: F401,

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,
from django_ledger.repositories._utils.chung_tu_item_utils import (  # noqa: F401,
    process_chung_tu_fields_extraction_and_conversion,
    update_instance_with_chung_tu_fields,
)


class HoaDonMuaHangTrongNuocRepository(BaseRepository):
    """
    Repository class for HoaDonMuaHangTrongNuocModel.
    Handles data access operations for domestic purchase invoices.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the HoaDonMuaHangTrongNuocModel.
        """
        super().__init__(model_class=HoaDonMuaHangTrongNuocModel)
        # Initialize any additional attributes here

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for HoaDonMuaHangTrongNuocModel.

        Returns
        -------
        QuerySet
            The base queryset for HoaDonMuaHangTrongNuocModel.
        """
        return self.model_class.objects.all().select_related(
            'entity_model', 'unit_id', 'ma_nt', 'ma_kh',
            # ChungTu fields from ChungTuMixIn
            'chung_tu_item',
            'chung_tu_item__ma_nk',
            'chung_tu',
        ).prefetch_related(
            'chi_tiet_hoa_don_mua_hang_trong_nuoc',
            'chi_phi_hoa_don_mua_hang_trong_nuoc',
            'chi_phi_chi_tiet_hoa_don_mua_hang_trong_nuoc',
            'thue_hoa_don_mua_hang_trong_nuoc',
        )

    def get_by_uuid(
        self, uuid: Union[str, UUID]
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Get a HoaDonMuaHangTrongNuocModel by UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaHangTrongNuocModel to retrieve.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The HoaDonMuaHangTrongNuocModel with the specified UUID, or None if not found.  # noqa: E501
        """
        try:
            return self.get_queryset().get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def get_by_entity_slug(self, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Get all HoaDonMuaHangTrongNuocModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The slug of the entity to filter by.

        Returns
        -------
        QuerySet
            A queryset of HoaDonMuaHangTrongNuocModel instances for the specified entity.  # noqa: E501
        """
        return self.get_queryset().filter(entity_model__slug=entity_slug)

    def get_by_entity_slug_and_status(
        self, entity_slug: str, status: str
    ) -> QuerySet:  # noqa: C901
        """
        Get all HoaDonMuaHangTrongNuocModel instances for a specific entity with a specific status.  # noqa: E501

        Parameters
        ----------
        entity_slug : str
            The slug of the entity to filter by.
        status : str
            The status to filter by.

        Returns
        -------
        QuerySet
            A queryset of HoaDonMuaHangTrongNuocModel instances for the specified entity and status.  # noqa: E501
        """
        return self.get_by_entity_slug(entity_slug).filter(status=status)

    def create(
        self, data: Dict[str, Any]
    ) -> HoaDonMuaHangTrongNuocModel:  # noqa: C901
        """
        Create a new HoaDonMuaHangTrongNuocModel instance using ChungTuMixin pattern.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the HoaDonMuaHangTrongNuocModel with.

        Returns
        -------
        HoaDonMuaHangTrongNuocModel
            The created HoaDonMuaHangTrongNuocModel instance.
        """
        # Use ChungTuMixin utilities for proper handling
        chung_tu_fields, remaining_data = process_chung_tu_fields_extraction_and_conversion(data)

        # Convert UUIDs to model instances for non-ChungTu fields
        remaining_data_copy = self.convert_uuids_to_model_instances(remaining_data)

        # Create instance with ChungTu fields
        from django_ledger.repositories._utils.chung_tu_item_utils import create_instance_with_chung_tu_fields
        return create_instance_with_chung_tu_fields(
            self.model_class, remaining_data_copy, chung_tu_fields
        )

    def update(
        self, instance: HoaDonMuaHangTrongNuocModel, data: Dict[str, Any]
    ) -> HoaDonMuaHangTrongNuocModel:  # noqa: C901
        """
        Update an existing HoaDonMuaHangTrongNuocModel instance using ChungTuMixin pattern.

        Parameters
        ----------
        instance : HoaDonMuaHangTrongNuocModel
            The HoaDonMuaHangTrongNuocModel instance to update.
        data : Dict[str, Any]
            The data to update the HoaDonMuaHangTrongNuocModel with.

        Returns
        -------
        HoaDonMuaHangTrongNuocModel
            The updated HoaDonMuaHangTrongNuocModel instance.
        """
        # Use ChungTuMixin utilities for proper handling
        return update_instance_with_chung_tu_fields(
            instance, data, self.convert_uuids_to_model_instances
        )

    def create_instance(self, data: Dict[str, Any]) -> HoaDonMuaHangTrongNuocModel:  # noqa: C901
        """
        Create a new HoaDonMuaHangTrongNuocModel instance (alias for create method).

        Parameters
        ----------
        data : Dict[str, Any]
            The data for the new HoaDonMuaHangTrongNuocModel.

        Returns
        -------
        HoaDonMuaHangTrongNuocModel
            The created HoaDonMuaHangTrongNuocModel instance.
        """
        return self.create(data)

    def delete(
        self, instance: HoaDonMuaHangTrongNuocModel
    ) -> bool:  # noqa: C901
        """
        Delete a HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        instance : HoaDonMuaHangTrongNuocModel
            The HoaDonMuaHangTrongNuocModel instance to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        try:
            instance.delete()
            return True
        except Exception:
            return False

    def search(self, entity_slug: str, query: str) -> QuerySet:  # noqa: C901
        """
        Search for HoaDonMuaHangTrongNuocModel instances by various fields.

        Parameters
        ----------
        entity_slug : str
            The slug of the entity to filter by.
        query : str
            The search query.

        Returns
        -------
        QuerySet
            A queryset of HoaDonMuaHangTrongNuocModel instances matching the search query.  # noqa: E501
        """
        return self.get_by_entity_slug(entity_slug).filter(
            Q(so_ct__icontains=query)
            | Q(ten_kh__icontains=query)
            | Q(ma_kh__icontains=query)
            | Q(so_ct0__icontains=query)
        )

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        the so_ct field which requires a ChungTu instance.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # First, use the base implementation to handle common patterns
        data_copy = super().convert_uuids_to_model_instances(data)
        # Import ChungTuRepository
        from django_ledger.repositories.chung_tu.chung_tu import (  # noqa: F401,
            ChungTuRepository,
        )

        chung_tu_repo = ChungTuRepository()
        # Specifically handle so_ct field if it's a string (UUID)
        if "so_ct" in data_copy and isinstance(data_copy["so_ct"], str):
            try:
                # Try to parse as UUID to validate
                uuid_obj = UUID(data_copy["so_ct"])
                # Get the ChungTu instance using the repository
                chung_tu_instance = chung_tu_repo.get_chung_tu_by_uuid(
                    str(uuid_obj)
                )
                if chung_tu_instance:
                    # Replace the UUID string with the ChungTu instance
                    data_copy["so_ct"] = chung_tu_instance
            except ValueError:
                # If it's not a valid UUID, keep as is
                pass

        # Similarly handle so_ct0 and so_ct2 fields
        if "so_ct0" in data_copy and isinstance(data_copy["so_ct0"], str):
            try:
                uuid_obj = UUID(data_copy["so_ct0"])
                chung_tu_instance = chung_tu_repo.get_chung_tu_by_uuid(
                    str(uuid_obj)
                )
                if chung_tu_instance:
                    data_copy["so_ct0"] = chung_tu_instance
            except ValueError:
                pass

        if "so_ct2" in data_copy and isinstance(data_copy["so_ct2"], str):
            try:
                uuid_obj = UUID(data_copy["so_ct2"])
                chung_tu_instance = chung_tu_repo.get_chung_tu_by_uuid(
                    str(uuid_obj)
                )
                if chung_tu_instance:
                    data_copy["so_ct2"] = chung_tu_instance
            except ValueError:
                pass

        return data_copy
