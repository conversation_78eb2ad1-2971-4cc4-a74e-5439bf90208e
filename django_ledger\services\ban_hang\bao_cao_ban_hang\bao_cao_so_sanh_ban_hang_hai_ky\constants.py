"""
Constants for Sales Comparison Report Between Two Periods

This module contains configuration mappings for different detail_by options
used in sales comparison reports.

Author: AI Assistant
Date: July 1, 2025
"""

from django_ledger.models import (
    BoPhanModel,
    ContractModel,
    CustomerModel,
    EntityUnitModel,
    KheUocModel,
    KhoHangModel,
    NhanVienModel,
    PhiModel,
    VatTuModel,
    VuViecModel,
)

# Detail_by configuration mapping for sales comparison reports
DETAIL_BY_CONFIG = {
    # Khách hàng và nhóm khách hàng
    "200": {  # Khách hàng
        "model": CustomerModel,
        "ma_field": "customer_code",
        "ten_field": "customer_name",
        "join_field": "ma_kh",
        "description": "Khách hàng",
    },
    "210": {  # Nhóm khách hàng 1
        "model": CustomerModel,
        "ma_field": "nh_kh1",
        "ten_field": "nh_kh1",
        "join_field": "ma_kh",
        "description": "Nhóm khách hàng 1",
    },
    "220": {  # Nhóm khách hàng 2
        "model": CustomerModel,
        "ma_field": "nh_kh2",
        "ten_field": "nh_kh2",
        "join_field": "ma_kh",
        "description": "Nhóm khách hàng 2",
    },
    "230": {  # Nhóm khách hàng 3
        "model": CustomerModel,
        "ma_field": "nh_kh3",
        "ten_field": "nh_kh3",
        "join_field": "ma_kh",
        "description": "Nhóm khách hàng 3",
    },
    # Vật tư và nhóm vật tư
    "300": {  # Vật tư
        "model": VatTuModel,
        "ma_field": "ma_vt",
        "ten_field": "ten_vt",
        "join_field": "ma_vt",
        "description": "Vật tư",
    },
    "320": {  # Nhóm vật tư 1
        "model": VatTuModel,
        "ma_field": "nh_vt1",
        "ten_field": "nh_vt1",
        "join_field": "ma_vt",
        "description": "Nhóm vật tư 1",
    },
    "330": {  # Nhóm vật tư 2
        "model": VatTuModel,
        "ma_field": "nh_vt2",
        "ten_field": "nh_vt2",
        "join_field": "ma_vt",
        "description": "Nhóm vật tư 2",
    },
    "340": {  # Nhóm vật tư 3
        "model": VatTuModel,
        "ma_field": "nh_vt3",
        "ten_field": "nh_vt3",
        "join_field": "ma_vt",
        "description": "Nhóm vật tư 3",
    },
    # Đơn vị tổ chức
    "700": {  # Đơn vị
        "model": EntityUnitModel,
        "ma_field": "ma_unit",
        "ten_field": "ten_unit",
        "join_field": "ma_unit",
        "description": "Đơn vị",
    },
    "810": {  # Bộ phận
        "model": BoPhanModel,
        "ma_field": "ma_bp",
        "ten_field": "ten_bp",
        "join_field": "ma_bp",
        "description": "Bộ phận",
    },
    "820": {  # Vụ việc
        "model": VuViecModel,
        "ma_field": "ma_vv",
        "ten_field": "ten_vv",
        "join_field": "ma_vv",
        "description": "Vụ việc",
    },
    # Hợp đồng và khế ước
    "830": {  # Hợp đồng
        "model": ContractModel,
        "ma_field": "ma_hd",
        "ten_field": "ten_hd",
        "join_field": "ma_hd",
        "description": "Hợp đồng",
    },
    "840": {  # Khế ước
        "model": KheUocModel,
        "ma_field": "ma_ku",
        "ten_field": "ten_ku",
        "join_field": "ma_ku",
        "description": "Khế ước",
    },
    # Chi phí và phí
    "850": {  # Phí
        "model": PhiModel,
        "ma_field": "ma_phi",
        "ten_field": "ten_phi",
        "join_field": "ma_phi",
        "description": "Phí",
    },
    # Sản phẩm và sản xuất
    "860": {  # Sản phẩm
        "model": VatTuModel,  # Assuming SanPham uses VatTu model
        "ma_field": "ma_sp",
        "ten_field": "ten_sp",
        "join_field": "ma_sp",
        "description": "Sản phẩm",
    },
    # Nhân sự
    "910": {  # Nhân viên bán hàng
        "model": NhanVienModel,
        "ma_field": "ma_nv",
        "ten_field": "ten_nv",
        "join_field": "ma_nvbh",
        "description": "Nhân viên bán hàng",
    },
}


# Helper functions for working with DETAIL_BY_CONFIG
def get_detail_by_config(detail_by: str) -> dict:
    """
    Get configuration for a specific detail_by option.

    Args:
        detail_by: The detail_by option code (e.g., "100", "200")

    Returns:
        Configuration dictionary or None if not found
    """
    return DETAIL_BY_CONFIG.get(detail_by)


def get_available_detail_by_options() -> dict:
    """
    Get all available detail_by options with descriptions.

    Returns:
        Dictionary mapping detail_by codes to descriptions
    """
    return {
        code: config.get("description", f"Option {code}")
        for code, config in DETAIL_BY_CONFIG.items()
    }


def is_valid_detail_by(detail_by: str) -> bool:
    """
    Check if a detail_by option is valid.

    Args:
        detail_by: The detail_by option code to validate

    Returns:
        True if valid, False otherwise
    """
    return detail_by in DETAIL_BY_CONFIG


# Group mappings for easier categorization
DETAIL_BY_GROUPS = {
    "customer": ["100", "101", "102", "103"],  # Khách hàng và nhóm
    "material": ["200", "201", "202", "203"],  # Vật tư và nhóm
    "organization": ["300", "400", "500"],  # Đơn vị, bộ phận, vụ việc
    "contract": ["600", "800"],  # Hợp đồng, khế ước
    "warehouse": ["700"],  # Kho hàng
    "cost": ["900"],  # Phí
    "product": ["1000", "1100"],  # Sản phẩm, lệnh sản xuất
    "staff": ["1200"],  # Nhân viên
}


def get_detail_by_group(detail_by: str) -> str:
    """
    Get the group category for a detail_by option.

    Args:
        detail_by: The detail_by option code

    Returns:
        Group name or "unknown" if not found
    """
    for group, options in DETAIL_BY_GROUPS.items():
        if detail_by in options:
            return group
    return "unknown"


# Group_by field mapping for hierarchical grouping
# Maps detail_by codes to their possible group_by field names
GROUP_BY_FIELD_MAPPING = {
    # Customer detail_by options can be grouped by customer groups, regions, or materials
    "200": {  # Khách hàng
        "210": "customer_group1",  # Nhóm khách hàng 1
        "220": "customer_group2",  # Nhóm khách hàng 2
        "230": "customer_group3",  # Nhóm khách hàng 3
        "300": "ma_vt",  # Vật tư (cross-category)
        "700": "region",  # Đơn vị (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    "210": {  # Nhóm khách hàng 1 (can be grouped by other customer groups)
        "220": "customer_group2",  # Nhóm khách hàng 2
        "230": "customer_group3",  # Nhóm khách hàng 3
        "700": "region",  # Đơn vị
    },
    "220": {  # Nhóm khách hàng 2
        "210": "customer_group1",  # Nhóm khách hàng 1
        "230": "customer_group3",  # Nhóm khách hàng 3
        "700": "region",  # Đơn vị
    },
    "230": {  # Nhóm khách hàng 3
        "210": "customer_group1",  # Nhóm khách hàng 1
        "220": "customer_group2",  # Nhóm khách hàng 2
        "700": "region",  # Đơn vị
    },
    # Material detail_by options can be grouped by material groups or customers
    "300": {  # Vật tư
        "200": "ma_kh",  # Khách hàng (cross-category)
        "210": "customer_group1",  # Nhóm khách hàng 1 (cross-category)
        "220": "customer_group2",  # Nhóm khách hàng 2 (cross-category)
        "230": "customer_group3",  # Nhóm khách hàng 3 (cross-category)
        "320": "nh_vt1",  # Nhóm vật tư 1
        "330": "nh_vt2",  # Nhóm vật tư 2
        "340": "nh_vt3",  # Nhóm vật tư 3
        "700": "region",  # Đơn vị (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    "320": {  # Nhóm vật tư 1
        "330": "nh_vt2",  # Nhóm vật tư 2
        "340": "nh_vt3",  # Nhóm vật tư 3
    },
    "330": {  # Nhóm vật tư 2
        "320": "nh_vt1",  # Nhóm vật tư 1
        "340": "nh_vt3",  # Nhóm vật tư 3
    },
    "340": {  # Nhóm vật tư 3
        "320": "nh_vt1",  # Nhóm vật tư 1
        "330": "nh_vt2",  # Nhóm vật tư 2
    },
    # Đơn vị detail_by options can be grouped by various entities
    "700": {  # Đơn vị
        "200": "ma_kh",  # Khách hàng (cross-category)
        "300": "ma_vt",  # Vật tư (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    # Department detail_by options can be grouped by various entities
    "810": {  # Bộ phận
        "200": "ma_kh",  # Khách hàng (cross-category)
        "300": "ma_vt",  # Vật tư (cross-category)
        "700": "region",  # Đơn vị (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    # Task detail_by options can be grouped by various entities
    "820": {  # Vụ việc
        "200": "ma_kh",  # Khách hàng (cross-category)
        "300": "ma_vt",  # Vật tư (cross-category)
        "700": "region",  # Đơn vị (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    # Contract detail_by options can be grouped by various entities
    "830": {  # Hợp đồng
        "200": "ma_kh",  # Khách hàng (cross-category)
        "300": "ma_vt",  # Vật tư (cross-category)
        "700": "region",  # Đơn vị (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    # Khế ước detail_by options can be grouped by various entities
    "840": {  # Khế ước
        "200": "ma_kh",  # Khách hàng (cross-category)
        "300": "ma_vt",  # Vật tư (cross-category)
        "700": "region",  # Đơn vị (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    # Phí detail_by options can be grouped by various entities
    "850": {  # Phí
        "200": "ma_kh",  # Khách hàng (cross-category)
        "300": "ma_vt",  # Vật tư (cross-category)
        "700": "region",  # Đơn vị (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    # Sản phẩm detail_by options can be grouped by various entities
    "860": {  # Sản phẩm
        "200": "ma_kh",  # Khách hàng (cross-category)
        "300": "ma_vt",  # Vật tư (cross-category)
        "700": "region",  # Đơn vị (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
        "910": "ma_nvbh",  # Nhân viên bán hàng (cross-category)
    },
    # Sales staff detail_by options can be grouped by various entities
    "910": {  # Nhân viên bán hàng
        "200": "ma_kh",  # Khách hàng (cross-category)
        "300": "ma_vt",  # Vật tư (cross-category)
        "700": "region",  # Đơn vị (cross-category)
        "810": "ma_bp",  # Bộ phận (cross-category)
        "820": "ma_vv",  # Vụ việc (cross-category)
    },
}


# Group type mapping for loai_nhom filtering
GROUP_TYPE_MAPPING = {
    "210": "KH1",  # Customer Group 1
    "220": "KH2",  # Customer Group 2
    "230": "KH3",  # Customer Group 3
    "300": "VT",  # Material (for cross-category grouping)
    "320": "VT1",  # Material Group 1
    "330": "VT2",  # Material Group 2
    "340": "VT3",  # Material Group 3
}


def get_group_field_name(detail_by: str, group_by: str) -> str:
    """
    Get the field name for grouping based on detail_by and group_by codes.

    Parameters
    ----------
    detail_by : str
        The detail_by code (e.g., "100" for customers)
    group_by : str
        The group_by code (e.g., "101" for customer group 1)

    Returns
    -------
    str
        The field name to use for grouping (e.g., "customer_group1")
        Returns None if combination is not valid
    """
    if detail_by in GROUP_BY_FIELD_MAPPING:
        return GROUP_BY_FIELD_MAPPING[detail_by].get(group_by)
    return None


def get_group_type(group_by: str) -> str:
    """
    Get the loai_nhom value for filtering Group model.

    Parameters
    ----------
    group_by : str
        The group_by code (e.g., "101" for customer group 1)

    Returns
    -------
    str
        The loai_nhom value (e.g., "KH1")
        Returns None if not found
    """
    return GROUP_TYPE_MAPPING.get(group_by)


def is_valid_group_by_combination(detail_by: str, group_by: str) -> bool:
    """
    Check if detail_by and group_by combination is valid.

    Parameters
    ----------
    detail_by : str
        The detail_by code
    group_by : str
        The group_by code

    Returns
    -------
    bool
        True if combination is valid, False otherwise
    """
    # Cannot group by self
    if detail_by == group_by:
        return False

    # Check if combination exists in mapping
    return get_group_field_name(detail_by, group_by) is not None


def is_cross_category_grouping(detail_by: str, group_by: str) -> bool:
    """
    Check if this is cross-category grouping (different entity types).

    Parameters
    ----------
    detail_by : str
        The detail_by code
    group_by : str
        The group_by code

    Returns
    -------
    bool
        True if cross-category grouping
    """
    # Cross-category combinations
    cross_category_combinations = [
        ("200", "300"),  # Customers grouped by Materials
        ("300", "200"),  # Materials grouped by Customers
        ("200", "810"),  # Customers grouped by Departments
        ("810", "200"),  # Departments grouped by Customers
        ("200", "820"),  # Customers grouped by Tasks
        ("820", "200"),  # Tasks grouped by Customers
        ("300", "810"),  # Materials grouped by Departments
        ("810", "300"),  # Departments grouped by Materials
        ("300", "820"),  # Materials grouped by Tasks
        ("820", "300"),  # Tasks grouped by Materials
        ("810", "820"),  # Departments grouped by Tasks
        ("820", "810"),  # Tasks grouped by Departments
        ("200", "700"),  # Customers grouped by Units
        ("700", "200"),  # Units grouped by Customers
        ("300", "700"),  # Materials grouped by Units
        ("700", "300"),  # Units grouped by Materials
        ("810", "700"),  # Departments grouped by Units
        ("700", "810"),  # Units grouped by Departments
        ("820", "700"),  # Tasks grouped by Units
        ("700", "820"),  # Units grouped by Tasks
        ("200", "910"),  # Customers grouped by Sales Staff
        ("910", "200"),  # Sales Staff grouped by Customers
        ("300", "910"),  # Materials grouped by Sales Staff
        ("910", "300"),  # Sales Staff grouped by Materials
        ("810", "910"),  # Departments grouped by Sales Staff
        ("910", "810"),  # Sales Staff grouped by Departments
        ("820", "910"),  # Tasks grouped by Sales Staff
        ("910", "820"),  # Sales Staff grouped by Tasks
        ("200", "830"),  # Customers grouped by Contracts
        ("830", "200"),  # Contracts grouped by Customers
        ("300", "830"),  # Materials grouped by Contracts
        ("830", "300"),  # Contracts grouped by Materials
        ("200", "840"),  # Customers grouped by Agreements
        ("840", "200"),  # Agreements grouped by Customers
        ("300", "840"),  # Materials grouped by Agreements
        ("840", "300"),  # Agreements grouped by Materials
        ("200", "850"),  # Customers grouped by Fees
        ("850", "200"),  # Fees grouped by Customers
        ("300", "850"),  # Materials grouped by Fees
        ("850", "300"),  # Fees grouped by Materials
        ("200", "860"),  # Customers grouped by Products
        ("860", "200"),  # Products grouped by Customers
        ("300", "860"),  # Materials grouped by Products
        ("860", "300"),  # Products grouped by Materials
    ]

    return (detail_by, group_by) in cross_category_combinations
