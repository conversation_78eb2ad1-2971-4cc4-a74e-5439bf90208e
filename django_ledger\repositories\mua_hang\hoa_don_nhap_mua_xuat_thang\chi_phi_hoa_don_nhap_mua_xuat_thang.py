"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiPhiHoaDonNhapMuaXuatThang Repository implementation.
"""

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiHoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401


class ChiPhiHoaDonNhapMuaXuatThangRepository(BaseRepository):
    """
    Repository class for ChiPhiHoaDonNhapMuaXuatThangModel.
    Handles data access operations for invoice costs.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the ChiPhiHoaDonNhapMuaXuatThangModel.
        """
        super().__init__(model_class=ChiPhiHoaDonNhapMuaXuatThangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for ChiPhiHoaDonNhapMuaXuatThangModel with optimized relationships.

        Returns
        -------
        QuerySet
            The base queryset for ChiPhiHoaDonNhapMuaXuatThangModel with related objects.
        """
        return (
            self.model_class.objects.all()
            .select_related(
                "hoa_don",
                "ma_cp",
            )
        )

    def get_by_invoice(self, hoa_don_uuid) -> QuerySet:  # noqa: C901
        """
        Get ChiPhiHoaDonNhapMuaXuatThangModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiPhiHoaDonNhapMuaXuatThangModel instances for the specified invoice.
        """
        return self.get_queryset().filter(hoa_don__uuid=hoa_don_uuid)

    def get_by_id(self, uuid, hoa_don_uuid=None) -> ChiPhiHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Get a ChiPhiHoaDonNhapMuaXuatThangModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiPhiHoaDonNhapMuaXuatThangModel to retrieve.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiPhiHoaDonNhapMuaXuatThangModel
            The retrieved ChiPhiHoaDonNhapMuaXuatThangModel instance.

        Raises
        ------
        ObjectDoesNotExist
            If the ChiPhiHoaDonNhapMuaXuatThangModel with the given UUID does not exist.
        """
        qs = self.get_queryset()
        if hoa_don_uuid:
            qs = self.get_by_invoice(hoa_don_uuid)
        return qs.get(uuid=uuid)

    def create(self, **kwargs) -> ChiPhiHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ChiPhiHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        **kwargs : dict
            The data to create the ChiPhiHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ChiPhiHoaDonNhapMuaXuatThangModel
            The created ChiPhiHoaDonNhapMuaXuatThangModel instance.
        """
        return self.model_class.objects.create(**kwargs)

    def update(self, instance: ChiPhiHoaDonNhapMuaXuatThangModel, **kwargs) -> ChiPhiHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ChiPhiHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        instance : ChiPhiHoaDonNhapMuaXuatThangModel
            The instance to update.
        **kwargs : dict
            The data to update the instance with.

        Returns
        -------
        ChiPhiHoaDonNhapMuaXuatThangModel
            The updated ChiPhiHoaDonNhapMuaXuatThangModel instance.
        """
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(self, instance: ChiPhiHoaDonNhapMuaXuatThangModel) -> None:  # noqa: C901
        """
        Delete a ChiPhiHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        instance : ChiPhiHoaDonNhapMuaXuatThangModel
            The instance to delete.
        """
        instance.delete()

    def get_total_by_invoice(self, hoa_don_uuid) -> dict:  # noqa: C901
        """
        Get total cost amounts for ChiPhiHoaDonNhapMuaXuatThangModel instances by invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        dict
            A dictionary containing total cost amounts.
        """
        from django.db.models import Sum
        
        qs = self.get_by_invoice(hoa_don_uuid)
        return qs.aggregate(
            total_cost=Sum('tien_cp'),
            total_cost_nt=Sum('tien_cp_nt'),
        )
