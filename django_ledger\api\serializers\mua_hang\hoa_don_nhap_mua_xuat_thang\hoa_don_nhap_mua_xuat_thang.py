"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

HoaDonNhapMuaXuatThang Serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.chi_phi_chi_tiet_hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiChiTietHoaDonNhapMuaXuatThangSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.chi_phi_hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiHoaDonNhapMuaXuatThangSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.chi_tiet_hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiTietHoaDonNhapMuaXuatThangSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.thue_hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ThueHoaDonNhapMuaXuatThangSerializer,
)
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    HoaDonNhapMuaXuatThangModel,
)


class HoaDonNhapMuaXuatThangSerializer(serializers.ModelSerializer):
    """
    Serializer for HoaDonNhapMuaXuatThangModel.
    Used for read operations and API responses.
    """

    # Explicit field definitions for ChungTu fields (properties)
    i_so_ct = serializers.IntegerField(required=False, allow_null=True)
    ma_nk = serializers.UUIDField(required=False, allow_null=True)
    so_ct = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    ngay_ct = serializers.DateField(required=False, allow_null=True)
    ngay_lct = serializers.DateField(required=False, allow_null=True)

    # Related object data
    ma_kh_data = serializers.SerializerMethodField()
    ma_kh_x_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField()

    # Related collections (using SerializerMethodField to avoid RelatedManager issues)
    chi_tiet = serializers.SerializerMethodField()
    chi_phi = serializers.SerializerMethodField()
    chi_phi_vat_tu = serializers.SerializerMethodField()
    thue = serializers.SerializerMethodField()

    class Meta:
        model = HoaDonNhapMuaXuatThangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated", "entity_model"]

    def get_ma_kh_data(self, obj):  # noqa: C901
        """Get customer data."""
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_kh_x_data(self, obj):  # noqa: C901
        """Get customer X data."""
        if obj.ma_kh_x:
            return CustomerModelSerializer(obj.ma_kh_x).data
        return None

    def get_tk_data(self, obj):  # noqa: C901
        """Get account data."""
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        """Get payment method data."""
        if obj.ma_tt:
            return {
                'uuid': str(obj.ma_tt.uuid),
                'ten_tt': getattr(obj.ma_tt, 'ten_tt', None),
            }
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        """Get currency data."""
        if obj.ma_nt:
            return {
                'uuid': str(obj.ma_nt.uuid),
                'ten_nt': getattr(obj.ma_nt, 'ten_nt', None),
                'ma_nt': getattr(obj.ma_nt, 'ma_nt', None),
            }
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """Get unit data."""
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_chi_tiet(self, obj):  # noqa: C901
        """Get invoice details."""
        from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.chi_tiet_hoa_don_nhap_mua_xuat_thang import (
            ChiTietHoaDonNhapMuaXuatThangSerializer,
        )

        return ChiTietHoaDonNhapMuaXuatThangSerializer(
            obj.chi_tiet.all(), many=True
        ).data

    def get_chi_phi(self, obj):  # noqa: C901
        """Get invoice costs."""
        from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.chi_phi_hoa_don_nhap_mua_xuat_thang import (
            ChiPhiHoaDonNhapMuaXuatThangSerializer,
        )

        return ChiPhiHoaDonNhapMuaXuatThangSerializer(obj.chi_phi.all(), many=True).data

    def get_chi_phi_vat_tu(self, obj):  # noqa: C901
        """Get invoice material costs."""
        from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.chi_phi_chi_tiet_hoa_don_nhap_mua_xuat_thang import (
            ChiPhiChiTietHoaDonNhapMuaXuatThangSerializer,
        )

        return ChiPhiChiTietHoaDonNhapMuaXuatThangSerializer(
            obj.chi_phi_vat_tu.all(), many=True
        ).data

    def get_thue(self, obj):  # noqa: C901
        """Get invoice taxes."""
        from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.thue_hoa_don_nhap_mua_xuat_thang import (
            ThueHoaDonNhapMuaXuatThangSerializer,
        )

        return ThueHoaDonNhapMuaXuatThangSerializer(obj.thue.all(), many=True).data


class HoaDonNhapMuaXuatThangCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for HoaDonNhapMuaXuatThangModel.
    Used for create and update operations.

    This serializer handles the conversion between JSON data and HoaDonNhapMuaXuatThangModel instances,
    supporting both creation of new instances and updating existing ones.

    Key features:
    - Accepts nested data for related collections (chi_tiet, chi_phi, etc.)
    - Performs validation on the entire data structure
    - Accepts UUID references for foreign key fields
    """

    # Explicit field definitions for ChungTu fields (properties)
    i_so_ct = serializers.IntegerField(required=False, allow_null=True)
    ma_nk = serializers.UUIDField(required=False, allow_null=True)
    so_ct = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    ngay_ct = serializers.DateField(required=False, allow_null=True)
    ngay_lct = serializers.DateField(required=False, allow_null=True)

    # Nested serializers for related objects
    chi_tiet = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True,
        help_text="List of invoice details",
    )
    chi_phi = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True,
        help_text="List of invoice costs",
    )
    chi_phi_vat_tu = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True,
        help_text="List of detailed invoice costs",
    )
    thue = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True,
        help_text="List of invoice taxes",
    )

    class Meta:
        model = HoaDonNhapMuaXuatThangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated", "entity_model"]

    def validate(self, data):
        """
        Validate the input data for HoaDonNhapMuaXuatThangModel.

        Parameters
        ----------
        data : dict
            The input data to validate.

        Returns
        -------
        dict
            The validated data.

        Raises
        ------
        serializers.ValidationError
            If the data is invalid.
        """
        # Add custom validation logic here
        return data
