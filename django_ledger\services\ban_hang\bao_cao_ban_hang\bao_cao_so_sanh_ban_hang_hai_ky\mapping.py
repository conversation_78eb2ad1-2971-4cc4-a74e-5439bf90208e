"""
Sales Comparison Report - Data Mapping Module

This module handles all data mapping logic for cross-category grouping,
entity key extraction, and response formatting.
"""

import logging
from collections import defaultdict
from decimal import Decimal
from typing import Any, Dict, List, Tuple

logger = logging.getLogger(__name__)


class SalesComparisonMapping:
    """
    Handles data mapping and entity extraction for sales comparison reports.
    """

    @staticmethod
    def _extract_group_by_info(detail, group_by: str) -> <PERSON><PERSON>[str, Dict[str, Any]]:
        """
        Extract group_by key and info from detail record.

        Query levels:
        - Invoice level: Customers (200), Units (700), Sales Staff (910)
        - Detail level: Materials (300), Departments (810), Tasks (820),
                       Contracts (830), Agreements (840), Fees (850), Products (860)

        Parameters
        ----------
        detail : ChiTietHoaDonBanHangModel
            Invoice detail record
        group_by : str
            Group by code

        Returns
        -------
        tuple
            (group_key, group_info)
        """
        group_key = None
        group_info = None

        # INVOICE LEVEL ENTITIES
        if group_by == "200":  # Customers (Invoice level)
            if detail.hoa_don_ban_hang.ma_kh:
                group_key = str(detail.hoa_don_ban_hang.ma_kh.uuid)
                group_info = {
                    'ma': detail.hoa_don_ban_hang.ma_kh.customer_code,
                    'ten': detail.hoa_don_ban_hang.ma_kh.customer_name,
                    'uuid': group_key,
                }
        elif group_by == "700":  # Units (Invoice level)
            if detail.hoa_don_ban_hang.unit_id:
                group_key = str(detail.hoa_don_ban_hang.unit_id.uuid)
                group_info = {
                    'ma': detail.hoa_don_ban_hang.unit_id.ma_unit,
                    'ten': detail.hoa_don_ban_hang.unit_id.ten_unit,
                    'uuid': group_key,
                }
        elif group_by == "910":  # Sales Staff (Invoice level)
            if detail.hoa_don_ban_hang.ma_nvbh:
                group_key = str(detail.hoa_don_ban_hang.ma_nvbh.uuid)
                group_info = {
                    'ma': detail.hoa_don_ban_hang.ma_nvbh.ma_nv,
                    'ten': detail.hoa_don_ban_hang.ma_nvbh.ten_nv,
                    'uuid': group_key,
                }

        # DETAIL LEVEL ENTITIES
        elif group_by == "300":  # Materials (Detail level)
            if detail.ma_vt:
                group_key = str(detail.ma_vt.uuid)
                group_info = {
                    'ma': detail.ma_vt.ma_vt,
                    'ten': detail.ma_vt.ten_vt,
                    'uuid': group_key,
                }
        elif group_by == "810":  # Departments (Detail level)
            if detail.ma_bp:
                group_key = str(detail.ma_bp.uuid)
                group_info = {
                    'ma': detail.ma_bp.ma_bp,
                    'ten': detail.ma_bp.ten_bp,
                    'uuid': group_key,
                }
        elif group_by == "820":  # Tasks (Detail level)
            if detail.ma_vv:
                group_key = str(detail.ma_vv.uuid)
                group_info = {
                    'ma': detail.ma_vv.ma_vu_viec,
                    'ten': detail.ma_vv.ten_vu_viec,
                    'uuid': group_key,
                }
        elif group_by == "830":  # Contracts (Detail level)
            if detail.ma_hd:
                group_key = str(detail.ma_hd.uuid)
                group_info = {
                    'ma': detail.ma_hd.ma_hd,
                    'ten': detail.ma_hd.ten_hd,
                    'uuid': group_key,
                }
        elif group_by == "840":  # Agreements (Detail level)
            if detail.ma_ku:
                group_key = str(detail.ma_ku.uuid)
                group_info = {
                    'ma': detail.ma_ku.ma_ku,
                    'ten': detail.ma_ku.ten_ku,
                    'uuid': group_key,
                }
        elif group_by == "850":  # Fees (Detail level)
            if detail.ma_phi:
                group_key = str(detail.ma_phi.uuid)
                group_info = {
                    'ma': detail.ma_phi.ma_phi,
                    'ten': detail.ma_phi.ten_phi,
                    'uuid': group_key,
                }
        elif group_by == "860":  # Products (Detail level)
            if detail.ma_sp:
                group_key = str(detail.ma_sp.uuid)
                group_info = {
                    'ma': detail.ma_sp.ma_sp,
                    'ten': detail.ma_sp.ten_sp,
                    'uuid': group_key,
                }

        return group_key, group_info

    @staticmethod
    def _extract_detail_by_info(detail, detail_by: str) -> Tuple[str, Dict[str, Any]]:
        """
        Extract detail_by key and info from detail record.

        Query levels:
        - Invoice level: Customers (200), Units (700), Sales Staff (910)
        - Detail level: Materials (300), Departments (810), Tasks (820),
                       Contracts (830), Agreements (840), Fees (850), Products (860)

        Parameters
        ----------
        detail : ChiTietHoaDonBanHangModel
            Invoice detail record
        detail_by : str
            Detail by code

        Returns
        -------
        tuple
            (detail_key, detail_info)
        """
        detail_key = None
        detail_info = None

        # INVOICE LEVEL ENTITIES
        if detail_by == "200":  # Customers (Invoice level)
            if detail.hoa_don_ban_hang.ma_kh:
                detail_key = str(detail.hoa_don_ban_hang.ma_kh.uuid)
                detail_info = {
                    'ma': detail.hoa_don_ban_hang.ma_kh.customer_code,
                    'ten': detail.hoa_don_ban_hang.ma_kh.customer_name,
                    'uuid': detail_key,
                }
        elif detail_by == "700":  # Units (Invoice level)
            if detail.hoa_don_ban_hang.unit_id:
                detail_key = str(detail.hoa_don_ban_hang.unit_id.uuid)
                detail_info = {
                    'ma': detail.hoa_don_ban_hang.unit_id.ma_unit,
                    'ten': detail.hoa_don_ban_hang.unit_id.ten_unit,
                    'uuid': detail_key,
                }
        elif detail_by == "910":  # Sales Staff (Invoice level)
            if detail.hoa_don_ban_hang.ma_nvbh:
                detail_key = str(detail.hoa_don_ban_hang.ma_nvbh.uuid)
                detail_info = {
                    'ma': detail.hoa_don_ban_hang.ma_nvbh.ma_nv,
                    'ten': detail.hoa_don_ban_hang.ma_nvbh.ten_nv,
                    'uuid': detail_key,
                }

        # DETAIL LEVEL ENTITIES
        elif detail_by == "300":  # Materials (Detail level)
            if detail.ma_vt:
                detail_key = str(detail.ma_vt.uuid)
                detail_info = {
                    'ma': detail.ma_vt.ma_vt,
                    'ten': detail.ma_vt.ten_vt,
                    'uuid': detail_key,
                }
        elif detail_by == "810":  # Departments (Detail level)
            if detail.ma_bp:
                detail_key = str(detail.ma_bp.uuid)
                detail_info = {
                    'ma': detail.ma_bp.ma_bp,
                    'ten': detail.ma_bp.ten_bp,
                    'uuid': detail_key,
                }
        elif detail_by == "820":  # Tasks (Detail level)
            if detail.ma_vv:
                detail_key = str(detail.ma_vv.uuid)
                detail_info = {
                    'ma': detail.ma_vv.ma_vu_viec,
                    'ten': detail.ma_vv.ten_vu_viec,
                    'uuid': detail_key,
                }
        elif detail_by == "830":  # Contracts (Detail level)
            if detail.ma_hd:
                detail_key = str(detail.ma_hd.uuid)
                detail_info = {
                    'ma': detail.ma_hd.ma_hd,
                    'ten': detail.ma_hd.ten_hd,
                    'uuid': detail_key,
                }
        elif detail_by == "840":  # Agreements (Detail level)
            if detail.ma_ku:
                detail_key = str(detail.ma_ku.uuid)
                detail_info = {
                    'ma': detail.ma_ku.ma_ku,
                    'ten': detail.ma_ku.ten_ku,
                    'uuid': detail_key,
                }
        elif detail_by == "850":  # Fees (Detail level)
            if detail.ma_phi:
                detail_key = str(detail.ma_phi.uuid)
                detail_info = {
                    'ma': detail.ma_phi.ma_phi,
                    'ten': detail.ma_phi.ten_phi,
                    'uuid': detail_key,
                }
        elif detail_by == "860":  # Products (Detail level)
            if detail.ma_sp:
                detail_key = str(detail.ma_sp.uuid)
                detail_info = {
                    'ma': detail.ma_sp.ma_sp,
                    'ten': detail.ma_sp.ten_sp,
                    'uuid': detail_key,
                }

        return detail_key, detail_info

    @staticmethod
    def combine_group_by_with_detail_by_internal(
        details_p1, details_p2, detail_by: str, group_by: str
    ) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        STEP 2: Có data đó → Kết hợp với detail_by để kiểm tra hóa đơn và chi tiết có match không

        Combine group_by matched data with detail_by to create grouped structure.

        Parameters
        ----------
        details_p1 : QuerySet
            Period 1 details from group_by query
        details_p2 : QuerySet
            Period 2 details from group_by query
        detail_by : str
            Detail by code
        group_by : str
            Group by code

        Returns
        -------
        Dict[str, Dict[str, Dict[str, Any]]]
            Combined data structure: {group_key: {detail_key: {period_data, info}}}
        """
        from collections import defaultdict

        # Structure: {group_key: {detail_key: {period_1_data, period_2_data, info}}}
        combined_data = defaultdict(
            lambda: defaultdict(
                lambda: {
                    'period_1': {'sl': Decimal('0'), 'tien': Decimal('0')},
                    'period_2': {'sl': Decimal('0'), 'tien': Decimal('0')},
                    'group_info': None,
                    'detail_info': None,
                }
            )
        )

        # Process period 1 data
        for detail in details_p1:
            group_key, detail_key, group_info, detail_info = (
                SalesComparisonMapping.extract_keys_and_info_from_detail_internal(
                    detail, detail_by, group_by
                )
            )

            if group_key and detail_key:
                combined_data[group_key][detail_key]['period_1']['sl'] += Decimal(
                    str(detail.so_luong or 0)
                )
                combined_data[group_key][detail_key]['period_1']['tien'] += Decimal(
                    str(detail.tien_nt or 0)
                )
                combined_data[group_key][detail_key]['group_info'] = group_info
                combined_data[group_key][detail_key]['detail_info'] = detail_info

        # Process period 2 data
        for detail in details_p2:
            group_key, detail_key, group_info, detail_info = (
                SalesComparisonMapping.extract_keys_and_info_from_detail_internal(
                    detail, detail_by, group_by
                )
            )

            if group_key and detail_key:
                combined_data[group_key][detail_key]['period_2']['sl'] += Decimal(
                    str(detail.so_luong or 0)
                )
                combined_data[group_key][detail_key]['period_2']['tien'] += Decimal(
                    str(detail.tien_nt or 0)
                )
                if not combined_data[group_key][detail_key]['group_info']:
                    combined_data[group_key][detail_key]['group_info'] = group_info
                if not combined_data[group_key][detail_key]['detail_info']:
                    combined_data[group_key][detail_key]['detail_info'] = detail_info

        return combined_data

    @staticmethod
    def extract_keys_and_info_from_detail_internal(
        detail, detail_by: str, group_by: str
    ) -> Tuple[str, str, Dict[str, Any], Dict[str, Any]]:
        """
        Extract group key, detail key, and info from invoice detail record.

        Parameters
        ----------
        detail : ChiTietHoaDonBanHangModel
            Invoice detail record
        detail_by : str
            Detail by code
        group_by : str
            Group by code

        Returns
        -------
        tuple
            (group_key, detail_key, group_info, detail_info)
        """
        group_key = None
        detail_key = None
        group_info = None
        detail_info = None

        # Extract group_by key and info
        group_key, group_info = SalesComparisonMapping._extract_group_by_info(
            detail, group_by
        )

        # Extract detail_by key and info
        detail_key, detail_info = SalesComparisonMapping._extract_detail_by_info(
            detail, detail_by
        )

        return group_key, detail_key, group_info, detail_info

    @staticmethod
    def format_group_mapping_response_internal(
        combined_data: Dict[str, Dict[str, Dict[str, Any]]],
        detail_by: str,
        group_by: str,
    ) -> List[Dict[str, Any]]:
        """
        STEP 3: Format group mapping response → Tạo response theo structure mong muốn

        Format the combined data into the final response structure with group headers
        and detail items.

        Parameters
        ----------
        combined_data : Dict[str, Dict[str, Dict[str, Any]]]
            Combined data from step 2
        detail_by : str
            Detail by code
        group_by : str
            Group by code

        Returns
        -------
        List[Dict[str, Any]]
            Formatted response with group headers and detail items
        """
        result = []

        # Sort groups for consistent ordering
        for group_key in sorted(combined_data.keys()):
            details_data = combined_data[group_key]

            # Calculate group totals
            total_sl_kn = sum(data['period_1']['sl'] for data in details_data.values())
            total_tien_kn = sum(
                data['period_1']['tien'] for data in details_data.values()
            )
            total_sl_kt = sum(data['period_2']['sl'] for data in details_data.values())
            total_tien_kt = sum(
                data['period_2']['tien'] for data in details_data.values()
            )

            sl_cl = total_sl_kn - total_sl_kt
            tien_cl = total_tien_kn - total_tien_kt
            sl_tl = (sl_cl / total_sl_kt * 100) if total_sl_kt != 0 else 0
            tien_tl = (tien_cl / total_tien_kt * 100) if total_tien_kt != 0 else 0

            # Get group info
            sample_detail = next(iter(details_data.values()))
            group_info = sample_detail.get('group_info', {})

            # Create group header
            group_header = {
                'ma': group_info.get('ma', 'Unknown'),
                'ten': group_info.get('ten', 'Unknown Group'),
                'sl_kn': float(total_sl_kn),
                'tien_kn': float(total_tien_kn),
                'sl_kt': float(total_sl_kt),
                'tien_kt': float(total_tien_kt),
                'sl_cl': float(sl_cl),
                'sl_tl': float(sl_tl),
                'tien_cl': float(tien_cl),
                'tien_nt_cl': float(tien_cl),
                'tien_tl': float(tien_tl),
                'is_group_header': True,
            }
            result.append(group_header)

            # Add detail records for this group
            for detail_key in sorted(details_data.keys()):
                detail_data = details_data[detail_key]
                detail_info = detail_data.get('detail_info', {})

                sl_kn = detail_data['period_1']['sl']
                tien_kn = detail_data['period_1']['tien']
                sl_kt = detail_data['period_2']['sl']
                tien_kt = detail_data['period_2']['tien']

                detail_sl_cl = sl_kn - sl_kt
                detail_tien_cl = tien_kn - tien_kt
                detail_sl_tl = (detail_sl_cl / sl_kt * 100) if sl_kt != 0 else 0
                detail_tien_tl = (detail_tien_cl / tien_kt * 100) if tien_kt != 0 else 0

                detail_record = {
                    'ma': detail_info.get('ma', 'Unknown'),
                    'ten': detail_info.get('ten', 'Unknown Detail'),
                    'sl_kn': float(sl_kn),
                    'tien_kn': float(tien_kn),
                    'sl_kt': float(sl_kt),
                    'tien_kt': float(tien_kt),
                    'sl_cl': float(detail_sl_cl),
                    'sl_tl': float(detail_sl_tl),
                    'tien_cl': float(detail_tien_cl),
                    'tien_nt_cl': float(detail_tien_cl),
                    'tien_tl': float(detail_tien_tl),
                    'is_group_header': False,
                }
                result.append(detail_record)

        return result
