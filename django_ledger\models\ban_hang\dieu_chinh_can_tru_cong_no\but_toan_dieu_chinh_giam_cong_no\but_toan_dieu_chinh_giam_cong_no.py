"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

But Toan <PERSON> (Debt Reduction Adjustment) model implementation.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ButToanDieuChinhGiamCongNoModelQueryset(QuerySet):
    """
    A custom defined ButToanDieuChinhGiamCongNoModel QuerySet.
    """

    def active(self):  # noqa: C901
        """
        Returns active ButToanDieuChinhGiamCongNoModel instances.
        """
        return self.filter(status='1')

    def inactive(self):  # noqa: C901
        """
        Returns inactive ButToanDieuChinhGiamCongNoModel instances.
        """
        return self.exclude(status='1')

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Fetches a QuerySet of ButToanDieuChinhGiamCongNoModels for a specific entity.

        Parameters
        __________
        entity_slug: str
            The entity slug to filter by.
        """
        return self.filter(entity_model__slug__exact=entity_slug)


class ButToanDieuChinhGiamCongNoModelManager(Manager):
    """
    A custom defined ButToanDieuChinhGiamCongNoModel Manager that will act as an interface to handle the  # noqa: E501
    ButToanDieuChinhGiamCongNoModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ButToanDieuChinhGiamCongNoModelQueryset.
        """
        return ButToanDieuChinhGiamCongNoModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug, user_model=None):  # noqa: F811,
        """
        Returns ButToanDieuChinhGiamCongNoModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.
        user_model
            The authenticated Django UserModel making the request.

        Returns
        -------
        ButToanDieuChinhGiamCongNoModelQueryset
            A QuerySet of ButToanDieuChinhGiamCongNoModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)

    def active(self):  # noqa: C901
        """
        Active debt reduction adjustment entries that can be used in transactions.

        Returns
        -------
        ButToanDieuChinhGiamCongNoModelQueryset
            A QuerySet of active debt reduction adjustment entries.
        """
        return self.get_queryset().active()


class ButToanDieuChinhGiamCongNoModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the ButToanDieuChinhGiamCongNoModel database will inherit from.  # noqa: E501
    The ButToanDieuChinhGiamCongNoModel inherits functionality from the ChungTuMixIn and CreateUpdateMixIn.  # noqa: E501

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    entity_model : EntityModel
        The EntityModel associated with this ButToanDieuChinhGiamCongNo.
    ma_ngv : str
        Mã ngân hàng vốn.
    dien_giai : str
        Diễn giải.
    tk : AccountModel
        Tài khoản.
    unit_id : EntityUnitModel
        Đơn vị.
    ma_nt : NgoaiTe
        Mã ngoại tệ.
    ty_gia : decimal
        Tỷ giá.
    status : str
        Trạng thái.
    transfer_yn : bool
        Đã chuyển.
    hd_yn : bool
        Hợp đồng.
    so_ct0 : str
        Số chứng từ gốc.
    ngay_ct0 : date
        Ngày chứng từ gốc.
    ma_tt : HanThanhToanModel
        Mã thanh toán.
    tg_dd : bool
        Tỷ giá đích danh.
    cltg_yn : bool
        Chênh lệch tỷ giá.
    ma_kh : CustomerModel
        Mã khách hàng.
    so_ct_goc : str
        Số chứng từ gốc.
    dien_giai_ct_goc : str
        Diễn giải chứng từ gốc.
    t_tien_nt : decimal
        Tổng tiền ngoại tệ.
    t_tien : decimal
        Tổng tiền.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_("UUID"),
        help_text=_("Unique identifier for the record"),
    )

    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_("Entity Model"),
        help_text=_("The entity this record belongs to"),
    )

    ma_ngv = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã ngân hàng vốn"),
        help_text=_("Capital bank code"),
    )

    dien_giai = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Diễn giải"),
        help_text=_("Description"),
    )

    tk = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_("Tài khoản"),
        help_text=_("Account"),
    )

    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='but_toan_dieu_chinh_giam_cong_no_unit_set',
        verbose_name=_("ID đơn vị"),
        help_text=_("Unit ID"),
    )

    # i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct, chung_tu are provided by ChungTuMixIn

    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã ngoại tệ"),
        help_text=_("Currency code"),
    )

    ty_gia = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Tỷ giá"),
        help_text=_("Exchange rate"),
    )

    status = models.CharField(
        max_length=10, verbose_name=_("Trạng thái"), help_text=_("Status")
    )

    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_("Đã chuyển"),
        help_text=_("Transferred"),
    )

    hd_yn = models.BooleanField(
        default=False, verbose_name=_("Hợp đồng"), help_text=_("Contract")
    )

    so_ct0 = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Số chứng từ gốc"),
        help_text=_("Original document number"),
    )

    ngay_ct0 = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Ngày chứng từ gốc"),
        help_text=_("Original document date"),
    )

    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã thanh toán"),
        help_text=_("Payment code"),
    )

    tg_dd = models.BooleanField(
        default=False,
        verbose_name=_("Tỷ giá đích danh"),
        help_text=_("Specific exchange rate"),
    )

    cltg_yn = models.BooleanField(
        default=False,
        verbose_name=_("Chênh lệch tỷ giá"),
        help_text=_("Exchange rate difference"),
    )

    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã khách hàng"),
        help_text=_("Customer code"),
    )

    so_ct_goc = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Số chứng từ gốc"),
        help_text=_("Root document number"),
    )

    dien_giai_ct_goc = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Diễn giải chứng từ gốc"),
        help_text=_("Description of the root document"),
    )

    t_tien_nt = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        verbose_name=_("Tổng tiền ngoại tệ"),
        help_text=_("Total amount in foreign currency"),
    )

    t_tien = models.DecimalField(
        max_digits=20,  
        decimal_places=2,
        verbose_name=_("Tổng tiền"),
        help_text=_("Total amount"),
    )

    # Document information provided by ChungTuMixIn
    # chung tu fields: i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct, chung_tu
    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho bút toán này"),
        related_name="but_toan_dieu_chinh_giam_cong_no",
    )

    # Define custom model manager
    objects = ButToanDieuChinhGiamCongNoModelManager()

    class Meta:
        abstract = True
        verbose_name = _("Bút toán điều chỉnh giảm công nợ")
        verbose_name_plural = _("Bút toán điều chỉnh giảm công nợ")
        ordering = ['ma_ngv']  # Removed -ngay_ct since it's now in ChungTuItem
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_ngv']),
            models.Index(fields=['status']),
            models.Index(fields=['unit_id']),
        ]
        # Removed unique_together with so_ct since it's now in ChungTuItem

    def __str__(self):  # noqa: C901
        return (
            f"Bút toán điều chỉnh giảm công nợ: {self.so_ct} - {self.dien_giai or ''}"
        )


class ButToanDieuChinhGiamCongNoModel(ButToanDieuChinhGiamCongNoModelAbstract):
    """
    Base ButToanDieuChinhGiamCongNo Model Implementation
    """

    class Meta(ButToanDieuChinhGiamCongNoModelAbstract.Meta):
        abstract = False
        db_table = 'but_toan_dieu_chinh_giam_cong_no'
