"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietHoaDonNhapMuaXuatThang Repository implementation.
"""

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiTietHoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401


class ChiTietHoaDonNhapMuaXuatThangRepository(BaseRepository):
    """
    Repository class for ChiTietHoaDonNhapMuaXuatThangModel.
    Handles data access operations for invoice details.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the ChiTietHoaDonNhapMuaXuatThangModel.
        """
        super().__init__(model_class=ChiTietHoaDonNhapMuaXuatThangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for ChiTietHoaDonNhapMuaXuatThangModel with optimized relationships.

        Returns
        -------
        QuerySet
            The base queryset for ChiTietHoaDonNhapMuaXuatThangModel with related objects.
        """
        return self.model_class.objects.all().select_related(
            "hoa_don",
            "ma_vt",
            "dvt",
            "ma_kho",
            "ma_lo",
            "ma_vi_tri",
            "ma_thue",
            "tk_thue",
            "tk_vt",
            "ma_nx",
            "tk_cpxt",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
        )

    def get_by_invoice(self, hoa_don_uuid) -> QuerySet:  # noqa: C901
        """
        Get ChiTietHoaDonNhapMuaXuatThangModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonNhapMuaXuatThangModel instances for the specified invoice.
        """
        return self.get_queryset().filter(hoa_don__uuid=hoa_don_uuid)

    def get_by_id(
        self, uuid, hoa_don_uuid=None
    ) -> ChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Get a ChiTietHoaDonNhapMuaXuatThangModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiTietHoaDonNhapMuaXuatThangModel to retrieve.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiTietHoaDonNhapMuaXuatThangModel
            The retrieved ChiTietHoaDonNhapMuaXuatThangModel instance.

        Raises
        ------
        ObjectDoesNotExist
            If the ChiTietHoaDonNhapMuaXuatThangModel with the given UUID does not exist.
        """
        qs = self.get_queryset()
        if hoa_don_uuid:
            qs = self.get_by_invoice(hoa_don_uuid)
        return qs.get(uuid=uuid)

    def create(self, **kwargs) -> ChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        **kwargs : dict
            The data to create the ChiTietHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ChiTietHoaDonNhapMuaXuatThangModel
            The created ChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        # Convert UUID strings to model instances
        processed_kwargs = self.convert_uuids_to_model_instances(kwargs)
        return self.model_class.objects.create(**processed_kwargs)

    def convert_uuids_to_model_instances(self, data: dict) -> dict:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.

        Parameters
        ----------
        data : dict
            The data containing UUID strings.

        Returns
        -------
        dict
            The data with UUID strings converted to model instances.
        """
        from django_ledger.models import (  # noqa: F401,
            AccountModel,
            BoPhanModel,
            ContractModel,
            DonViTinhModel,
            DotThanhToanModel,
            KheUocModel,
            KhoHangModel,
            LoModel,
            NhapXuatModel,
            PhiModel,
            TaxModel,
            VatTuModel,
            ViTriModel,
            VuViecModel,
        )

        data_copy = data.copy()

        def convert_uuid_field(field_name, model_class):
            """Helper function to convert UUID field to model instance."""
            if field_name in data_copy:
                if data_copy[field_name] and str(data_copy[field_name]).strip():
                    try:
                        data_copy[field_name] = model_class.objects.get(
                            uuid=data_copy[field_name]
                        )
                    except model_class.DoesNotExist:
                        data_copy[field_name] = None
                else:
                    data_copy[field_name] = None

        # Convert all UUID fields using helper function
        convert_uuid_field('ma_vt', VatTuModel)
        convert_uuid_field('dvt', DonViTinhModel)
        convert_uuid_field('ma_kho', KhoHangModel)
        convert_uuid_field('ma_lo', LoModel)
        convert_uuid_field('ma_vi_tri', ViTriModel)
        convert_uuid_field('ma_thue', TaxModel)
        convert_uuid_field('tk_thue', AccountModel)
        convert_uuid_field('tk_vt', AccountModel)
        convert_uuid_field('ma_nx', NhapXuatModel)
        convert_uuid_field('tk_cpxt', AccountModel)
        convert_uuid_field('ma_bp', BoPhanModel)
        convert_uuid_field('ma_vv', VuViecModel)
        convert_uuid_field('ma_hd', ContractModel)
        convert_uuid_field('ma_dtt', DotThanhToanModel)
        convert_uuid_field('ma_ku', KheUocModel)
        convert_uuid_field('ma_phi', PhiModel)
        convert_uuid_field('ma_sp', VatTuModel)

        return data_copy

    def update(
        self, instance: ChiTietHoaDonNhapMuaXuatThangModel, **kwargs
    ) -> ChiTietHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        instance : ChiTietHoaDonNhapMuaXuatThangModel
            The instance to update.
        **kwargs : dict
            The data to update the instance with.

        Returns
        -------
        ChiTietHoaDonNhapMuaXuatThangModel
            The updated ChiTietHoaDonNhapMuaXuatThangModel instance.
        """
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(
        self, instance: ChiTietHoaDonNhapMuaXuatThangModel
    ) -> None:  # noqa: C901
        """
        Delete a ChiTietHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        instance : ChiTietHoaDonNhapMuaXuatThangModel
            The instance to delete.
        """
        instance.delete()

    def filter_by_material(
        self, material_uuid, hoa_don_uuid=None
    ) -> QuerySet:  # noqa: C901
        """
        Filter ChiTietHoaDonNhapMuaXuatThangModel instances by material.

        Parameters
        ----------
        material_uuid : UUID
            The UUID of the material to filter by.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonNhapMuaXuatThangModel instances for the specified material.
        """
        qs = self.get_queryset()
        if hoa_don_uuid:
            qs = self.get_by_invoice(hoa_don_uuid)
        return qs.filter(ma_vt__uuid=material_uuid)

    def filter_by_warehouse(
        self, warehouse_uuid, hoa_don_uuid=None
    ) -> QuerySet:  # noqa: C901
        """
        Filter ChiTietHoaDonNhapMuaXuatThangModel instances by warehouse.

        Parameters
        ----------
        warehouse_uuid : UUID
            The UUID of the warehouse to filter by.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonNhapMuaXuatThangModel instances for the specified warehouse.
        """
        qs = self.get_queryset()
        if hoa_don_uuid:
            qs = self.get_by_invoice(hoa_don_uuid)
        return qs.filter(ma_kho__uuid=warehouse_uuid)

    def get_total_by_invoice(self, hoa_don_uuid) -> dict:  # noqa: C901
        """
        Get total amounts for ChiTietHoaDonNhapMuaXuatThangModel instances by invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        dict
            A dictionary containing total amounts.
        """
        from django.db.models import Sum

        qs = self.get_by_invoice(hoa_don_uuid)
        return qs.aggregate(
            total_quantity=Sum('so_luong'),
            total_amount=Sum('tien'),
            total_amount_nt=Sum('tien_nt'),
            total_tax=Sum('thue'),
            total_tax_nt=Sum('thue_nt'),
            total_cost=Sum('cp'),
            total_cost_nt=Sum('cp_nt'),
        )
