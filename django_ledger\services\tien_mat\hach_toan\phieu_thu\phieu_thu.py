"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for PhieuThu model.
"""

from typing import Any, Dict, List, Optional, Tuple  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models import (  # noqa: F401,
    PhieuThuChiTietModel,
    PhieuThuModel,
)
from django_ledger.repositories.tien_mat.hach_toan.phieu_thu import (  # noqa: F401,
    PhieuThuChiTietRepository,
    PhieuThuRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,

# ✅ UNIFIED ACCOUNTING SERVICE: Replace legacy utils with unified service
from django_ledger.utils_new.debt_management import CongNoCreation


class PhieuThuService(BaseService):
    """
    Service class for handling PhieuThu business logic.
    Implements the Service pattern for PhieuThu.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Phiếu thu accounting mappings
    RECEIPT_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'THUHD',                # Thu từ hóa đơn (ma_ngv=1)
            'debit_account_field': 'tk',            # Tài khoản tiền - DEBIT
            'credit_account_field': 'tk_co',        # Tài khoản đối ứng - CREDIT
            'debit_account_source': 'header',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'tien',                 # Số tiền (detail)
            'detail_source': 'children',            # Related name to detail (PhieuThuChiTiet)
            'canCreate': True                       # Default: always create entry
        }
    ]

    def __init__(self):  # noqa: C901
        """
        Initialize the service.
        """
        super().__init__()
        self.repository = PhieuThuRepository()
        self.detail_repository = PhieuThuChiTietRepository()

        # ✅ ĐƠN GIẢN: Khởi tạo unified accounting service
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(self, phieu_thu: PhieuThuModel) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định mapping kế toán dựa trên ma_ngv và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional journal type dựa trên ma_ngv (1=THUHD, 2=THUKH)
        - Luôn tạo bút toán (theo yêu cầu)
        - Support flexible business rules cho future enhancements

        Parameters
        ----------
        phieu_thu : PhieuThuModel
            Phiếu thu để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với journal_type và canCreate được set
        """
        # Get base configuration
        mappings = self.get_accounting_configuration()

        # ✅ BUSINESS LOGIC: Determine journal type based on ma_ngv
        if hasattr(phieu_thu, 'ma_ngv'):
            ma_ngv = getattr(phieu_thu, 'ma_ngv', '1')  # Default to THUHD

            for mapping in mappings:
                if ma_ngv == '1':
                    mapping['journal_type'] = 'THUHD'  # Thu từ hóa đơn
                elif ma_ngv == '2':
                    mapping['journal_type'] = 'THUKH'  # Thu từ khách hàng
                else:
                    mapping['journal_type'] = 'THUHD'  # Default fallback

        # ✅ BUSINESS LOGIC: Luôn tạo bút toán (theo yêu cầu)
        for mapping in mappings:
            mapping['canCreate'] = True

        # ✅ QUY TẮC NGHIỆP VỤ BỔ SUNG: Kiểm tra điều kiện cơ bản
        # Chỉ kiểm tra validation nếu phieu_thu đã có ID (đã được save)
        if hasattr(phieu_thu, 'pk') and phieu_thu.pk and hasattr(phieu_thu, 'children'):
            try:
                chi_tiet_list = phieu_thu.children.all()
                # Chỉ validate nếu có children records
                if chi_tiet_list.exists():
                    has_valid_entries = any(
                        getattr(ct, 'tien', 0) > 0 and
                        getattr(ct, 'tk_co', None) is not None
                        for ct in chi_tiet_list
                    )

                    if not has_valid_entries:
                        for mapping in mappings:
                            mapping['canCreate'] = False
                # Nếu không có children records, vẫn cho phép tạo bút toán
                # (có thể là header-only hoặc children sẽ được tạo sau)
            except Exception as e:
                # Log error nhưng không fail - cho phép tạo bút toán
                pass

        return mappings

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for PhieuThuModel.

        Returns
        -------
        QuerySet
            The base queryset
        """
        return self.repository.get_queryset()

    def list(  # noqa: C901
        self,
        entity_slug: str,
        search_query: str = None,
        status: str = None,
        from_date: str = None,
        to_date: str = None,
    ) -> QuerySet:
        """
        Get a list of PhieuThu instances

        Parameters
        ----------
        entity_slug : str
            The entity slug
        search_query : str, optional
            Search query to filter results, by default None
        status : str, optional
            Status filter ('1' for active, '0' for inactive), by default None
        from_date : str, optional
            Start date for filtering, by default None
        to_date : str, optional
            End date for filtering, by default None

        Returns
        -------
        QuerySet
            QuerySet of PhieuThu instances
        """
        # Get data from repository with filters
        return self.repository.list(
            entity_slug=entity_slug,
            search_query=search_query,
            status=status,
            from_date=from_date,
            to_date=to_date,
        )

    def get(self, entity_slug: str, uuid: UUID) -> PhieuThuModel:  # noqa: C901
        """
        Get a specific PhieuThu instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : UUID
            The UUID of the PhieuThu to retrieve

        Returns
        -------
        PhieuThuModel
            The PhieuThu instance

        Raises
        ------
        PhieuThuModel.DoesNotExist
            If the instance does not exist
        """
        return self.repository.get_by_uuid(entity_slug=entity_slug, uuid=uuid)

    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuThuModel:  # noqa: C901
        """
        Create a new PhieuThu instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new instance

        Returns
        -------
        PhieuThuModel
            The created PhieuThu instance
        """
        # Extract child items if present (support both naming conventions)
        child_items = data.pop('child_items', None)
        children = data.pop('children', None)
        detail_items = children or child_items  # Use children if provided, fallback to child_items

        # Create the instance using repository
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # Create child items if provided
        if detail_items:
            for item in detail_items:
                # ✅ Ensure amount has max 2 decimal places for validation
                if 'tien' in item and item['tien'] is not None:
                    item['tien'] = round(float(item['tien']), 2)

                self.detail_repository.create(parent_field=instance, data=item)

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not instance.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(source_document=instance,
                                                                       document_type="phiếu thu",
                                                                       account_mappings=self._determine_accounting_mappings(instance))
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuThu and accounting entries
                raise Exception(f"Failed to create accounting entry for PhieuThu {instance.so_ct}: {str(e)}") from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance

    @transaction.atomic
    def update(
        self, entity_slug: str, uuid: UUID, data: Dict[str, Any]
    ) -> PhieuThuModel:  # noqa: C901
        """
        Update an existing PhieuThu instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : UUID
            The UUID of the PhieuThu to update
        data : Dict[str, Any]
            The data to update

        Returns
        -------
        PhieuThuModel
            The updated PhieuThu instance

        Raises
        ------
        PhieuThuModel.DoesNotExist
            If the instance does not exist
        """
        # Extract child items if present (support both naming conventions)
        child_items = data.pop('child_items', None)
        children = data.pop('children', None)
        detail_items = children or child_items  # Use children if provided, fallback to child_items

        # Update the instance using repository
        instance = self.repository.update(
            entity_slug=entity_slug, uuid=uuid, data=data
        )
        # Update child items if provided
        if detail_items:
            for item in detail_items:
                if 'uuid' in item:
                    # Update existing item
                    self.detail_repository.update(uuid=item['uuid'], data=item)
                else:
                    # Create new item
                    self.detail_repository.create(
                        parent_field=instance, data=item
                    )

        # ✅ UNIFIED ACCOUNTING: Cập nhật bút toán kế toán
        # Only update accounting if ledger exists
        if instance.ledger:
            try:
                self._cong_no_service.update_document_accounting_entries(source_document=instance,
                                                                        document_type="phiếu thu",
                                                                        account_mappings=self._determine_accounting_mappings(instance))
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuThu and accounting entries
                raise Exception(f"Failed to update accounting entry for PhieuThu {instance.so_ct}: {str(e)}") from e
        # Note: If no ledger exists, skip accounting update

        return instance

    @transaction.atomic
    def delete(self, entity_slug: str, uuid: UUID) -> bool:  # noqa: C901
        """
        Delete a PhieuThu instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : UUID
            The UUID of the PhieuThu to delete

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise

        Raises
        ------
        PhieuThuModel.DoesNotExist
            If the instance does not exist
        """
        # Delete the instance using repository
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    def get_details(self, phieu_thu_uuid: UUID) -> QuerySet:  # noqa: C901
        """
        Get details for a specific PhieuThu

        Parameters
        ----------
        phieu_thu_uuid : UUID
            The UUID of the PhieuThu

        Returns
        -------
        QuerySet
            QuerySet of PhieuThuChiTiet instances
        """
        return self.detail_repository.list_by_parent(parent_uuid=phieu_thu_uuid)

    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Lấy cấu hình kế toán cho phiếu thu.

        Returns:
            List[Dict[str, Any]]: Danh sách mapping configuration
        """
        return self.RECEIPT_ACCOUNTING_CONFIG.copy()
