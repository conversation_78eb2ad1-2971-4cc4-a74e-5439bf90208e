"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for Tinh Ton Kho Tuc Thoi (Real-time Inventory Calculation).
"""

from datetime import date
from typing import Union
from uuid import UUID

from django.utils import timezone

from django_ledger.services.base import BaseService


class TinhTonKhoTucThoiService(BaseService):

    def __init__(self):
        super().__init__()
        self._warehouse_stock_audit_service = None

    @property
    def warehouse_stock_audit_service(self):
        if self._warehouse_stock_audit_service is None:
            from django_ledger.services.warehouse_stock_audit.warehouse_stock_audit import (
                WarehouseStockAuditService,
            )
            self._warehouse_stock_audit_service = WarehouseStockAuditService()
        return self._warehouse_stock_audit_service

    def inventory_calculation(
        self,
        entity_slug: str,
        nam: int,
        ma_vt: Union[str, UUID],
        ma_kho: Union[str, UUID]
    ):
        self.warehouse_stock_audit_service.recalculate_stock_balances(
            warehouse_id=ma_kho,
            product_id=ma_vt,
            from_date=date(nam, 1, 1),
            lot_id=None,
            location_id=None
        )
