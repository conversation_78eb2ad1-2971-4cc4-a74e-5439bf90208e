"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuYeuCauKiemKe (Inventory Check Request) repository implementation.
"""

from typing import Any, Dict, Optional, Union
from uuid import UUID

from django.db.models import QuerySet
from django.shortcuts import get_object_or_404  # noqa: E402

from django_ledger.models.entity import EntityModel  # noqa: E402
from django_ledger.models.ton_kho.kiem_ke.phieu_yeu_cau_kiem_ke import (  # noqa: E402
    PhieuYeuCauKiemKeModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: E402


class PhieuYeuCauKiemKeRepository(BaseRepository):
    """
    Repository class for PhieuYeuCauKiemKeModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=PhieuYeuCauKiemKeModel)

    def get_queryset(self) -> QuerySet:
        """
        Returns the base queryset for PhieuYeuCauKiemKeModel with optimized related field loading.

        Returns
        -------
        QuerySet
            The base queryset for PhieuYeuCauKiemKeModel.
        """
        return (
            self.model_class.objects.all()
            .select_related(
                'entity_model',
                'unit_id',
                'ma_nk',
                'so_ct',
                'ma_kho',
                'ma_vi_tri',
                'ma_lo',
            )
        )

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[PhieuYeuCauKiemKeModel]:  # noqa: C901
        """
        Retrieves a PhieuYeuCauKiemKeModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuYeuCauKiemKeModel to retrieve.

        Returns
        -------
        Optional[PhieuYeuCauKiemKeModel]
            The PhieuYeuCauKiemKeModel with the given UUID, or None if not found.
        """
        try:
            return self.get_queryset().for_entity(
                entity_slug=entity_slug
            ).get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists PhieuYeuCauKiemKeModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuYeuCauKiemKeModel instances.
        """
        return self.get_queryset().for_entity(
            entity_slug=entity_slug
        ).filter(**kwargs)

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuYeuCauKiemKeModel:  # noqa: C901
        """
        Creates a new PhieuYeuCauKiemKeModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuYeuCauKiemKeModel.

        Returns
        -------
        PhieuYeuCauKiemKeModel
            The created PhieuYeuCauKiemKeModel instance.
        """
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Get entity model
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)
        # Add entity model to data
        data['entity_model'] = entity_model
        # Create the PhieuYeuCauKiemKeModel instance
        instance = self.model_class(**data)
        instance.save()

        return instance

    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[PhieuYeuCauKiemKeModel]:  # noqa: C901
        """
        Updates an existing PhieuYeuCauKiemKeModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuYeuCauKiemKeModel to update.
        data : Dict[str, Any]
            The data to update the PhieuYeuCauKiemKeModel with.

        Returns
        -------
        Optional[PhieuYeuCauKiemKeModel]
            The updated PhieuYeuCauKiemKeModel instance, or None if not found.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)
            # Update instance fields
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> bool:  # noqa: C901
        """
        Deletes a PhieuYeuCauKiemKeModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuYeuCauKiemKeModel to delete.

        Returns
        -------
        bool
            True if the PhieuYeuCauKiemKeModel was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False
