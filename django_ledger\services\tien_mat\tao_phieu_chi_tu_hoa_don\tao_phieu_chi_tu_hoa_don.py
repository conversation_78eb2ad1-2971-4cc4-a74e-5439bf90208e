"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for creating PhieuChi from purchase invoices directly.
"""

import logging
from datetime import date
from decimal import Decimal
from typing import Any, Dict, List, Union
from uuid import UUID

from django.db import transaction
from django.utils import timezone

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import HoaDonMuaHangTrongNuocModel
# Note: HD5 and HD6 models will be added when available
# from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_nhap_khau import HoaDonMuaHangNhapKhauModel
# from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuModel
from django_ledger.services.base import BaseService
from django_ledger.services.tien_mat.hach_toan.phieu_chi.phieu_chi import PhieuChiService
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.hoa_don_mua_hang_trong_nuoc import HoaDonMuaHangTrongNuocService
from datetime import datetime
from django_ledger.models.tien_mat.hach_toan.phieu_chi import PhieuChiModel


# Configure logger for production monitoring
logger = logging.getLogger(__name__)

class TaoPhieuChiTuHoaDonService(BaseService):
    """
    Service class for creating PhieuChi from purchase invoices directly.
    This is just an intermediate processing service - no models, no repository.
    Logic: 1 Invoice = 1 PhieuChi (1:1 relationship).
    """

    def __init__(self):
        """
        Initialize the service with required services.
        """
        super().__init__()
        # Initialize services once in constructor (ERP best practice)
        self.phieu_chi_service = PhieuChiService()
        self.hoa_don_mua_hang_trong_nuoc_service = HoaDonMuaHangTrongNuocService()
        # Note: Other purchase invoice services need entity_slug + user_model, create when needed

    def get_data_by_xu_ly(self, entity_slug: str, filters: Dict[str, Any], user_model=None) -> List[Dict[str, Any]]:
        """
        Get data based on xu_ly mode - ERP Expert Logic.
        xu_ly=1: Get unpaid purchase invoices for creating payments
        xu_ly=2: Get created payments for deletion/rollback

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        filters : Dict[str, Any]
            Filter parameters for the query.
        user_model : User, optional
            The user model for permission checks.

        Returns
        -------
        List[Dict[str, Any]]
            List of invoices or payments based on xu_ly mode.
        """
        # Validate required filters
        if not filters.get('ngay_ct1') or not filters.get('ngay_ct2'):
            raise ValueError("ngay_ct1 and ngay_ct2 are required")

        # Convert date strings to date objects if needed
        ngay_ct1 = filters['ngay_ct1']
        ngay_ct2 = filters['ngay_ct2']
        if isinstance(ngay_ct1, str):
            ngay_ct1 = datetime.strptime(ngay_ct1, '%Y-%m-%d').date()
        if isinstance(ngay_ct2, str):
            ngay_ct2 = datetime.strptime(ngay_ct2, '%Y-%m-%d').date()

        # Validate date range
        if ngay_ct1 > ngay_ct2:
            raise ValueError("ngay_ct1 must be less than or equal to ngay_ct2")

        xu_ly = filters.get('xu_ly', '1')

        if xu_ly == '1':
            # ERP Logic: Show unpaid purchase invoices for creating payments
            return self._get_unpaid_invoices(entity_slug, filters, user_model)
        elif xu_ly == '2':
            # ERP Logic: Show created payments for deletion/rollback
            return self._get_created_payments(entity_slug, filters, user_model)
        else:
            raise ValueError(f"Invalid xu_ly value: {xu_ly}. Must be '1' or '2'")

    def _get_unpaid_invoices(self, entity_slug: str, filters: Dict[str, Any], user_model=None) -> List[Dict[str, Any]]:
        """
        Get unpaid purchase invoices for creating payments (xu_ly=1).
        """
        unpaid_invoices = []
        ma_ct = filters.get('ma_ct', '')

        try:
            # ERP Expert: Filter by document type (ma_ct)
            if ma_ct == 'HD4' or not ma_ct:  # Default to HD4 if not specified
                # Process HoaDonMuaHangTrongNuoc invoices (HD4)
                hd_mua_hang_trong_nuoc_invoices = self._get_filtered_invoices(
                    HoaDonMuaHangTrongNuocModel, entity_slug, filters, 'HoaDonMuaHangTrongNuocModel', user_model
                )
                unpaid_invoices.extend(hd_mua_hang_trong_nuoc_invoices)

            # TODO: Add when models are available
            # elif ma_ct == 'HD5':
            #     # Process HoaDonMuaHangNhapKhau invoices (HD5)
            #     hd_mua_hang_nhap_khau_invoices = self._get_filtered_invoices(
            #         HoaDonMuaHangNhapKhauModel, entity_slug, filters, 'HoaDonMuaHangNhapKhauModel', user_model
            #     )
            #     unpaid_invoices.extend(hd_mua_hang_nhap_khau_invoices)

            # elif ma_ct == 'HD6':
            #     # Process HoaDonMuaDichVu invoices (HD6)
            #     hd_mua_dich_vu_invoices = self._get_filtered_invoices(
            #         HoaDonMuaDichVuModel, entity_slug, filters, 'HoaDonMuaDichVuModel', user_model
            #     )
            #     unpaid_invoices.extend(hd_mua_dich_vu_invoices)

            elif ma_ct in ['HD5', 'HD6']:
                logger.warning(f"Document type {ma_ct} not yet implemented",
                             extra={'entity_slug': entity_slug, 'ma_ct': ma_ct})
                # Return empty list for unsupported document types
                return []

        except Exception as e:
            logger.error(f"Error querying unpaid purchase invoices: {str(e)}",
                        extra={'entity_slug': entity_slug, 'filters': filters})
            # Return empty list instead of failing
            return []

        return unpaid_invoices

    def _get_created_payments(self, entity_slug: str, filters: Dict[str, Any], user_model=None) -> List[Dict[str, Any]]:
        """
        Get created payments for deletion/rollback (xu_ly=2).
        """
        created_payments = []

        try:
            # Build base queryset for PhieuChi created from invoices

            queryset = PhieuChiModel.objects.filter(
                entity_model__slug=entity_slug,
                ngay_ct__gte=filters['ngay_ct1'],
                ngay_ct__lte=filters['ngay_ct2'],
                id__isnull=False  # Only payments created from invoices (have invoice ID in 'id' field)
            )

            # Apply additional filters
            if filters.get('so_ct1') and filters.get('so_ct2'):
                queryset = queryset.filter(
                    so_ct__gte=filters['so_ct1'],
                    so_ct__lte=filters['so_ct2']
                )
            elif filters.get('so_ct1'):
                queryset = queryset.filter(so_ct__gte=filters['so_ct1'])
            elif filters.get('so_ct2'):
                queryset = queryset.filter(so_ct__lte=filters['so_ct2'])

            if filters.get('ma_kh'):
                queryset = queryset.filter(ma_kh=filters['ma_kh'])

            if filters.get('unit_id'):
                queryset = queryset.filter(unit_id=filters['unit_id'])

            # Apply optimized select_related
            queryset = queryset.select_related('ma_kh', 'ma_nt', 'unit_id')

            # Convert each payment to dictionary
            for payment in queryset:
                try:
                    created_payments.append(self._convert_phieu_chi_to_dict(payment))
                except Exception as e:
                    logger.error(f"Error converting PhieuChi {payment.uuid}: {str(e)}",
                               extra={'entity_slug': entity_slug, 'payment_id': str(payment.uuid)})
                    continue

        except Exception as e:
            logger.error(f"Error querying created payments: {str(e)}",
                        extra={'entity_slug': entity_slug, 'filters': filters})

        return created_payments

    def _convert_phieu_chi_to_dict(self, payment) -> Dict[str, Any]:
        """
        Convert PhieuChi model instance to dictionary format for xu_ly=2.

        Parameters
        ----------
        payment : PhieuChiModel instance
            The payment model instance.

        Returns
        -------
        Dict[str, Any]
            Converted payment dictionary.
        """
        return {
            'id': str(payment.uuid),  # PhieuChi UUID for deletion
            'so_ct': getattr(payment, 'so_ct', ''),
            'ngay_ct': payment.ngay_ct.isoformat() if hasattr(payment, 'ngay_ct') and payment.ngay_ct else None,
            'ma_ngv': getattr(payment, 'ma_ngv', ''),
            'ma_kh': str(payment.ma_kh.uuid) if hasattr(payment, 'ma_kh') and payment.ma_kh else None,
            'tk': str(payment.tk.uuid) if hasattr(payment, 'tk') and payment.tk else None,
            'ma_nt': str(payment.ma_nt.uuid) if hasattr(payment, 'ma_nt') and payment.ma_nt else None,
            't_tt_nt': float(getattr(payment, 't_tien_nt', 0) or 0),
            'status': getattr(payment, 'status', 'ACTIVE'),
            'ma_tthddt': '',  # Not applicable for payments
            'unit_id': str(payment.unit_id.uuid) if hasattr(payment, 'unit_id') and payment.unit_id else None,
            'ma_ct': 'PC1',  # Payment voucher type
            'ma_unit': str(payment.unit_id.uuid) if hasattr(payment, 'unit_id') and payment.unit_id else None,
            'ten_kh': getattr(payment.ma_kh, 'ten_kh', '') if hasattr(payment, 'ma_kh') and payment.ma_kh else '',
            'ten_ngv': getattr(payment, 'ten_ngv', ''),
            'ten_ttct': 'Phiếu chi',
            'so_ct3': '',
            'ma_ct3': '',
            'id_ct3': '',
            'invoice_id': str(payment.id) if payment.id else None,  # Original invoice ID
            'phieu_chi_uuid': str(payment.uuid),  # For deletion
        }



    def _get_filtered_invoices(self, model_class, entity_slug: str, filters: Dict[str, Any], model_type: str, user_model=None) -> List[Dict[str, Any]]:
        """
        Get filtered purchase invoices using service pattern (following template rules).

        Parameters
        ----------
        model_class : Model
            The Django model class (HoaDonMuaHangTrongNuocModel, HoaDonMuaHangNhapKhauModel, or HoaDonMuaDichVuModel).
        entity_slug : str
            The entity slug.
        filters : Dict[str, Any]
            Filter parameters.
        model_type : str
            The model type string.
        user_model : User, optional
            The user model for permissions.

        Returns
        -------
        List[Dict[str, Any]]
            List of converted invoice dictionaries.
        """
        invoices = []

        try:
            # Build base queryset - ERP Expert: Only UNPAID invoices for xu_ly=1
            queryset = model_class.objects.filter(
                entity_model__slug=entity_slug,
                ngay_ct__gte=filters['ngay_ct1'],
                ngay_ct__lte=filters['ngay_ct2']
            ).exclude(
                status__in=['PAID', 'CANCELLED', 'DELETED']  # Exclude paid/cancelled/deleted
            )

            # Apply additional filters
            if filters.get('so_ct1') and filters.get('so_ct2'):
                queryset = queryset.filter(
                    so_ct__gte=filters['so_ct1'],
                    so_ct__lte=filters['so_ct2']
                )
            elif filters.get('so_ct1'):
                queryset = queryset.filter(so_ct__gte=filters['so_ct1'])
            elif filters.get('so_ct2'):
                queryset = queryset.filter(so_ct__lte=filters['so_ct2'])

            if filters.get('ma_kh'):
                queryset = queryset.filter(ma_kh=filters['ma_kh'])

            if filters.get('unit_id'):
                queryset = queryset.filter(unit_id=filters['unit_id'])

            # Apply optimized select_related
            queryset = queryset.select_related('ma_kh', 'ma_nt', 'ma_ct', 'unit_id', 'ma_ngv')

            # Convert each invoice to dictionary
            for hd in queryset:
                try:
                    invoices.append(self._convert_hoa_don_to_dict(hd, model_type))
                except Exception as e:
                    logger.error(f"Error converting {model_type} {hd.uuid}: {str(e)}",
                               extra={'entity_slug': entity_slug, 'invoice_id': str(hd.uuid)})
                    continue

        except Exception as e:
            logger.error(f"Error querying {model_type}: {str(e)}",
                        extra={'entity_slug': entity_slug, 'filters': filters})

        return invoices

    def _convert_hoa_don_to_dict(self, hd, model_type: str) -> Dict[str, Any]:
        """
        Convert purchase invoice model instance to dictionary format.

        Parameters
        ----------
        hd : Model instance
            The purchase invoice model instance.
        model_type : str
            The model type string.

        Returns
        -------
        Dict[str, Any]
            Converted invoice dictionary.
        """
        # Get document type mapping
        ma_ct_mapping = {
            'HoaDonMuaHangTrongNuocModel': 'HD4',
            'HoaDonMuaHangNhapKhauModel': 'HD5',
            'HoaDonMuaDichVuModel': 'HD6'
        }

        return {
            'id': str(hd.uuid),
            'so_ct': getattr(hd, 'so_ct', ''),
            'ngay_ct': hd.ngay_ct.isoformat() if hasattr(hd, 'ngay_ct') and hd.ngay_ct else None,
            'ma_ngv': getattr(hd, 'ma_ngv', ''),
            'ma_kh': str(hd.ma_kh.uuid) if hasattr(hd, 'ma_kh') and hd.ma_kh else None,
            'tk': getattr(hd, 'tk', ''),
            'ma_nt': str(hd.ma_nt.uuid) if hasattr(hd, 'ma_nt') and hd.ma_nt else None,
            't_tt_nt': float(getattr(hd, 't_tt_nt', 0) or 0),
            'status': getattr(hd, 'status', 'ACTIVE'),
            'ma_tthddt': getattr(hd, 'ma_tthddt', ''),
            'unit_id': str(hd.unit_id.uuid) if hasattr(hd, 'unit_id') and hd.unit_id else None,
            'ma_ct': ma_ct_mapping.get(model_type, ''),
            'ma_unit': str(hd.unit_id.uuid) if hasattr(hd, 'unit_id') and hd.unit_id else None,
            'ten_kh': getattr(hd, 'ten_kh', ''),
            'ten_ngv': getattr(hd, 'ten_ngv', ''),
            'ten_ttct': getattr(hd, 'ten_ttct', ''),
            'so_ct3': getattr(hd, 'so_ct3', ''),
            'ma_ct3': getattr(hd, 'ma_ct3', ''),
            'id_ct3': getattr(hd, 'id_ct3', ''),
        }

    @transaction.atomic
    def create_payments_from_invoices_direct(self, entity_slug: str, invoice_ids: List[str], common_payment_data: Dict[str, Any], user_model=None) -> Dict[str, Any]:
        """
        Create PhieuChi from selected purchase invoices with id field containing invoice ID.
        NEW LOGIC: Use the 'id' field to store the invoice ID for tracking.
        For multiple invoices, create separate payments (1:1 relationship).

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        invoice_ids : List[str]
            List of invoice UUIDs to create payments from.
        common_payment_data : Dict[str, Any]
            Common data applied to all payments.

        Returns
        -------
        Dict[str, Any]
            Creation result with success/failure details.
        """
        # Use the initialized service instead of creating new instance
        created_payments = []
        failed_payments = []
        total_amount = 0

        try:
            for invoice_id in invoice_ids:
                try:
                    # Get invoice details
                    invoice_info = self._get_invoice_details(
                        invoice_id=invoice_id,
                        entity_slug=entity_slug,
                        user_model=user_model
                    )
                    if not invoice_info:
                        failed_payments.append({
                            'invoice_id': invoice_id,
                            'error': 'Invoice not found'
                        })
                        continue

                    # Check if invoice is already paid
                    if invoice_info.get('status') in ['PAID', 'CANCELLED']:
                        failed_payments.append({
                            'invoice_id': invoice_id,
                            'error': f'Invoice is already {invoice_info.get("status")}'
                        })
                        continue

                    # Prepare PhieuChi data with invoice ID stored in 'id' field
                    phieu_chi_data = {
                        **common_payment_data,
                        'id': str(invoice_id),  # Store invoice ID as string for tracking
                        'ong_ba': invoice_info.get('ten_kh', ''),
                        'dien_giai': f"Thanh toán hóa đơn {invoice_info.get('so_ct', '')}",
                        'ma_kh': invoice_info.get('ma_kh'),
                        'ma_nt': invoice_info.get('ma_nt'),
                        't_tien_nt': Decimal(str(invoice_info.get('tong_tien', 0))),
                        't_tien': Decimal(str(invoice_info.get('tong_tien', 0))),
                        'status': 'ACTIVE',
                    }

                    # Create PhieuChi for this invoice using the service
                    phieu_chi = self.phieu_chi_service.create(entity_slug=entity_slug, data=phieu_chi_data)
                    if not phieu_chi:
                        raise ValueError("Failed to create PhieuChi - service returned None")

                    # Update invoice status to PAID
                    if not self._update_single_invoice_status(
                        invoice_id=invoice_id,
                        status='PAID',
                        entity_slug=entity_slug,
                        user_model=user_model
                    ):
                        # Rollback: delete the created PhieuChi if invoice update fails
                        try:
                            self.phieu_chi_service.delete(entity_slug=entity_slug, uuid=str(phieu_chi.uuid))
                        except:
                            pass  # Log but don't fail
                        raise ValueError(f"Failed to update invoice {invoice_id} status to PAID")

                    # Track success
                    created_payments.append({
                        'invoice_id': invoice_id,
                        'phieu_chi_uuid': str(phieu_chi.uuid),
                        'amount': float(phieu_chi_data['t_tien_nt'])
                    })
                    total_amount += float(phieu_chi_data['t_tien_nt'])

                    logger.info(f"Successfully created PhieuChi for invoice {invoice_id}",
                               extra={'entity_slug': entity_slug, 'invoice_id': invoice_id, 'phieu_chi_uuid': str(phieu_chi.uuid)})

                except Exception as e:
                    failed_payments.append({
                        'invoice_id': invoice_id,
                        'error': str(e)
                    })
                    logger.error(f"Failed to create PhieuChi for invoice {invoice_id}: {str(e)}",
                               extra={'entity_slug': entity_slug, 'invoice_id': invoice_id})
                    continue

            # Return data, not success/failure flags
            result = {
                'created_payments': created_payments,
                'failed_payments': failed_payments,
                'total_created': len(created_payments),
                'total_failed': len(failed_payments),
                'total_amount': total_amount
            }

            logger.info(f"Payment creation completed: {len(created_payments)} created, {len(failed_payments)} failed",
                       extra={'entity_slug': entity_slug, 'result': result})

            return result

        except Exception as e:
            raise ValueError(f"Failed to create payments from invoices: {str(e)}")

    @transaction.atomic
    def delete_payment_and_restore_invoice_direct(self, entity_slug: str, phieu_chi_uuid: str, user_model=None) -> Dict[str, Any]:
        """
        Delete a payment and restore associated invoice using id field.
        NEW LOGIC: Use the 'id' field to find the associated invoice.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        phieu_chi_uuid : str
            The UUID of the PhieuChi to delete.

        Returns
        -------
        Dict[str, Any]
            Deletion result with details.
        """
        # Use the initialized services instead of importing and creating new instances
        try:
            # Get the PhieuChi to find associated invoice
            phieu_chi = self.phieu_chi_service.get_by_uuid(uuid=phieu_chi_uuid, entity_slug=entity_slug)
            if not phieu_chi:
                raise ValueError(f"PhieuChi {phieu_chi_uuid} not found")

            # Get invoice ID from the 'id' field
            invoice_id = getattr(phieu_chi, 'id', None)
            restored = False

            # Restore invoice status if invoice_id exists
            if invoice_id:
                restored = self._update_single_invoice_status(
                    invoice_id=str(invoice_id),
                    status='ACTIVE',
                    entity_slug=entity_slug,
                    user_model=user_model
                )
                if not restored:
                    logger.warning(f"Failed to restore invoice {invoice_id} status",
                                 extra={'entity_slug': entity_slug, 'phieu_chi_uuid': phieu_chi_uuid, 'invoice_id': invoice_id})

            # Delete the PhieuChi using the service
            success = self.phieu_chi_service.delete(entity_slug=entity_slug, uuid=phieu_chi_uuid)

            if not success:
                raise ValueError(f"Failed to delete PhieuChi {phieu_chi_uuid}")

            # Return data, not success/failure flags
            result = {
                'phieu_chi_uuid': phieu_chi_uuid,
                'deleted': True,
                'invoice_restored': restored
            }

            if invoice_id:
                result['invoice_id'] = str(invoice_id)

            logger.info(f"Successfully deleted PhieuChi and restored invoice {invoice_id}",
                       extra={'entity_slug': entity_slug, 'phieu_chi_uuid': phieu_chi_uuid, 'invoice_id': invoice_id})

            return result

        except Exception as e:
            raise ValueError(f"Failed to delete payment and restore invoice: {str(e)}")



    def _get_invoice_details(self, invoice_id: str, entity_slug: str = None, user_model=None) -> Dict[str, Any]:
        """
        Get purchase invoice details using services (following template rules).

        Parameters
        ----------
        invoice_id : str
            Invoice UUID.
        entity_slug : str, optional
            Entity slug for permissions.
        user_model : User, optional
            User model for permissions.

        Returns
        -------
        Dict[str, Any]
            Invoice details or None if not found.
        """
        # Try HoaDonMuaHangTrongNuocService first (use pre-initialized service)
        try:
            invoice = self.hoa_don_mua_hang_trong_nuoc_service.get_by_id(
                uuid=invoice_id,
                entity_slug=entity_slug
            )
            if invoice:
                return self._extract_invoice_data(invoice, 'HoaDonMuaHangTrongNuocModel')
        except Exception as e:
            logger.warning(f"Error fetching invoice {invoice_id} via HoaDonMuaHangTrongNuocService: {e}")
            pass  # Try next service

        # TODO: Try other purchase invoice services when models are available
        # Note: These services may need different initialization patterns
        # try:
        #     # For HoaDonMuaHangNhapKhau and HoaDonMuaDichVu
        #     # We'll use direct model queries since services may not be available
        #     from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_nhap_khau import HoaDonMuaHangNhapKhauModel
        #     invoice = HoaDonMuaHangNhapKhauModel.objects.filter(
        #         uuid=invoice_id,
        #         entity_model__slug=entity_slug
        #     ).first()
        #     if invoice:
        #         return self._extract_invoice_data(invoice, 'HoaDonMuaHangNhapKhauModel')
        # except Exception:
        #     pass

        # try:
        #     from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuModel
        #     invoice = HoaDonMuaDichVuModel.objects.filter(
        #         uuid=invoice_id,
        #         entity_model__slug=entity_slug
        #     ).first()
        #     if invoice:
        #         return self._extract_invoice_data(invoice, 'HoaDonMuaDichVuModel')
        # except Exception:
        #     pass  # Invoice not found

        return None

    def _extract_invoice_data(self, invoice, model_type: str) -> Dict[str, Any]:
        """
        Extract common invoice data from purchase invoice instance.

        Parameters
        ----------
        invoice : Model instance
            The invoice model instance.
        model_type : str
            The model type string.

        Returns
        -------
        Dict[str, Any]
            Extracted invoice data.
        """
        return {
            'id': str(invoice.uuid),
            'so_ct': getattr(invoice, 'so_ct', ''),
            'ngay_ct': invoice.ngay_ct.isoformat() if hasattr(invoice, 'ngay_ct') and invoice.ngay_ct else None,
            'ma_kh': str(invoice.ma_kh.uuid) if hasattr(invoice, 'ma_kh') and invoice.ma_kh else None,
            'ten_kh': getattr(invoice, 'ten_kh', ''),
            'ma_nt': str(invoice.ma_nt.uuid) if hasattr(invoice, 'ma_nt') and invoice.ma_nt else None,
            'tong_tien': float(getattr(invoice, 't_tt_nt', 0) or getattr(invoice, 'tong_tien', 0) or 0),
            'status': getattr(invoice, 'status', 'ACTIVE'),
            'type': model_type
        }

    def _update_single_invoice_status(self, invoice_id: str, status: str, entity_slug: str = None, user_model=None) -> bool:
        """
        Update single purchase invoice status using services or direct model access.

        Parameters
        ----------
        invoice_id : str
            Invoice UUID.
        status : str
            New status value.
        entity_slug : str, optional
            Entity slug for permissions.
        user_model : User, optional
            User model for permissions.

        Returns
        -------
        bool
            True if update successful, False otherwise.
        """
        try:
            # Try HoaDonMuaHangTrongNuocService first
            try:
                invoice = self.hoa_don_mua_hang_trong_nuoc_service.get_by_id(
                    uuid=invoice_id,
                    entity_slug=entity_slug
                )
                if invoice:
                    updated = self.hoa_don_mua_hang_trong_nuoc_service.update(
                        entity_slug=entity_slug,
                        uuid=invoice_id,
                        data={'status': status}
                    )
                    return updated is not None
            except Exception as e:
                logger.warning(f"Error updating invoice {invoice_id} via HoaDonMuaHangTrongNuocService: {e}")
                pass  # Try next method

            # TODO: Try direct model updates for other invoice types when models are available
            # from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_nhap_khau import HoaDonMuaHangNhapKhauModel
            # from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuModel

            # # Try HoaDonMuaHangNhapKhau
            # updated_count = HoaDonMuaHangNhapKhauModel.objects.filter(
            #     uuid=invoice_id,
            #     entity_model__slug=entity_slug
            # ).update(status=status)
            # if updated_count > 0:
            #     return True

            # # Try HoaDonMuaDichVu
            # updated_count = HoaDonMuaDichVuModel.objects.filter(
            #     uuid=invoice_id,
            #     entity_model__slug=entity_slug
            # ).update(status=status)
            # if updated_count > 0:
            #     return True

            return False

        except Exception as e:
            logger.error(f"Failed to update invoice {invoice_id} status: {str(e)}",
                        extra={'entity_slug': entity_slug, 'invoice_id': invoice_id, 'status': status})
            return False
