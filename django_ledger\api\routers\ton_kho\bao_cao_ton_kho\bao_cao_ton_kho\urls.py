"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bao Cao Ton <PERSON>ho (Inventory Report) API endpoints.
"""

from django.urls import path

from django_ledger.api.views.ton_kho.bao_cao_ton_kho.bao_cao_ton_kho import (
    BaoCaoTonKhoViewSet,
)

# URL patterns - Single endpoint for inventory report with filters as POST body data
urlpatterns = [
    # Inventory Report endpoint - returns current stock balance report with filter POST body data
    path(
        "",
        BaoCaoTonKhoViewSet.as_view({"post": "get_report"}),
        name="bao-cao-ton-kho-report",
    ),
]
