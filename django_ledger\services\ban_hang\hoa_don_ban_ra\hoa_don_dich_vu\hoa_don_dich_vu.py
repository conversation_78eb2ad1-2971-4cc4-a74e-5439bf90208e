"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for HoaDonDichVu model.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (  # noqa: F401,
    ChiTietHoaDonModel,
    HoaDonDichVuModel,
    ThongTinThanhToanHoaDonDichVuModel,
)
from django_ledger.models.entity import EntityModel  # noqa: F401,
from django_ledger.repositories.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (  # noqa: F401,
    ChiTietHoaDonRepository,
    HoaDonDichVuRepository,
    ThongTinThanhToanHoaDonDichVuRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.utils_new.debt_management import CongNoCreation

class HoaDonDichVuService(BaseService):
    """
    Service class for handling HoaDonDichVu model business logic.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm

    Parameters
    ----------
    entity_slug : str
        The entity slug to filter by.
    user_model : UserModel
        The user model to check permissions.
    """

    # ✅ PREDEFINED CONFIGURATION: Hóa đơn dịch vụ accounting mappings
    SALES_INVOICE_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'CK',                   # Chiết khấu
            'debit_account_field': 'tk_ck',         # Tài khoản chiết khấu - DEBIT
            'credit_account_field': 'tk',           # Tài khoản nợ - CREDIT
            'debit_account_source': 'detail',       # Lấy debit account từ detail
            'credit_account_source': 'header',      # Lấy credit account từ header
            'amount_field': 'ck',                   # Chiết khấu (detail)
            'detail_source': 'chi_tiet',            # Related name
            'canCreate': True                       # Default: always create discount entry
        },
        {
            'journal_type': 'DTCK',                 # Doanh thu có chiết khấu
            'debit_account_field': 'tk',            # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_dt',        # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'header',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'tien2',                # Thành tiền (detail)
            'detail_source': 'chi_tiet',            # Related name
            'canCreate': True                       # Default: always create revenue entry
        },
        {
            'journal_type': 'DT0CK',                # Doanh thu không chiết khấu
            'debit_account_field': 'tk',            # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_dt',        # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'header',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'tien2',                # Thành tiền (detail)
            'detail_source': 'chi_tiet',            # Related name
            'canCreate': True                       # Default: always create revenue entry
        },
        {
            'journal_type': 'THUE',                 # Thuế
            'debit_account_field': 'tk',            # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_thue_co',   # Tài khoản thuế có - CREDIT
            'debit_account_source': 'header',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'thue',                 # Thuế (detail)
            'detail_source': 'chi_tiet',            # Related name
            'canCreate': True                       # Default: always create tax entry
        },
    ]

    def __init__(self, entity_slug: str, user_model):  # noqa: C901
        self.entity_slug = entity_slug
        self.user_model = user_model
        self.repository = HoaDonDichVuRepository()
        self.chi_tiet_repository = ChiTietHoaDonRepository()
        self.thanh_toan_repository = ThongTinThanhToanHoaDonDichVuRepository()
        self.model = HoaDonDichVuModel
        self.model_verbose_name = _('Hóa đơn dịch vụ')
        self._cong_no_service = CongNoCreation()
        super().__init__()

    def _determine_accounting_mappings(self, hoa_don: HoaDonDichVuModel) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional accounting entry creation dựa trên document status
        - Flexible business rules cho different scenarios
        - Support complex approval workflows

        Parameters
        ----------
        hoa_don : HoaDonDichVuModel
            Hóa đơn dịch vụ để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.SALES_INVOICE_ACCOUNTING_CONFIG.copy()

        # ✅ BUSINESS LOGIC: Determine canCreate based on invoice status and business rules
        for mapping in mappings:
            journal_type = mapping['journal_type']

            # Default: create entries for approved invoices (status = "1")
            can_create = hoa_don.status == "1"

            # ✅ CONDITIONAL LOGIC: Specific business rules for each journal type
            if journal_type == 'CK':  # Chiết khấu
                # Only create discount entry if there are details with discount > 0
                has_discount = False
                if hasattr(hoa_don, 'chi_tiet') and hoa_don.chi_tiet.exists():
                    has_discount = hoa_don.chi_tiet.filter(ck__gt=0).exists()
                can_create = can_create and has_discount

            elif journal_type == 'DTCK':  # Doanh thu có chiết khấu
                # Only create revenue entry if there are details with discount account and amount > 0
                has_revenue_with_discount = False
                if hasattr(hoa_don, 'chi_tiet') and hoa_don.chi_tiet.exists():
                    has_revenue_with_discount = hoa_don.chi_tiet.filter(
                        tk_ck__isnull=False, tien2__gt=0
                    ).exists()
                can_create = can_create and has_revenue_with_discount

            elif journal_type == 'DT0CK':  # Doanh thu không chiết khấu
                # Only create revenue entry if there are details without discount account and amount > 0
                has_revenue_without_discount = False
                if hasattr(hoa_don, 'chi_tiet') and hoa_don.chi_tiet.exists():
                    has_revenue_without_discount = hoa_don.chi_tiet.filter(
                        tk_ck__isnull=True, tien2__gt=0
                    ).exists()
                can_create = can_create and has_revenue_without_discount

            elif journal_type == 'THUE':  # Thuế
                # Only create tax entry if there are details with tax > 0
                has_tax = False
                if hasattr(hoa_don, 'chi_tiet') and hoa_don.chi_tiet.exists():
                    has_tax = hoa_don.chi_tiet.filter(thue__gt=0).exists()
                can_create = can_create and has_tax

            # Set the canCreate flag
            mapping['canCreate'] = can_create

        return mappings

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for the model.

        Returns
        -------
        QuerySet
            Base queryset for the model.
        """
        return self.repository.get_for_entity(
            entity_slug=self.entity_slug, user_model=self.user_model
        )

    def get_entity_model(self) -> EntityModel:  # noqa: C901
        """
        Get the entity model for the service.

        Returns
        -------
        EntityModel
            The entity model.
        """
        return get_object_or_404(EntityModel, slug=self.entity_slug)

    def get_by_uuid(self, uuid: str) -> HoaDonDichVuModel:  # noqa: C901
        """
        Get a HoaDonDichVuModel by UUID.

        Parameters
        ----------
        uuid : str
            The UUID of the HoaDonDichVuModel.

        Returns
        -------
        HoaDonDichVuModel
            The HoaDonDichVuModel with the specified UUID.
        """
        return self.repository.get_by_uuid(uuid)

    @transaction.atomic
    def create(self, data: Dict[str, Any]) -> HoaDonDichVuModel:  # noqa: C901
        """
        Create a new HoaDonDichVuModel.
        Handles ChungTuMixIn logic for document number generation.

        Parameters
        ----------
        data : Dict[str, Any]
            The data for the new HoaDonDichVuModel.

        Returns
        -------
        HoaDonDichVuModel
            The created HoaDonDichVuModel.
        """
        # Extract chi_tiet data if present
        chi_tiet_data = data.pop('chi_tiet', [])

        # Validate and process ChungTuMixIn fields
        self._validate_chung_tu_fields(data)

        # Create the HoaDonDichVuModel
        hoa_don = self.repository.create(entity_slug=self.entity_slug, data=data)

        # Create ChiTietHoaDonModel instances if provided
        for chi_tiet in chi_tiet_data:
            self.chi_tiet_repository.create(hoa_don=hoa_don, **chi_tiet)

        # ✅ ENHANCED: Auto-create accounting entries after successful creation
        try:
            self._cong_no_service.create_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán dịch vụ",
                account_mappings=self._determine_accounting_mappings(hoa_don)
            )
        except Exception as e:
            # Log the error but don't fail the creation process
            # This allows the invoice to be created even if accounting fails
            print(f"Warning: Failed to create accounting entries for invoice {hoa_don.so_ct}: {str(e)}")

        return hoa_don

    def _validate_chung_tu_fields(self, data: Dict[str, Any]) -> None:
        """
        Validate and process ChungTuMixIn fields.
        According to ChungTuMixIn logic, so_ct and i_so_ct are required when ma_nk is provided.

        Parameters
        ----------
        data : Dict[str, Any]
            The data dictionary to validate and modify.
        """
        # If ma_nk is provided, ensure so_ct and i_so_ct are present
        if data.get('ma_nk'):
            if not data.get('so_ct'):
                raise ValueError("so_ct là bắt buộc khi có ma_nk (quyển chứng từ)")
            if not data.get('i_so_ct'):
                raise ValueError("i_so_ct là bắt buộc khi có ma_nk (quyển chứng từ)")

        # Set default values if not provided
        if 'status' not in data:
            data['status'] = "1"
        if 'ty_gia' not in data:
            data['ty_gia'] = 1.0

    @transaction.atomic
    def update(
        self, uuid: str, data: Dict[str, Any]
    ) -> HoaDonDichVuModel:  # noqa: C901
        """
        Update an existing HoaDonDichVuModel.

        Parameters
        ----------
        uuid : str
            The UUID of the HoaDonDichVuModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonDichVuModel with.

        Returns
        -------
        HoaDonDichVuModel
            The updated HoaDonDichVuModel.
        """
        # Extract chi_tiet data if present
        chi_tiet_data = data.pop('chi_tiet', None)
        # Update the HoaDonDichVuModel
        hoa_don = self.repository.update(entity_slug=self.entity_slug, uuid=uuid, data=data)
        # Update ChiTietHoaDonModel instances if provided
        if chi_tiet_data is not None:
            # Delete existing chi_tiet
            self.chi_tiet_repository.delete_for_hoa_don(hoa_don_uuid=uuid)
            # Create new chi_tiet
            for chi_tiet in chi_tiet_data:
                self.chi_tiet_repository.create(hoa_don=hoa_don, **chi_tiet)

        # ✅ ENHANCED: Auto-update accounting entries after successful update
        try:
            self._cong_no_service.update_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán dịch vụ",
                account_mappings=self._determine_accounting_mappings(hoa_don)
            )
        except Exception as e:
            # Log the error but don't fail the update process
            # This allows the invoice to be updated even if accounting fails
            print(f"Warning: Failed to update accounting entries for invoice {hoa_don.so_ct}: {str(e)}")

        return hoa_don

    @transaction.atomic
    def delete(self, uuid: str) -> None:  # noqa: C901
        """
        Delete a HoaDonDichVuModel.

        Parameters
        ----------
        uuid : str
            The UUID of the HoaDonDichVuModel to delete.
        """
        # Delete associated ChiTietHoaDonModel instances
        self.chi_tiet_repository.delete_for_hoa_don(hoa_don_uuid=uuid)
        # Delete associated ThongTinThanhToanHoaDonDichVuModel instances
        self.thanh_toan_repository.model_class.objects.filter(
            hoa_don_dich_vu_id=uuid
        ).delete()

        # Delete the HoaDonDichVuModel
        self.repository.delete(uuid=uuid)

    def get_chi_tiet(self, hoa_don_uuid: str) -> QuerySet:  # noqa: C901
        """
        Get ChiTietHoaDonModel instances for a HoaDonDichVuModel.

        Parameters
        ----------
        hoa_don_uuid : str
            The UUID of the HoaDonDichVuModel.

        Returns
        -------
        QuerySet
            QuerySet of ChiTietHoaDonModel instances.
        """
        return self.chi_tiet_repository.get_for_hoa_don(hoa_don_uuid=hoa_don_uuid)

    def get_thanh_toan(self, hoa_don_uuid: str) -> QuerySet:  # noqa: C901
        """
        Get ThongTinThanhToanHoaDonDichVuModel instances for a HoaDonDichVuModel.

        Parameters
        ----------
        hoa_don_uuid : str
            The UUID of the HoaDonDichVuModel.

        Returns
        -------
        QuerySet
            QuerySet of ThongTinThanhToanHoaDonDichVuModel instances.
        """
        return self.thanh_toan_repository.get_by_hoa_don(hoa_don_id=hoa_don_uuid)

    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Lấy cấu hình kế toán cho hóa đơn dịch vụ.

        Returns:
            List[Dict[str, Any]]: Danh sách mapping configuration
        """
        return self.SALES_INVOICE_ACCOUNTING_CONFIG.copy()
