"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuYeuCauKiemKe (Inventory Check Request) serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.chung_tu import (  # noqa: F401
    ChungTuSerializer,
)

# Import serializers for foreign key fields
from django_ledger.api.serializers.entity import (  # noqa: F401,
    EntityModelSerializer,
)
from django_ledger.api.serializers.lo import (  # noqa: F401,
    LoModelSerializer,
)
from django_ledger.api.serializers.quyen_chung_tu import (  # noqa: F401,
    QuyenChungTuListSerializer,
)
from django_ledger.api.serializers.unit import (  # noqa: F401,
    EntityUnitModelSimpleSerializer,
)
from django_ledger.api.serializers.vi_tri import (  # noqa: F401,
    ViTriModelSerializer,
)
from django_ledger.api.serializers.warehouse import (  # noqa: F401,
    KhoHangModelSerializer,
)
from django_ledger.models.ton_kho.kiem_ke.phieu_yeu_cau_kiem_ke import (  # noqa: F401,
    PhieuYeuCauKiemKeModel,
)


class PhieuYeuCauKiemKeModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the PhieuYeuCauKiemKeModel.

    This serializer handles the conversion between PhieuYeuCauKiemKeModel instances and JSON representations,  # noqa: E501
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (entity_model, ma_nk, so_ct, ma_kho, ma_vi_tri, ma_lo)  # noqa: E501
    - Adds additional fields with "_data" suffix (ma_nk_data, so_ct_data, ma_kho_data, ma_vi_tri_data, ma_lo_data)  # noqa: E501
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    """

    # Reference data fields
    ma_nk_data = serializers.SerializerMethodField()
    so_ct_data = serializers.SerializerMethodField()
    ma_kho_data = serializers.SerializerMethodField()
    ma_vi_tri_data = serializers.SerializerMethodField()
    ma_lo_data = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField()

    class Meta:
        model = PhieuYeuCauKiemKeModel
        fields = [
            'uuid',
            'entity_model',
            'unit_id',
            'unit_id_data',
            'ngay_ct',
            'i_so_ct',
            'ma_nk',
            'ma_nk_data',
            'so_ct',
            'so_ct_data',
            'ma_kho',
            'ma_kho_data',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ma_lo',
            'ma_lo_data',
            'lo_yn',
            'tien_yn',
            'dien_giai',
            'status',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'unit_id_data',
            'ma_nk_data',
            'so_ct_data',
            'ma_kho_data',
            'ma_vi_tri_data',
            'ma_lo_data',
            'entity_model',
            'created',
            'updated',
        ]

    def get_unit_id_data(self, obj):  # noqa: C901
        """
        Get the entity unit data corresponding to the unit_id field
        """
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_ma_nk_data(self, obj):  # noqa: C901
        """
        Returns the document permission data for the ma_nk field.
        """
        if obj.ma_nk:
            return QuyenChungTuListSerializer(obj.ma_nk).data
        return None

    def get_so_ct_data(self, obj):  # noqa: C901
        """
        Returns the document data for the so_ct field.
        """
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        """
        Returns the warehouse data for the ma_kho field.
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_vi_tri_data(self, obj):  # noqa: C901
        """
        Returns the location data for the ma_vi_tri field.
        """
        if obj.ma_vi_tri:
            return ViTriModelSerializer(obj.ma_vi_tri).data
        return None

    def get_ma_lo_data(self, obj):  # noqa: C901
        """
        Returns the batch data for the ma_lo field.
        """
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None
