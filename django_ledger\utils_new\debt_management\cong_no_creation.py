"""
Django Ledger - Tiện ích Giao dịch Kế toán

Module này cung cấp tiện ích để tạo giao dịch kế toán, b<PERSON><PERSON> toán,
và quản lý sổ cái cho hệ thống ERP với kế toán kép đúng chuẩn.

Tạo bởi: Chuyên gia ERP & Lậ<PERSON> (20 năm kinh nghiệm)
Ngày: 2024-12-19
"""

from decimal import Decimal
from typing import Dict, Any, List, Optional

from datetime import date, datetime

from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError

from django_ledger.models import (
    LedgerModel,
    JournalEntryModel,
)

# Use lazy imports to avoid circular import issues

# Import JournalEntryMapping from types module to avoid circular imports
from .types import JournalEntryMapping

# ✅ PHASE 1: Enhanced Condition Structure - Supported Operators
SUPPORTED_OPERATORS = {
    'eq': '==',         # Equal
    'ne': '!=',         # Not equal
    'gt': '>',          # Greater than
    'gte': '>=',        # Greater than or equal
    'lt': '<',          # Less than
    'lte': '<=',        # Less than or equal
    'in': 'in',         # In list
    'not_in': 'not in', # Not in list
    'is_null': 'is None',       # Is null
    'is_not_null': 'is not None', # Is not null
    'between': 'between'        # Between range [min, max]
}


class CongNoCreation:
    """
    Lớp tiện ích để tạo giao dịch kế toán với kế toán kép đúng chuẩn.
    Xử lý quản lý sổ cái, bút toán, và tạo giao dịch.
    """

    def __init__(self):
        """
        Khởi tạo tiện ích kế toán cho một đơn vị cụ thể.

        """
        # Initialize services lazily to avoid circular imports
        self._ledger_service = None
        self._journal_entry_service = None
        self._transaction_service = None
        self._account_balance_audit_service = None

    @property
    def ledger_service(self):
        """Lazy loading for LedgerService to avoid circular imports."""
        if self._ledger_service is None:
            from django_ledger.services.ledger.ledger import LedgerService
            self._ledger_service = LedgerService()
        return self._ledger_service

    @property
    def journal_entry_service(self):
        """Lazy loading for JournalEntryService to avoid circular imports."""
        if self._journal_entry_service is None:
            from django_ledger.services.journal_entry.journal_entry import JournalEntryService
            self._journal_entry_service = JournalEntryService()
        return self._journal_entry_service

    @property
    def transaction_service(self):
        """Lazy loading for TransactionService to avoid circular imports."""
        if self._transaction_service is None:
            from django_ledger.services.transaction.transaction_service import TransactionService
            self._transaction_service = TransactionService()
        return self._transaction_service

    @property
    def account_balance_audit_service(self):
        """Lazy loading for AccountBalanceAuditService to avoid circular imports."""
        if self._account_balance_audit_service is None:
            from django_ledger.services.account_balance_audit.account_balance_audit import AccountBalanceAuditService
            self._account_balance_audit_service = AccountBalanceAuditService()
        return self._account_balance_audit_service

    def create_document_ledger(
        self,
        source_document,
        ledger_name: str
    ) -> LedgerModel:
        """
        Tạo ledger cho một chứng từ generic.

        Parameters
        ----------
        source_document : Model
            Chứng từ nguồn (hóa đơn, phiếu thu, phiếu chi, v.v.)
        ledger_name : str
            Tên ledger (text đơn giản)

        Returns
        -------
        LedgerModel
            Ledger đã tạo hoặc lấy được

        Raises
        ------
        ValueError
            Nếu source_document không có entity_model
        """

        # Tạo hoặc lấy ledger
        ledger = self.ledger_service.get_or_create_ledger(
            entity_slug=source_document.entity_model.slug,
            ledger_name=ledger_name,
            source_document=source_document
        )

        # Liên kết ledger với source document nếu chưa có
        if not hasattr(source_document, 'ledger') or not source_document.ledger:
            source_document.ledger = ledger
            source_document.save(update_fields=['ledger'])

        return ledger

    def create_document_journal_entry(
        self,
        source_document,
        ledger: LedgerModel,
        journal_description: str,
        document_date_field: str = 'ngay_ct',
        customer_field: str = 'ma_kh',
        journal_type: str = None
    ) -> JournalEntryModel:
        """
        Tạo journal entry cho một chứng từ generic.

        Parameters
        ----------
        source_document : Model
            Chứng từ nguồn (hóa đơn, phiếu thu, phiếu chi, v.v.)
        ledger : LedgerModel
            Ledger để tạo journal entry
        journal_description : str
            Mô tả journal entry (text đơn giản)
        document_date_field : str, optional
            Tên field chứa ngày chứng từ, mặc định 'ngay_ct'
        customer_field : str, optional
            Tên field chứa thông tin khách hàng, mặc định None
        journal_type : str, optional
            Loại bút toán (CONGNO, THUE, DT0CK, etc.), mặc định None

        Returns
        -------
        JournalEntryModel
            Journal entry đã tạo

        Raises
        ------
        ValueError
            Nếu source_document không có các field bắt buộc
        """

        # Lấy customer nếu có
        customer = None
        if customer_field and hasattr(source_document, customer_field):
            customer = getattr(source_document, customer_field)

        # Lấy document date
        document_date = getattr(source_document, document_date_field)
        if hasattr(document_date, 'date'):
            # Nếu là datetime, lấy date
            timestamp = timezone.make_aware(
                datetime.combine(document_date.date(), datetime.min.time())
            )
        else:
            # Nếu là date
            timestamp = timezone.make_aware(
                datetime.combine(document_date, datetime.min.time())
            )

        # Tạo journal entry
        journal_entry = self.journal_entry_service.create_document_journal_entry(
            ledger=ledger,
            description=journal_description,
            timestamp=timestamp,
            customer=customer,
            journal_type=journal_type,
            source_document=source_document
        )

        return journal_entry

    def create_document_opposititon_transaction(
            self,
            journal_entry,
            debit_account,
            credit_account,
            transaction_description: str,
            transaction_amount: Decimal,
            skip_audit_creation: bool = False  # ✅ NEW PARAMETER
        ):
        """
        ✅ FIXED: Tạo một giao dịch đối xứng trong một bút toán với conditional audit creation

        Parameters
        ----------
        skip_audit_creation : bool, optional
            True: Skip audit creation (for UPDATE operations - will be handled by recalculation)
            False: Create audit records (for CREATE operations - immediate audit creation)
        """

        # ✅ FIX: Ensure amount has max 2 decimal places
        transaction_amount = round(float(transaction_amount), 2)

        # Tạo giao dịch nợ
        debit_transaction = self.transaction_service.create_transaction(
            journal_entry=journal_entry,
            account=debit_account,
            tx_type='debit',
            amount=transaction_amount,
            description=transaction_description
        )

        # ✅ CONDITIONAL AUDIT CREATION
        if not skip_audit_creation:
            # ✅ CREATE AUDIT FOR NEW INVOICES (individual creation for performance)
            self.account_balance_audit_service.create(debit_transaction)
        # else: Skip audit for UPDATE operations (will be handled by cascade recalculation)

        # Tạo giao dịch có
        credit_transaction = self.transaction_service.create_transaction(
            journal_entry=journal_entry,
            account=credit_account,
            tx_type='credit',
            amount=transaction_amount,
            description=transaction_description
        )

        # ✅ CONDITIONAL AUDIT CREATION
        if not skip_audit_creation:
            # ✅ CREATE AUDIT FOR NEW INVOICES (individual creation for performance)
            self.account_balance_audit_service.create(credit_transaction)
        # else: Skip audit for UPDATE operations (will be handled by cascade recalculation)

        return debit_transaction, credit_transaction

    def _create_journal_entry_and_transactions(self, source_document, debit_account, credit_account, amount, ledger=None, journal_type: str = None, skip_audit_creation: bool = False):
        """Tạo journal entry và tất cả các giao dịch cho hóa đơn bán hàng."""
        try:
            journal_entry = self.create_document_journal_entry(
                source_document=source_document,
                ledger=ledger,
                journal_description=source_document.dien_giai,
                document_date_field='ngay_ct',
                customer_field='ma_kh',
                journal_type=journal_type
            )

            self.create_document_opposititon_transaction(
                journal_entry=journal_entry,
                debit_account=debit_account,
                credit_account=credit_account,
                transaction_description=f"Giao dịch đối ứng của tk {debit_account.code} và {credit_account.code}",
                transaction_amount=amount,
                skip_audit_creation=skip_audit_creation  # ✅ PASS PARAMETER
            )

            return True,

        except Exception as e:
            raise e

    def create_journal_entries_by_mapping(self, mapping: JournalEntryMapping):
        """
        Tạo các bút toán theo mapping được định nghĩa.

        Parameters
        ----------
        mapping : JournalEntryMapping
            Mapping định nghĩa cách tạo bút toán, bao gồm tất cả dữ liệu cần thiết
        """
        # Lấy dữ liệu từ mapping
        source_document = mapping['source_document']
        debit_account = mapping['debit_account']
        chi_tiet_list = mapping['chi_tiet_list']
        ledger = mapping['ledger']

        # Nhóm chi tiết theo tài khoản
        grouped_accounts = {}  # {account_code: {account: Account, amount: Decimal}}

        for chi_tiet in chi_tiet_list:
            # Lấy tài khoản theo field được định nghĩa trong mapping
            account = getattr(chi_tiet, mapping['account_field'], None)
            if account:
                account_code = account.code
                if account_code not in grouped_accounts:
                    grouped_accounts[account_code] = {
                        'account': account,
                        'amount': Decimal('0')
                    }

                # Lấy số tiền theo field được định nghĩa trong mapping
                amount_value = getattr(chi_tiet, mapping['amount_field'], None)
                grouped_accounts[account_code]['amount'] += Decimal(str(amount_value or 0))

        # Tạo bút toán cho từng nhóm tài khoản
        for account_code, account_info in grouped_accounts.items():
            if account_info['amount'] >= 0:
                self._create_journal_entry_and_transactions(
                    source_document=source_document,
                    debit_account=debit_account,
                    credit_account=account_info['account'],
                    amount=account_info['amount'],
                    ledger=ledger,
                    journal_type=mapping['journal_type'],
                    skip_audit_creation=mapping.get('skip_audit_creation', False)  # ✅ PASS PARAMETER
                )

    def delete_journal_entries_by_ledger(self, ledger: LedgerModel) -> Dict[str, Any]:
        """
        ✅ CLEAN: Delegate to JournalEntryService - SINGLE RESPONSIBILITY.

        SINGLE RESPONSIBILITY: This method ONLY delegates to the appropriate service.
        No duplicate logic for metadata collection or recalculation.

        Parameters
        ----------
        ledger : LedgerModel
            Ledger để xóa các bút toán

        Returns
        -------
        Dict[str, Any]
            Deletion results from JournalEntryService
        """
        # ✅ DELEGATE to JournalEntryService - no duplicate logic
        return self.journal_entry_service.delete_by_ledger(ledger)

    # ========================================================================
    # UNIFIED DOCUMENT ACCOUNTING METHODS - ERP EXPERT CONSOLIDATION
    # ========================================================================

    @transaction.atomic
    def create_document_accounting_entries(
        self,
        source_document,
        document_type: str = None,
        ledger_name_template: str = "Sổ cái {document_type} {so_ct}",
        account_mappings: List[Dict[str, Any]] = None
    ) -> bool:
        """
        ✅ UNIFIED METHOD: Tạo bút toán cho bất kỳ loại chứng từ nào.

        Thay thế tất cả các file bút toán utils riêng lẻ (hóa đơn bán hàng, phiếu thu, phiếu chi, v.v.)
        với một method tổng hợp duy nhất.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Hỗ trợ mọi loại chứng từ thông qua account_mappings
        - Tự động tạo ledger và journal entries
        - Xử lý cả header và detail records
        - Audit trail tự động

        Parameters
        ----------
        source_document : Model
            Chứng từ nguồn (hóa đơn, phiếu thu, phiếu chi, v.v.)
        document_type : str, optional
            Loại chứng từ (để tạo tên ledger), mặc định lấy từ model name
        ledger_name_template : str, optional
            Template tên ledger, mặc định "Sổ cái {document_type} {so_ct}"
        account_mappings : List[Dict[str, Any]], optional
            Danh sách mapping định nghĩa cách tạo bút toán
            Format: [
                {
                    'journal_type': 'DT0CK',
                    'debit_account_field': 'tk',           # Field name
                    'credit_account_field': 'tk_dt',       # Field name
                    'debit_account_source': 'header',      # 'header' hoặc 'detail' (mặc định 'header')
                    'credit_account_source': 'detail',     # 'header' hoặc 'detail' (mặc định 'detail')
                    'amount_field': 'tien2',               # Field số tiền
                    'detail_source': 'chi_tiet',           # Related name (None nếu header only)
                    'canCreate': True,                     # Flag để skip mapping này

                    # ✅ PHASE 1-3: Enhanced conditional logic with operators
                    'detail_conditions': {                 # ✅ NEW: Advanced conditions
                        'thue_nt': {'gt': 0},              # thue_nt > 0
                        'tien2': {'gte': 'tien1'},         # tien2 >= tien1 (field comparison)
                        'is_khuyenmai': {'eq': False},     # is_khuyenmai == False
                        'category': {'in': ['A', 'B']},    # category in ['A', 'B']
                        'discount': {'between': [0, 50]}   # discount between 0 and 50
                    }
                }
            ]

        Returns
        -------
        bool
            True nếu thành công

        Raises
        ------
        Exception
            Nếu có lỗi trong quá trình tạo bút toán

        Examples
        --------
        # Hóa đơn bán hàng (mặc định: debit từ header, credit từ detail)
        cong_no_service.create_document_accounting_entries(
            source_document=hoa_don,
            document_type="hóa đơn bán hàng",
            account_mappings=[
                {
                    'journal_type': 'DT0CK',
                    'debit_account_field': 'tk',
                    'credit_account_field': 'tk_dt',
                    'amount_field': 'tien2',
                    'detail_source': 'chi_tiet'
                }
            ]
        )

        # Phiếu thu (cả 2 account từ header)
        cong_no_service.create_document_accounting_entries(
            source_document=phieu_thu,
            document_type="phiếu thu",
            account_mappings=[
                {
                    'journal_type': 'CONGNO',
                    'debit_account_field': 'tk_tien',
                    'credit_account_field': 'tk_du',
                    'amount_field': 'tien',
                    'detail_source': None  # Header only
                }
            ]
        )

        # Ví dụ flexible: credit từ header, debit từ detail (có detail_source)
        cong_no_service.create_document_accounting_entries(
            source_document=document,
            account_mappings=[
                {
                    'journal_type': 'CUSTOM',
                    'debit_account_field': 'tk_no',
                    'credit_account_field': 'tk',
                    'debit_account_source': 'detail',      # Lấy từ detail
                    'credit_account_source': 'header',     # Lấy từ header
                    'amount_field': 't_tien',
                    'detail_source': 'chi_tiet',           # Có detail để lấy debit account
                    'canCreate': True
                }
            ]
        )

        # Ví dụ header-only: cả 2 account từ header (không có detail_source)
        cong_no_service.create_document_accounting_entries(
            source_document=phieu_thu,
            account_mappings=[
                {
                    'journal_type': 'CONGNO',
                    'debit_account_field': 'tk_tien',
                    'credit_account_field': 'tk_du',
                    'debit_account_source': 'header',      # Mặc định
                    'credit_account_source': 'header',     # Mặc định
                    'amount_field': 'tien',
                    'detail_source': None,                 # Header-only
                    'canCreate': True
                }
            ]
        )

        # ✅ PHASE 1-3: Advanced conditional accounting with operators
        cong_no_service.create_document_accounting_entries(
            source_document=hoa_don,
            document_type="hóa đơn bán hàng",
            account_mappings=[
                # High-value regular sales with complex conditions
                {
                    'journal_type': 'high_value_dt',
                    'debit_account_field': 'tk',
                    'credit_account_field': 'tk_dt_high',
                    'amount_field': 'tien2',
                    'detail_source': 'chi_tiet',
                    'detail_conditions': {                     # ✅ NEW: Enhanced conditions
                        'tien2': {'gt': 1000000},              # Revenue > 1M VND
                        'thue_nt': {'gt': 0},                  # Has tax
                        'is_khuyenmai': {'eq': False},         # Not promotional
                        'profit_margin': {'gte': 'cost_price'} # Profit >= Cost
                    },
                    'canCreate': True
                },

                # Promotional items with tax
                {
                    'journal_type': 'km_taxable',
                    'debit_account_field': 'tk',
                    'credit_account_field': 'tk_km_tax',
                    'amount_field': 'tien_km',
                    'detail_source': 'chi_tiet',
                    'detail_conditions': {                     # ✅ NEW: Enhanced conditions
                        'is_khuyenmai': {'eq': True},          # Promotional
                        'thue_km': {'gt': 0},                  # Has promotional tax
                        'category': {'in': ['A', 'B', 'VIP']} # Premium categories
                    },
                    'canCreate': True
                },

                # Bulk discount items
                {
                    'journal_type': 'bulk_discount',
                    'debit_account_field': 'tk',
                    'credit_account_field': 'tk_discount',
                    'amount_field': 'discount_amount',
                    'detail_source': 'chi_tiet',
                    'detail_conditions': {                     # ✅ NEW: Enhanced conditions
                        'quantity': {'gte': 100},              # Bulk quantity
                        'discount_percent': {'between': [10, 50]}, # Discount 10-50%
                        'unit_price': {'lt': 'market_price'}   # Below market price
                    },
                    'canCreate': True
                }
            ]
        )
        """
        try:
            # Auto-detect document type if not provided
            if not document_type:
                document_type = source_document._meta.verbose_name or source_document.__class__.__name__

            # Create ledger
            so_ct = getattr(source_document, 'so_ct', 'N/A')
            ledger_name = ledger_name_template.format(
                document_type=document_type,
                so_ct=so_ct
            )

            ledger = self.create_document_ledger(
                source_document=source_document,
                ledger_name=ledger_name
            )

            # Process account mappings
            if account_mappings:
                self._process_account_mappings(
                    source_document=source_document,
                    account_mappings=account_mappings,
                    ledger=ledger,
                    skip_audit_creation=False  # CREATE mode - enable audit
                )
            return True

        except Exception as e:
            raise Exception(f"Lỗi tạo bút toán cho {document_type}: {str(e)}") from e

    @transaction.atomic
    def update_document_accounting_entries(
        self,
        source_document,
        document_type: str = None,
        account_mappings: List[Dict[str, Any]] = None
    ) -> bool:
        """
        ✅ UNIFIED METHOD: Cập nhật bút toán cho bất kỳ loại chứng từ nào.

        Thay thế tất cả các file bút toán utils riêng lẻ với logic update thống nhất.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Delete-and-recreate pattern để đảm bảo tính nhất quán
        - Smart recalculation chỉ khi cần thiết
        - Audit trail được xử lý đúng thứ tự
        - Transaction safety với rollback

        Parameters
        ----------
        source_document : Model
            Chứng từ nguồn đã tồn tại
        document_type : str, optional
            Loại chứng từ (để logging), mặc định lấy từ model name
        account_mappings : List[Dict[str, Any]], optional
            Danh sách mapping định nghĩa cách tạo bút toán (same format as create method)

        Returns
        -------
        bool
            True nếu thành công

        Raises
        ------
        Exception
            Nếu có lỗi trong quá trình cập nhật bút toán

        Examples
        --------
        # Update hóa đơn bán hàng (mặc định: debit từ header, credit từ detail)
        cong_no_service.update_document_accounting_entries(
            source_document=hoa_don,
            account_mappings=[
                {
                    'journal_type': 'DT0CK',
                    'debit_account_field': 'tk',
                    'credit_account_field': 'tk_dt',
                    'amount_field': 'tien2',
                    'detail_source': 'chi_tiet'
                }
            ]
        )

        # Update với flexible account sources
        cong_no_service.update_document_accounting_entries(
            source_document=document,
            account_mappings=[
                {
                    'journal_type': 'CUSTOM',
                    'debit_account_field': 'tk_no',
                    'credit_account_field': 'tk',
                    'debit_account_source': 'detail',
                    'credit_account_source': 'header',
                    'amount_field': 't_tien',
                    'detail_source': 'chi_tiet',
                    'canCreate': True
                }
            ]
        )
        """
        try:
            # Auto-detect document type if not provided
            if not document_type:
                document_type = source_document._meta.verbose_name or source_document.__class__.__name__

            # Get existing ledger
            ledger = source_document.ledger
            if not ledger:
                raise Exception(f"Không tìm thấy ledger cho {document_type}")

            # Get document date for recalculation
            document_date = getattr(source_document, 'ngay_ct', None)
            if not document_date:
                raise Exception(f"Không tìm thấy ngày chứng từ cho {document_type}")

            # ✅ STEP 1: Delete old journal entries WITHOUT triggering recalculation yet
            deletion_result = self.journal_entry_service.delete_journal_entries_by_ledger(
                ledger=ledger,
                custom_from_date=document_date
            )

            # Validate deletion
            if not deletion_result.get('deleted_journal_entries', 0) and ledger.journalentrymodel_set.exists():
                raise Exception("Failed to delete existing journal entries")

            # ✅ STEP 2: Recreate journal entries with skip_audit_creation=True
            if account_mappings:
                self._process_account_mappings(
                    source_document=source_document,
                    account_mappings=account_mappings,
                    ledger=ledger,
                    skip_audit_creation=True  # UPDATE mode - let recalculation handle audit
                )

            # ✅ STEP 3: Trigger smart recalculation if needed
            if deletion_result.get('affected_accounts'):
                entity_id = str(ledger.entity.uuid)

                # Smart recalculation with affected accounts
                self.journal_entry_service.account_balance_audit_service.recalculate_after_journal_entry_deletion(
                    affected_accounts=deletion_result.get('affected_accounts'),
                    earliest_date=document_date,
                    entity_id=entity_id,
                )

            return True

        except Exception as e:
            raise Exception(f"Lỗi cập nhật bút toán cho {document_type}: {str(e)}") from e

    def _process_account_mappings(
        self,
        source_document,
        account_mappings: List[Dict[str, Any]],
        ledger: LedgerModel,
        skip_audit_creation: bool = False
    ):
        """
        ✅ HELPER METHOD: Xử lý danh sách account mappings để tạo journal entries.

        ERP Expert Logic:
        - Hỗ trợ cả header-only và header-detail patterns
        - Flexible field mapping cho mọi loại chứng từ
        - Consistent error handling

        Parameters
        ----------
        source_document : Model
            Chứng từ nguồn
        account_mappings : List[Dict[str, Any]]
            Danh sách mapping configurations
        ledger : LedgerModel
            Ledger để tạo journal entries
        skip_audit_creation : bool, optional
            True để skip audit creation (cho UPDATE operations)
        """
        for mapping_config in account_mappings:
            try:
                # Extract configuration
                journal_type = mapping_config.get('journal_type', 'GENERAL')
                debit_account_field = mapping_config.get('debit_account_field')
                credit_account_field = mapping_config.get('credit_account_field')
                debit_account_source = mapping_config.get('debit_account_source', 'header')  # Default: header
                credit_account_source = mapping_config.get('credit_account_source', 'detail')  # Default: detail
                amount_field = mapping_config.get('amount_field')
                detail_source = mapping_config.get('detail_source')
                can_create = mapping_config.get('canCreate', True)  # Default True for backward compatibility

                # ✅ CONDITIONAL CREATION: Skip nếu canCreate = False
                if not can_create:
                    continue  # Skip this mapping

                # Validate required fields
                if not all([debit_account_field, credit_account_field, amount_field, detail_source]):
                    raise Exception(f"Missing required fields in mapping config: {mapping_config}")

                # ✅ EXTRACT DETAIL CONDITIONS: Enhanced conditional logic with operators
                detail_conditions = mapping_config.get('detail_conditions', {})

                # ✅ UNIFIED PROCESSING: Sử dụng một method duy nhất cho mọi trường hợp
                self._process_flexible_account_mapping(
                    source_document=source_document,
                    debit_account_field=debit_account_field,
                    credit_account_field=credit_account_field,
                    debit_account_source=debit_account_source,
                    credit_account_source=credit_account_source,
                    amount_field=amount_field,
                    detail_source=detail_source,
                    journal_type=journal_type,
                    ledger=ledger,
                    skip_audit_creation=skip_audit_creation,
                    detail_conditions=detail_conditions  # ✅ NEW: Enhanced conditions
                )

            except Exception as e:
                raise Exception(f"Lỗi xử lý mapping {journal_type}: {str(e)}") from e

    def _check_detail_conditions_enhanced(self, chi_tiet, detail_conditions: Dict[str, Any] = None) -> bool:
        """
        ✅ PHASE 2: Enhanced condition checking with operators and field comparisons.

        ERP Expert Logic - Advanced conditional accounting:
        - Support multiple operators: >, <, >=, <=, ==, !=, in, between
        - Enable field-to-field comparisons: tien2 > tien1
        - Support multiple conditions with AND logic
        - Maintain high performance with optimized evaluation

        Parameters
        ----------
        chi_tiet : Model
            Detail record to check
        detail_conditions : Dict[str, Any], optional
            Enhanced conditions configuration:
            {
                'thue_nt': {'gt': 0},                    # thue_nt > 0
                'tien2': {'gte': 'tien1'},               # tien2 >= tien1 (field comparison)
                'is_khuyenmai': {'eq': True},            # is_khuyenmai == True
                'category': {'in': ['A', 'B', 'C']},     # category in list
                'discount': {'between': [0, 50]}         # discount between 0 and 50
            }

        Returns
        -------
        bool
            True if detail meets ALL conditions (AND logic)
            False if any condition fails

        Examples
        --------
        # Complex business rule
        conditions = {
            'thue_nt': {'gt': 0},           # Has tax
            'tien2': {'gte': 'tien1'},      # Revenue >= Cost
            'is_khuyenmai': {'eq': False}   # Not promotional
        }
        if self._check_detail_conditions_enhanced(chi_tiet, conditions):
            # Create high-value regular sale journal entries
        """
        # No conditions specified = always create
        if not detail_conditions:
            return True

        # Check all conditions (AND logic)
        for field_name, condition_config in detail_conditions.items():
            if not self._evaluate_field_condition(chi_tiet, field_name, condition_config):
                return False  # Any condition fails = skip this detail

        return True  # All conditions passed

    def _evaluate_field_condition(self, chi_tiet, field_name: str, condition_config: Dict[str, Any]) -> bool:
        """
        ✅ PHASE 2: Evaluate single field condition with operator support.

        Parameters
        ----------
        chi_tiet : Model
            Detail record to evaluate
        field_name : str
            Field name to check (e.g., 'thue_nt', 'tien2')
        condition_config : Dict[str, Any]
            Condition configuration:
            {'gt': 0} or {'gte': 'tien1'} or {'in': [1,2,3]}

        Returns
        -------
        bool
            True if condition is met, False otherwise

        Examples
        --------
        # Numeric comparison
        _evaluate_field_condition(chi_tiet, 'thue_nt', {'gt': 0})

        # Field comparison
        _evaluate_field_condition(chi_tiet, 'tien2', {'gte': 'tien1'})

        # List membership
        _evaluate_field_condition(chi_tiet, 'category', {'in': ['A', 'B']})
        """
        try:
            # Get actual value from detail record
            actual_value = getattr(chi_tiet, field_name, None)

            # Process each operator in condition config
            for operator, expected_value in condition_config.items():
                if not self._evaluate_condition(actual_value, operator, expected_value, chi_tiet):
                    return False  # Any operator fails = condition fails

            return True  # All operators passed

        except Exception as e:
            # Log error but don't break the flow
            print(f"Warning: Error evaluating condition for field '{field_name}': {str(e)}")
            return False  # Error = condition fails (safe default)

    def _evaluate_condition(self, actual_value: Any, operator: str, expected_value: Any, chi_tiet=None) -> bool:
        """
        ✅ PHASE 2: Core condition evaluation engine with operator support.

        Parameters
        ----------
        actual_value : Any
            Actual value from detail record field
        operator : str
            Operator to use: 'gt', 'lt', 'eq', 'in', etc.
        expected_value : Any
            Expected value or field reference
        chi_tiet : Model, optional
            Detail record for field reference resolution

        Returns
        -------
        bool
            True if condition is met, False otherwise

        Examples
        --------
        # Numeric comparison
        _evaluate_condition(100000, 'gt', 0)  # 100000 > 0 = True

        # Field comparison
        _evaluate_condition(200000, 'gte', 'tien1', chi_tiet)  # tien2 >= tien1

        # List membership
        _evaluate_condition('A', 'in', ['A', 'B', 'C'])  # 'A' in list = True
        """
        try:
            # Resolve expected value (could be literal or field reference)
            resolved_expected = self._resolve_field_reference(expected_value, chi_tiet)

            # Handle null checks first
            if operator == 'is_null':
                return actual_value is None
            elif operator == 'is_not_null':
                return actual_value is not None

            # Skip if actual value is None (except for null checks)
            if actual_value is None:
                return False

            # Evaluate based on operator
            if operator == 'eq':
                return actual_value == resolved_expected
            elif operator == 'ne':
                return actual_value != resolved_expected
            elif operator == 'gt':
                return actual_value > resolved_expected
            elif operator == 'gte':
                return actual_value >= resolved_expected
            elif operator == 'lt':
                return actual_value < resolved_expected
            elif operator == 'lte':
                return actual_value <= resolved_expected
            elif operator == 'in':
                return actual_value in resolved_expected
            elif operator == 'not_in':
                return actual_value not in resolved_expected
            elif operator == 'between':
                # Expected format: [min_value, max_value]
                if isinstance(resolved_expected, (list, tuple)) and len(resolved_expected) == 2:
                    min_val, max_val = resolved_expected
                    return min_val <= actual_value <= max_val
                return False
            else:
                print(f"Warning: Unsupported operator '{operator}'")
                return False

        except Exception as e:
            print(f"Warning: Error evaluating condition {actual_value} {operator} {expected_value}: {str(e)}")
            return False  # Error = condition fails (safe default)

    def _resolve_field_reference(self, field_ref: Any, chi_tiet=None) -> Any:
        """
        ✅ PHASE 2: Resolve field reference to actual value.

        Parameters
        ----------
        field_ref : Any
            Field reference or literal value:
            - 'tien1' → getattr(chi_tiet, 'tien1')
            - 100 → 100 (literal value)
            - [1,2,3] → [1,2,3] (literal list)
        chi_tiet : Model, optional
            Detail record for field resolution

        Returns
        -------
        Any
            Resolved value

        Examples
        --------
        # Field reference
        _resolve_field_reference('tien1', chi_tiet)  # Returns chi_tiet.tien1

        # Literal value
        _resolve_field_reference(100, chi_tiet)      # Returns 100

        # List value
        _resolve_field_reference([1,2,3], chi_tiet)  # Returns [1,2,3]
        """
        # If it's a string and we have chi_tiet, try to resolve as field
        if isinstance(field_ref, str) and chi_tiet is not None:
            # Check if it looks like a field name (not a literal string value)
            if field_ref.isidentifier() and hasattr(chi_tiet, field_ref):
                return getattr(chi_tiet, field_ref, None)

        # Return as literal value
        return field_ref

    def _process_flexible_account_mapping(
        self,
        source_document,
        debit_account_field: str,
        credit_account_field: str,
        debit_account_source: str,
        credit_account_source: str,
        amount_field: str,
        detail_source: str,
        journal_type: str,
        ledger: LedgerModel,
        skip_audit_creation: bool = False,
        detail_conditions: Dict[str, Any] = None  # ✅ NEW: Enhanced conditions with operators
    ):
        """
        ✅ UNIFIED METHOD: Xử lý mapping với flexible account sources cho mọi trường hợp.

        Logic:
        - Nếu có detail_source: xử lý từng detail record với conditional filtering
        - Nếu không có detail_source: xử lý header-only
        - Account có thể lấy từ header hoặc detail tùy theo cấu hình
        - ✅ NEW: Support detail-level conditions cho selective journal entry creation

        Parameters
        ----------
        debit_account_source : str
            'header' hoặc 'detail' - nguồn lấy debit account
        credit_account_source : str
            'header' hoặc 'detail' - nguồn lấy credit account
        detail_source : str or None
            Tên related field chứa chi tiết (None nếu header-only)
        detail_condition_field : str, optional
            ✅ NEW: Field name trong detail record để check condition (e.g., 'is_khuyenmai')
        detail_condition_value : Any, optional
            ✅ NEW: Expected value cho condition field (e.g., True, False, 1, 0)

        Examples
        --------
        # Chỉ tạo journal entries cho detail lines có is_khuyenmai=True
        self._process_flexible_account_mapping(
            ...,
            detail_condition_field='is_khuyenmai',
            detail_condition_value=True
        )

        # Chỉ tạo journal entries cho detail lines có is_khuyenmai=False
        self._process_flexible_account_mapping(
            ...,
            detail_condition_field='is_khuyenmai',
            detail_condition_value=False
        )
        """
        if detail_source:
            # ✅ CASE 1: Có detail_source - xử lý từng detail record
            detail_queryset = getattr(source_document, detail_source, None)
            if not detail_queryset:
                raise Exception(f"Detail source not found: {detail_source}")

            chi_tiet_list = detail_queryset.all()
            if not chi_tiet_list:
                return  # No details to process

            # Process each detail record
            for chi_tiet in chi_tiet_list:
                # ✅ NEW: Check detail-level conditions first
                if not self._check_detail_conditions_enhanced(chi_tiet, detail_conditions):
                    continue  # Skip this detail if conditions not met

                # Get debit account
                if debit_account_source == 'header':
                    debit_account = getattr(source_document, debit_account_field, None)
                else:  # debit_account_source == 'detail'
                    debit_account = getattr(chi_tiet, debit_account_field, None)

                if not debit_account:
                    continue  # Skip this detail if no debit account

                # Get credit account
                if credit_account_source == 'header':
                    credit_account = getattr(source_document, credit_account_field, None)
                else:  # credit_account_source == 'detail'
                    credit_account = getattr(chi_tiet, credit_account_field, None)

                if not credit_account:
                    continue  # Skip this detail if no credit account

                # Get amount
                amount_value = getattr(chi_tiet, amount_field, None)
                if amount_value is None or amount_value <= 0:
                    continue  # Skip if no amount

                # Create journal entry and transactions using existing wrapper
                self._create_journal_entry_and_transactions(
                    source_document=source_document,
                    debit_account=debit_account,
                    credit_account=credit_account,
                    amount=Decimal(str(amount_value)),
                    ledger=ledger,
                    journal_type=journal_type,
                    skip_audit_creation=skip_audit_creation
                )
        else:
            # ✅ CASE 2: Không có detail_source - header-only processing
            # Cả 2 account đều phải từ header (vì không có detail)
            debit_account = getattr(source_document, debit_account_field, None)
            if not debit_account:
                raise Exception(f"Debit account not found in header: {debit_account_field}")

            credit_account = getattr(source_document, credit_account_field, None)
            if not credit_account:
                raise Exception(f"Credit account not found in header: {credit_account_field}")

            # Get amount from header
            amount_value = getattr(source_document, amount_field, None)
            if amount_value is None or amount_value <= 0:
                return  # No amount to process

            # Create single journal entry using existing wrapper
            self._create_journal_entry_and_transactions(
                source_document=source_document,
                debit_account=debit_account,
                credit_account=credit_account,
                amount=Decimal(str(amount_value)),
                ledger=ledger,
                journal_type=journal_type,
                skip_audit_creation=skip_audit_creation
            )

    def _process_header_detail_mapping_flexible(
        self,
        source_document,
        debit_account_field: str,
        credit_account_field: str,
        debit_account_source: str,
        credit_account_source: str,
        amount_field: str,
        detail_source: str,
        journal_type: str,
        ledger: LedgerModel,
        skip_audit_creation: bool = False
    ):
        """
        ✅ NEW METHOD: Xử lý mapping cho pattern Header-Detail với flexible account sources.

        Logic: Hỗ trợ lấy debit/credit account từ header hoặc detail tùy theo cấu hình.

        Parameters
        ----------
        debit_account_source : str
            'header' hoặc 'detail' - nguồn lấy debit account
        credit_account_source : str
            'header' hoặc 'detail' - nguồn lấy credit account
        """
        # Get detail records
        detail_queryset = getattr(source_document, detail_source, None)
        if not detail_queryset:
            raise Exception(f"Detail source not found: {detail_source}")

        chi_tiet_list = detail_queryset.all()

        if not chi_tiet_list:
            return  # No details to process

        # Get accounts based on source configuration
        debit_account = None
        if debit_account_source == 'header':
            debit_account = getattr(source_document, debit_account_field, None)
            if not debit_account:
                raise Exception(f"Debit account not found in header: {debit_account_field}")

        # Process each detail record
        for chi_tiet in chi_tiet_list:
            # Get debit account (from header or detail)
            if debit_account_source == 'detail':
                debit_account = getattr(chi_tiet, debit_account_field, None)
                if not debit_account:
                    continue  # Skip this detail if no debit account

            # Get credit account (from header or detail)
            if credit_account_source == 'header':
                credit_account = getattr(source_document, credit_account_field, None)
            else:  # credit_account_source == 'detail'
                credit_account = getattr(chi_tiet, credit_account_field, None)

            if not credit_account:
                continue  # Skip this detail if no credit account

            # Get amount
            amount_value = getattr(chi_tiet, amount_field, None)
            if amount_value is None or amount_value <= 0:
                continue  # Skip if no amount

            # Create journal entry for this detail
            journal_entry = self.create_document_journal_entry(
                source_document=source_document,
                ledger=ledger,
                journal_description=f"{journal_type} - {source_document}",
                journal_type=journal_type
            )

            # Create transaction
            self.create_document_opposititon_transaction(
                journal_entry=journal_entry,
                debit_account=debit_account,
                credit_account=credit_account,
                transaction_description=f"{journal_type} - Chi tiết",
                transaction_amount=Decimal(str(amount_value)),
                skip_audit_creation=skip_audit_creation
            )

    def _process_header_only_mapping_flexible(
        self,
        source_document,
        debit_account_field: str,
        credit_account_field: str,
        amount_field: str,
        journal_type: str,
        ledger: LedgerModel,
        skip_audit_creation: bool = False
    ):
        """
        ✅ NEW METHOD: Xử lý mapping cho pattern Header-Only với flexible account sources.

        Logic: Lấy cả debit và credit account từ header (vì không có detail_source).
        """
        # Get debit account from header
        debit_account = getattr(source_document, debit_account_field, None)
        if not debit_account:
            raise Exception(f"Debit account not found in header: {debit_account_field}")

        # Get credit account from header
        credit_account = getattr(source_document, credit_account_field, None)
        if not credit_account:
            raise Exception(f"Credit account not found in header: {credit_account_field}")

        # Get amount from header
        amount_value = getattr(source_document, amount_field, None)
        if amount_value is None or amount_value <= 0:
            return  # No amount to process

        # Create journal entry and transactions using existing wrapper
        self._create_journal_entry_and_transactions(
            source_document=source_document,
            debit_account=debit_account,
            credit_account=credit_account,
            amount=Decimal(str(amount_value)),
            ledger=ledger,
            journal_type=journal_type,
            skip_audit_creation=skip_audit_creation
        )

    def _process_header_only_mapping(
        self,
        source_document,
        debit_account,
        credit_account_field: str,
        amount_field: str,
        journal_type: str,
        ledger: LedgerModel,
        skip_audit_creation: bool = False
    ):
        """
        ✅ HELPER: Xử lý mapping cho pattern Header-Only (e.g., phiếu thu, phiếu chi).

        Logic: Lấy credit account và amount từ header, tạo 1 journal entry duy nhất.
        """
        # Get credit account from header
        credit_account = getattr(source_document, credit_account_field, None)
        if not credit_account:
            raise Exception(f"Credit account not found: {credit_account_field}")

        # Get amount from header
        amount_value = getattr(source_document, amount_field, None)
        if amount_value is None or amount_value <= 0:
            return  # No amount to process

        # Create single journal entry and transactions
        self._create_journal_entry_and_transactions(
            source_document=source_document,
            debit_account=debit_account,
            credit_account=credit_account,
            amount=Decimal(str(amount_value)),
            ledger=ledger,
            journal_type=journal_type,
            skip_audit_creation=skip_audit_creation
        )
