"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiPhiChiTietHoaDonNhapMuaXuatThangModel, which represents the detailed costs
associated with materials in a HoaDonNhapMuaXuatThang (Invoice Import/Purchase/Export Monthly).
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiPhiChiTietHoaDonNhapMuaXuatThangModelQueryset(models.QuerySet):
    """
    A custom defined ChiPhiChiTietHoaDonNhapMuaXuatThangModel QuerySet.
    """

    def for_invoice(self, hoa_don_uuid):  # noqa: C901
        """
        Filter by invoice UUID.
        """
        return self.filter(hoa_don__uuid=hoa_don_uuid)


class ChiPhiChiTietHoaDonNhapMuaXuatThangModelManager(models.Manager):
    """
    A custom defined ChiPhiChiTietHoaDonNhapMuaXuatThangModel Manager that will act as an interface to handle the  # noqa: E501
    ChiPhiChiTietHoaDonNhapMuaXuatThangModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiPhiChiTietHoaDonNhapMuaXuatThangModelQueryset.
        """
        return ChiPhiChiTietHoaDonNhapMuaXuatThangModelQueryset(self.model, using=self._db)


class ChiPhiChiTietHoaDonNhapMuaXuatThangModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiPhiChiTietHoaDonNhapMuaXuatThangModel database will inherit from.  # noqa: E501
    The ChiPhiChiTietHoaDonNhapMuaXuatThangModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    hoa_don : ForeignKey
        Reference to the main HoaDonNhapMuaXuatThang.
    id_goc : IntegerField
        Original ID.
    line : IntegerField
        Line number.
    ma_cp : ForeignKey
        Cost code.
    ma_vt : ForeignKey
        Material code.
    tien_cp_nt : DecimalField
        Cost amount in foreign currency.
    tien_cp : DecimalField
        Cost amount.
    line_vt : IntegerField
        Material line number.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)

    # Link to main invoice
    hoa_don = models.ForeignKey(
        'django_ledger.HoaDonNhapMuaXuatThangModel',
        on_delete=models.CASCADE,
        related_name='chi_phi_vat_tu',
        verbose_name=_('Hóa đơn'),
        help_text=_('Main invoice reference'),
    )

    # Line information
    id_goc = models.IntegerField(
        verbose_name=_('ID gốc'),
        help_text=_('Original ID'),
        null=True,
        blank=True,
    )
    line = models.IntegerField(
        verbose_name=_('Số dòng'),
        help_text=_('Line number'),
    )

    # Cost and material information
    ma_cp = models.ForeignKey(
        'django_ledger.ChiPhiModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã chi phí'),
        help_text=_('Cost code'),
        related_name='chi_phi_chi_tiet_hoa_don_nhap_mua_xuat_thang',
        null=True,
        blank=True,
    )
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vật tư'),
        help_text=_('Material code'),
        related_name='chi_phi_chi_tiet_hoa_don_nhap_mua_xuat_thang',
        null=True,
        blank=True,
    )
    tien_cp_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền chi phí ngoại tệ'),
        help_text=_('Cost amount in foreign currency'),
    )
    tien_cp = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền chi phí'),
        help_text=_('Cost amount'),
    )
    line_vt = models.IntegerField(
        verbose_name=_('Dòng vật tư'),
        help_text=_('Material line number'),
    )

    objects = ChiPhiChiTietHoaDonNhapMuaXuatThangModelManager.from_queryset(
        ChiPhiChiTietHoaDonNhapMuaXuatThangModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi Phí Vật Tư Hóa Đơn Nhập Mua Xuất Tháng')
        verbose_name_plural = _('Chi Phí Vật Tư Hóa Đơn Nhập Mua Xuất Tháng')
        ordering = ['line']
        indexes = [
            models.Index(fields=['hoa_don']),
            models.Index(fields=['ma_cp']),
            models.Index(fields=['ma_vt']),
            models.Index(fields=['line']),
            models.Index(fields=['created']),
            models.Index(fields=['updated']),
        ]

    def __str__(self):
        return f"{self.hoa_don.so_ct} - Chi phí VT {self.ma_vt}"


class ChiPhiChiTietHoaDonNhapMuaXuatThangModel(ChiPhiChiTietHoaDonNhapMuaXuatThangModelAbstract):
    """
    Base ChiPhiChiTietHoaDonNhapMuaXuatThangModel from Abstract.
    """

    class Meta(ChiPhiChiTietHoaDonNhapMuaXuatThangModelAbstract.Meta):
        abstract = False
        db_table = "chi_phi_chi_tiet_hoa_don_nhap_mua_xuat_thang"
