"""
Sales Reports Serializers.

This module provides serializers for all sales reports.
"""

try:
    from .bao_cao_tinh_trang_don_hang import (
        BaoCaoTinhTrangDonHangRequestSerializer,
        BaoCaoTinhTrangDonHangResponseSerializer,
    )
except ImportError as e:
    print(f"Warning: Sales order status report serializers import error: {e}")
    pass

try:
    from .bao_cao_so_sanh_ban_hang_hai_ky import (
        BaoCaoSoSanhBanHangHaiKyRequestSerializer,
        BaoCaoSoSanhBanHangHaiKyResponseSerializer,
    )
except ImportError as e:
    print(f"Warning: Sales comparison report serializers import error: {e}")
    pass

__all__ = [
    'BaoCaoTinhTrangDonHangRequestSerializer',
    'BaoCaoTinhTrangDonHangResponseSerializer',
    'BaoCaoSoSanhBanHangHaiKyRequestSerializer',
    'BaoCaoSoSanhBanHangHaiKyResponseSerializer',
]
