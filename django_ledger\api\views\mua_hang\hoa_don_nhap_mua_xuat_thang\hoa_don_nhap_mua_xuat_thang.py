"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

HoaDonNhapMuaXuatThang ViewSet implementation.
"""

from django.db.models import Q  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401
from rest_framework import status  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401
from rest_framework.viewsets import ModelViewSet  # noqa: F401

from django_ledger.api.decorators.error_handling import (  # noqa: F401,
    api_exception_handler,
)
from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    HoaDonNhapMuaXuatThangCreateUpdateSerializer,
    HoaDonNhapMuaXuatThangSerializer,
)
from django_ledger.models import EntityModel  # noqa: F401
from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    HoaDonNhapMuaXuatThangModel,
)
from django_ledger.services.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    HoaDonNhapMuaXuatThangService,
)


class HoaDonNhapMuaXuatThangViewSet(ModelViewSet):
    """
    ViewSet for HoaDonNhapMuaXuatThangModel.
    Provides CRUD operations for import/purchase/export monthly invoices.
    """

    queryset = HoaDonNhapMuaXuatThangModel.objects.all()
    serializer_class = HoaDonNhapMuaXuatThangSerializer
    permission_classes = [IsAuthenticated]
    service = HoaDonNhapMuaXuatThangService()
    lookup_field = "uuid"

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for HoaDonNhapMuaXuatThangModel.
        Filters by entity_slug if provided in the URL.
        """
        entity_slug = self.kwargs.get("entity_slug")
        if entity_slug:
            return (
                self.service.list(entity_slug=entity_slug, user_model=self.request.user)
                .prefetch_related('chi_tiet', 'chi_phi', 'chi_phi_vat_tu', 'thue')
                .distinct()
            )
        return self.queryset.prefetch_related(
            'chi_tiet', 'chi_phi', 'chi_phi_vat_tu', 'thue'
        ).distinct()

    def get_serializer_class(self):  # noqa: C901
        """
        Get the appropriate serializer class based on the action.

        Returns
        -------
        Serializer
            The serializer class for the current action.
        """
        if self.action in ['create', 'update', 'partial_update']:
            return HoaDonNhapMuaXuatThangCreateUpdateSerializer
        return HoaDonNhapMuaXuatThangSerializer

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List HoaDonNhapMuaXuatThangModel instances.
        """
        queryset = self.get_queryset()
        # Apply filters from query parameters
        status_filter = request.query_params.get("status")
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        customer_id = request.query_params.get("customer_id")
        if customer_id:
            queryset = queryset.filter(ma_kh_id=customer_id)
        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new HoaDonNhapMuaXuatThangModel instance.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Extract nested data
        chi_tiet_data = request.data.get("chi_tiet_data", [])
        chi_phi_data = request.data.get("chi_phi_data", [])
        chi_phi_chi_tiet_data = request.data.get("chi_phi_chi_tiet_data", [])
        thue_data = request.data.get("thue_data", [])

        entity_slug = self.kwargs.get("entity_slug")
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)

        # Use service to create the invoice with nested data
        hoa_don = self.service.create_invoice(
            entity_model=entity_model,
            hoa_don_data=serializer.validated_data,
            chi_tiet_data=chi_tiet_data,
            chi_phi_data=chi_phi_data,
            chi_phi_chi_tiet_data=chi_phi_chi_tiet_data,
            thue_data=thue_data,
        )

        # Return the created instance using read serializer
        response_serializer = HoaDonNhapMuaXuatThangSerializer(hoa_don)
        headers = self.get_success_headers(response_serializer.data)
        return Response(
            response_serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing HoaDonNhapMuaXuatThangModel instance.
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        # Extract nested data from request
        chi_tiet_data = request.data.get("chi_tiet_data", [])
        chi_phi_data = request.data.get("chi_phi_data", [])
        chi_phi_chi_tiet_data = request.data.get("chi_phi_chi_tiet_data", [])
        thue_data = request.data.get("thue_data", [])

        # Use service to update the invoice with nested data
        updated_instance = self.service.update_invoice(
            instance.uuid,
            serializer.validated_data,
            chi_tiet_data,
            chi_phi_data,
            chi_phi_chi_tiet_data,
            thue_data,
        )

        # Return updated instance using read serializer
        response_serializer = HoaDonNhapMuaXuatThangSerializer(updated_instance)
        return Response(response_serializer.data)

    def perform_update(self, serializer):  # noqa: C901
        """
        Perform the update of a HoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        serializer : HoaDonNhapMuaXuatThangCreateUpdateSerializer
            The serializer with validated data.
        """
        entity_slug = self.kwargs.get('entity_slug')
        serializer.save(entity_slug=entity_slug)

    @action(detail=True, methods=['post'])
    def calculate_totals(self, request, entity_slug=None, pk=None):  # noqa: C901
        """
        Calculate and update totals for the invoice.

        Parameters
        ----------
        request : Request
            The HTTP request.
        entity_slug : str
            The entity slug.
        pk : UUID
            The invoice UUID.

        Returns
        -------
        Response
            The response with updated totals.
        """
        try:
            invoice = self.get_object()
            # Calculate totals from related details
            totals = self.service.calculate_invoice_totals(invoice.uuid)

            # Update the invoice with calculated totals
            updated_invoice = self.service.update(invoice.uuid, totals, entity_slug)

            serializer = self.get_serializer(updated_invoice)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def summary(self, request, entity_slug=None, pk=None):  # noqa: C901
        """
        Get summary information for the invoice.

        Parameters
        ----------
        request : Request
            The HTTP request.
        entity_slug : str
            The entity slug.
        pk : UUID
            The invoice UUID.

        Returns
        -------
        Response
            The response with invoice summary.
        """
        try:
            invoice = self.get_object()

            # Get summary data
            summary_data = {
                'invoice': self.get_serializer(invoice).data,
                'details_count': invoice.chi_tiet.count(),
                'costs_count': invoice.chi_phi.count(),
                'detailed_costs_count': invoice.chi_phi_chi_tiet.count(),
                'taxes_count': invoice.thue.count(),
                'total_amount': invoice.t_tien,
                'total_amount_nt': invoice.t_tien_nt,
                'total_tax': invoice.t_thue,
                'total_tax_nt': invoice.t_thue_nt,
                'total_payment': invoice.t_tt,
                'total_payment_nt': invoice.t_tt_nt,
            }

            return Response(summary_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def statistics(self, request, entity_slug=None):  # noqa: C901
        """
        Get statistics for invoices in the entity.

        Parameters
        ----------
        request : Request
            The HTTP request.
        entity_slug : str
            The entity slug.

        Returns
        -------
        Response
            The response with invoice statistics.
        """
        try:
            stats = self.service.get_total_amount_by_entity(entity_slug)

            # Add count statistics
            queryset = self.get_queryset()
            stats.update(
                {
                    'total_invoices': queryset.count(),
                    'active_invoices': queryset.filter(status='active').count(),
                    'draft_invoices': queryset.filter(status='draft').count(),
                }
            )

            return Response(stats, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def by_customer(self, request, entity_slug=None):  # noqa: C901
        """
        Get invoices filtered by customer.

        Parameters
        ----------
        request : Request
            The HTTP request.
        entity_slug : str
            The entity slug.

        Returns
        -------
        Response
            The response with filtered invoices.
        """
        customer_uuid = request.query_params.get('customer_uuid')
        if not customer_uuid:
            return Response(
                {'error': 'customer_uuid parameter is required'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            invoices = self.service.filter_by_customer(customer_uuid, entity_slug)
            page = self.paginate_queryset(invoices)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(invoices, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def by_date_range(self, request, entity_slug=None):  # noqa: C901
        """
        Get invoices filtered by date range.

        Parameters
        ----------
        request : Request
            The HTTP request.
        entity_slug : str
            The entity slug.

        Returns
        -------
        Response
            The response with filtered invoices.
        """
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        if not start_date or not end_date:
            return Response(
                {'error': 'start_date and end_date parameters are required'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            invoices = self.service.filter_by_date_range(
                start_date, end_date, entity_slug
            )
            page = self.paginate_queryset(invoices)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(invoices, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
