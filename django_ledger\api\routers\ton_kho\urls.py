"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Ton Kho (Inventory) module.
"""

from django.urls import include, path

urlpatterns = [
    path(
        'xuat-kho-noi-bo/',
        include('django_ledger.api.routers.ton_kho.xuat_kho_noi_bo.urls'),
    ),
    path(
        'nhap-kho-noi-bo/',
        include('django_ledger.api.routers.ton_kho.nhap_kho_noi_bo.urls'),
    ),
    path('kiem-ke/', include('django_ledger.api.routers.ton_kho.kiem_ke.urls')),
    path(
        'tinh-hinh-nhap-xuat-kho/',
        include(
            'django_ledger.api.routers.ton_kho.tinh_hinh_nhap_xuat_kho.urls'
        ),
    ),
    path(
        'nhap-tu-dong-thanh-pham-tu-hoa-don/',
        include('django_ledger.api.routers.ton_kho.nhap_tu_dong_thanh_pham_tu_hoa_don.urls'),
    ),
    path(
        'tinh-gia-hang-ton-kho/',
        include('django_ledger.api.routers.ton_kho.tinh_gia_hang_ton_kho.urls'),
    ),
    path(
        'tong-hop-nhap-xuat-ton/',
        include('django_ledger.api.routers.ton_kho.tong_hop_nhap_xuat_ton.urls'),
    ),
    path(
        'bao-cao-ton-kho/',
        include('django_ledger.api.routers.ton_kho.bao_cao_ton_kho.urls'),
    ),
]
