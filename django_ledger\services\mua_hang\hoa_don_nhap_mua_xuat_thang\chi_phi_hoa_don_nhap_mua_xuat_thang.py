"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiPhiHoaDonNhapMuaXuatThang Service implementation.
"""

from typing import Any, Dict  # noqa: F401
from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiHoaDonNhapMuaXuatThangModel,
)
from django_ledger.repositories.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiPhiHoaDonNhapMuaXuatThangRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401


class ChiPhiHoaDonNhapMuaXuatThangService(BaseService):
    """
    Service class for ChiPhiHoaDonNhapMuaXuatThangModel.
    Provides business logic for invoice cost operations.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the ChiPhiHoaDonNhapMuaXuatThangRepository.
        """
        super().__init__()
        self.repository = ChiPhiHoaDonNhapMuaXuatThangRepository()

    def get_by_invoice(self, hoa_don_uuid) -> QuerySet:  # noqa: C901
        """
        Get ChiPhiHoaDonNhapMuaXuatThangModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiPhiHoaDonNhapMuaXuatThangModel instances for the specified invoice.
        """
        return self.repository.get_by_invoice(hoa_don_uuid)

    def get_by_id(self, uuid, hoa_don_uuid=None) -> ChiPhiHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Get a ChiPhiHoaDonNhapMuaXuatThangModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiPhiHoaDonNhapMuaXuatThangModel to retrieve.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiPhiHoaDonNhapMuaXuatThangModel
            The retrieved ChiPhiHoaDonNhapMuaXuatThangModel instance.
        """
        return self.repository.get_by_id(uuid, hoa_don_uuid)

    def create(self, data: Dict[str, Any]) -> ChiPhiHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Create a new ChiPhiHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the ChiPhiHoaDonNhapMuaXuatThangModel with.

        Returns
        -------
        ChiPhiHoaDonNhapMuaXuatThangModel
            The created ChiPhiHoaDonNhapMuaXuatThangModel instance.
        """
        return self.repository.create(**data)

    def update(self, uuid, data: Dict[str, Any], hoa_don_uuid=None) -> ChiPhiHoaDonNhapMuaXuatThangModel:  # noqa: C901
        """
        Update a ChiPhiHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiPhiHoaDonNhapMuaXuatThangModel to update.
        data : Dict[str, Any]
            The data to update the ChiPhiHoaDonNhapMuaXuatThangModel with.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.

        Returns
        -------
        ChiPhiHoaDonNhapMuaXuatThangModel
            The updated ChiPhiHoaDonNhapMuaXuatThangModel instance.
        """
        instance = self.get_by_id(uuid, hoa_don_uuid)
        return self.repository.update(instance, **data)

    def delete(self, uuid, hoa_don_uuid=None) -> None:  # noqa: C901
        """
        Delete a ChiPhiHoaDonNhapMuaXuatThangModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiPhiHoaDonNhapMuaXuatThangModel to delete.
        hoa_don_uuid : UUID, optional
            The UUID of the invoice to filter by, by default None.
        """
        instance = self.get_by_id(uuid, hoa_don_uuid)
        self.repository.delete(instance)

    def get_total_by_invoice(self, hoa_don_uuid) -> dict:  # noqa: C901
        """
        Get total cost amounts for ChiPhiHoaDonNhapMuaXuatThangModel instances by invoice.

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the invoice to filter by.

        Returns
        -------
        dict
            A dictionary containing total cost amounts.
        """
        return self.repository.get_total_by_invoice(hoa_don_uuid)
