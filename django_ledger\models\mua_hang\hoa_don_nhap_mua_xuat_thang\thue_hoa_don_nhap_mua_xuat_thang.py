"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ThueHoaDonNhapMuaXuatThangModel, which represents the taxes
associated with a HoaDonNhapMuaXuatThang (Invoice Import/Purchase/Export Monthly).
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ThueHoaDonNhapMuaXuatThangModelQueryset(models.QuerySet):
    """
    A custom defined ThueHoaDonNhapMuaXuatThangModel QuerySet.
    """

    def for_invoice(self, hoa_don_uuid):  # noqa: C901
        """
        Filter by invoice UUID.
        """
        return self.filter(hoa_don__uuid=hoa_don_uuid)


class ThueHoaDonNhapMuaXuatThangModelManager(models.Manager):
    """
    A custom defined ThueHoaDonNhapMuaXuatThangModel Manager that will act as an interface to handle the  # noqa: E501
    ThueHoaDonNhapMuaXuatThangModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ThueHoaDonNhapMuaXuatThangModelQueryset.
        """
        return ThueHoaDonNhapMuaXuatThangModelQueryset(self.model, using=self._db)


class ThueHoaDonNhapMuaXuatThangModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ThueHoaDonNhapMuaXuatThangModel database will inherit from.  # noqa: E501
    The ThueHoaDonNhapMuaXuatThangModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    hoa_don : ForeignKey
        Reference to the main HoaDonNhapMuaXuatThang.
    id_goc : IntegerField
        Original ID.
    line : IntegerField
        Line number.
    ma_thue : ForeignKey
        Tax code.
    ten_thue : CharField
        Tax name.
    thue_suat : DecimalField
        Tax rate.
    so_ct0 : CharField
        Document number 0.
    so_ct2 : CharField
        Document number 2.
    ngay_ct0 : DateTimeField
        Document date 0.
    ma_mau_ct : CharField
        Document template code.
    ma_mau_bc : CharField
        Report template code.
    ma_tc_thue : CharField
        Tax property code.
    ma_kh : ForeignKey
        Customer code.
    ten_kh_thue : CharField
        Tax customer name.
    dia_chi : TextField
        Address.
    ma_so_thue : CharField
        Tax code.
    ten_vt_thue : CharField
        Tax material name.
    t_tien_nt : DecimalField
        Total amount in foreign currency.
    t_tien : DecimalField
        Total amount.
    tk_thue_no : ForeignKey
        Tax debit account.
    ten_tk_thue_no : CharField
        Tax debit account name.
    tk_du : ForeignKey
        Balance account.
    ten_tk_du : CharField
        Balance account name.
    t_thue_nt : DecimalField
        Total tax in foreign currency.
    t_thue : DecimalField
        Total tax.
    ma_kh9 : ForeignKey
        Customer code 9.
    ten_kh9 : CharField
        Customer name 9.
    ma_tt : ForeignKey
        Payment method code.
    ten_tt : CharField
        Payment method name.
    ghi_chu : TextField
        Notes.
    id_tt : IntegerField
        Payment ID.
    ma_bp : ForeignKey
        Department code.
    ma_vv : ForeignKey
        Task code.
    ma_hd : ForeignKey
        Contract code.
    ma_dtt : ForeignKey
        Tax object code.
    ma_ku : ForeignKey
        Area code.
    ma_phi : ForeignKey
        Fee code.
    ma_sp : ForeignKey
        Product code.
    ma_lsx : ForeignKey
        Production order code.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)

    # Link to main invoice
    hoa_don = models.ForeignKey(
        'django_ledger.HoaDonNhapMuaXuatThangModel',
        on_delete=models.CASCADE,
        related_name='thue',
        verbose_name=_('Hóa đơn'),
        help_text=_('Main invoice reference'),
    )

    # Line information
    id_goc = models.IntegerField(
        verbose_name=_('ID gốc'),
        help_text=_('Original ID'),
        null=True,
        blank=True,
    )
    line = models.IntegerField(
        verbose_name=_('Số dòng'),
        help_text=_('Line number'),
    )

    # Tax information
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thuế'),
        help_text=_('Tax code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang',
        null=True,
        blank=True,
    )
    ten_thue = models.CharField(
        max_length=100,
        verbose_name=_('Tên thuế'),
        help_text=_('Tax name'),
        blank=True,
    )
    thue_suat = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_('Thuế suất'),
        help_text=_('Tax rate'),
    )

    # Tax document information
    so_ct0 = models.CharField(
        max_length=20,
        verbose_name=_('Số chứng từ 0'),
        help_text=_('Document number 0'),
    )
    so_ct2 = models.CharField(
        max_length=20,
        verbose_name=_('Số chứng từ 2'),
        help_text=_('Document number 2'),
    )
    ngay_ct0 = models.DateTimeField(
        verbose_name=_('Ngày chứng từ 0'),
        help_text=_('Document date 0'),
    )
    ma_mau_ct = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_('Mã mẫu chứng từ'),
        help_text=_('Document template code'),
    )
    ma_mau_bc = models.CharField(
        max_length=20,
        verbose_name=_('Mã mẫu báo cáo'),
        help_text=_('Report template code'),
    )
    ma_tc_thue = models.CharField(
        max_length=20,
        verbose_name=_('Mã tờ chính thuế'),
        help_text=_('Tax property code'),
    )

    # Tax customer information
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng'),
        help_text=_('Customer code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_kh',
        null=True,
        blank=True,
    )
    ten_kh_thue = models.CharField(
        max_length=200,
        verbose_name=_('Tên khách hàng thuế'),
        help_text=_('Tax customer name'),
        blank=True,
    )
    dia_chi = models.TextField(
        verbose_name=_('Địa chỉ'),
        help_text=_('Address'),
    )
    ma_so_thue = models.CharField(
        max_length=50,
        verbose_name=_('Mã số thuế'),
        help_text=_('Tax code'),
    )
    ten_vt_thue = models.CharField(
        max_length=200,
        verbose_name=_('Tên vật tư thuế'),
        help_text=_('Tax material name'),
        blank=True,
    )

    # Tax amounts
    t_tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ'),
        help_text=_('Total amount in foreign currency'),
    )
    t_tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền'),
        help_text=_('Total amount'),
    )

    # Tax accounts
    tk_thue_no = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản thuế nợ'),
        help_text=_('Tax debit account'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_tk_no',
        null=True,
        blank=True,
    )
    ten_tk_thue_no = models.CharField(
        max_length=200,
        verbose_name=_('Tên tài khoản thuế nợ'),
        help_text=_('Tax debit account name'),
        blank=True,
    )
    tk_du = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản dư'),
        help_text=_('Balance account'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_tk_du',
        null=True,
        blank=True,
    )
    ten_tk_du = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Tên tài khoản dư'),
        help_text=_('Balance account name'),
    )
    t_thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thuế ngoại tệ'),
        help_text=_('Total tax in foreign currency'),
    )
    t_thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thuế'),
        help_text=_('Total tax'),
    )

    # Additional information
    ma_kh9 = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng 9'),
        help_text=_('Customer code 9'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_kh9',
        null=True,
        blank=True,
    )
    ten_kh9 = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Tên khách hàng 9'),
        help_text=_('Customer name 9'),
    )
    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thanh toán'),
        help_text=_('Payment method code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_tt',
        null=True,
        blank=True,
    )
    ten_tt = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Tên thanh toán'),
        help_text=_('Payment method name'),
    )
    ghi_chu = models.TextField(
        blank=True,
        verbose_name=_('Ghi chú'),
        help_text=_('Notes'),
    )

    # Reference ID
    id_tt = models.IntegerField(
        verbose_name=_('ID thanh toán'),
        help_text=_('Payment ID'),
        null=True,
        blank=True,
    )

    # Allocation codes
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã bộ phận'),
        help_text=_('Department code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_bp',
        null=True,
        blank=True,
    )
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vụ việc'),
        help_text=_('Task code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_vv',
        null=True,
        blank=True,
    )
    ma_hd = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã hợp đồng'),
        help_text=_('Contract code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_hd',
        null=True,
        blank=True,
    )
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã đối tượng thuế'),
        help_text=_('Tax object code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_dtt',
        null=True,
        blank=True,
    )
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khu vực'),
        help_text=_('Area code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_ku',
        null=True,
        blank=True,
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã phí'),
        help_text=_('Fee code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_phi',
        null=True,
        blank=True,
    )
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã sản phẩm'),
        help_text=_('Product code'),
        related_name='thue_hoa_don_nhap_mua_xuat_thang_sp',
        null=True,
        blank=True,
    )

    # ma_lsx = models.ForeignKey(
    #     'django_ledger.LenhSanXuatModel',
    #     on_delete=models.CASCADE,
    #     verbose_name=_('Mã lệnh sản xuất'),
    #     help_text=_('Production order code'),
    #     related_name='thue_hoa_don_nhap_mua_xuat_thang_lsx',
    #     null=True,
    #     blank=True,
    # )
    ma_lsx = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_('Mã lệnh sản xuất'),
        help_text=_('Production order code'),
    )

    objects = ThueHoaDonNhapMuaXuatThangModelManager.from_queryset(
        ThueHoaDonNhapMuaXuatThangModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Thuế Hóa Đơn Nhập Mua Xuất Tháng')
        verbose_name_plural = _('Thuế Hóa Đơn Nhập Mua Xuất Tháng')
        ordering = ['line']
        indexes = [
            models.Index(fields=['hoa_don']),
            models.Index(fields=['ma_thue']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['ma_so_thue']),
            models.Index(fields=['line']),
            models.Index(fields=['created']),
            models.Index(fields=['updated']),
        ]

    def __str__(self):
        return f"{self.hoa_don.so_ct} - Thuế {self.ma_thue}"

    def save(self, *args, **kwargs):  # noqa: C901
        # Auto-calculate tax amount if not provided
        if not self.t_thue_nt and self.t_tien_nt and self.thue_suat:
            self.t_thue_nt = self.t_tien_nt * (self.thue_suat / 100)
        # Auto-calculate tax amount in local currency if not provided
        if not self.t_thue and self.t_tien and self.thue_suat:
            self.t_thue = self.t_tien * (self.thue_suat / 100)
        super().save(*args, **kwargs)


class ThueHoaDonNhapMuaXuatThangModel(ThueHoaDonNhapMuaXuatThangModelAbstract):
    """
    Base ThueHoaDonNhapMuaXuatThangModel from Abstract.
    """

    class Meta(ThueHoaDonNhapMuaXuatThangModelAbstract.Meta):
        abstract = False
        db_table = "thue_hoa_don_nhap_mua_xuat_thang"
