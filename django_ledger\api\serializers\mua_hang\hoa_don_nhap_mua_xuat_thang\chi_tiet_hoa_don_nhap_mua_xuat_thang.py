"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietHoaDonNhapMuaXuatThang Serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_nhap_mua_xuat_thang import (  # noqa: F401
    ChiTietHoaDonNhapMuaXuatThangModel,
)


class ChiTietHoaDonNhapMuaXuatThangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonNhapMuaXuatThangModel.
    Used for read operations and API responses.
    """

    # Related object data
    hoa_don_data = serializers.SerializerMethodField()
    ma_vt_data = serializers.SerializerMethodField()
    dvt_data = serializers.SerializerMethodField()
    ma_kho_data = serializers.SerializerMethodField()
    ma_lo_data = serializers.SerializerMethodField()
    ma_vi_tri_data = serializers.SerializerMethodField()
    ma_thue_data = serializers.SerializerMethodField()
    tk_thue_data = serializers.SerializerMethodField()
    tk_vt_data = serializers.SerializerMethodField()
    ma_nx_data = serializers.SerializerMethodField()
    tk_cpxt_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_lsx_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietHoaDonNhapMuaXuatThangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]

    def get_hoa_don_data(self, obj):  # noqa: C901
        """Get invoice data."""
        if obj.hoa_don:
            # Avoid circular import by importing here
            from django_ledger.api.serializers.mua_hang.hoa_don_nhap_mua_xuat_thang.hoa_don_nhap_mua_xuat_thang import (
                HoaDonNhapMuaXuatThangSerializer,
            )

            return HoaDonNhapMuaXuatThangSerializer(obj.hoa_don).data
        return None

    def get_ma_vt_data(self, obj):  # noqa: C901
        """Get material data."""
        if obj.ma_vt:
            from django_ledger.api.serializers.erp import (
                VatTuSanPhamHangHoaModelSerializer,
            )

            return VatTuSanPhamHangHoaModelSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """Get unit of measure data."""
        if obj.dvt:
            from django_ledger.api.serializers.erp import (
                VatTuSanPhamDonViTinhModelSerializer,
            )

            return VatTuSanPhamDonViTinhModelSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        """Get warehouse data."""
        if obj.ma_kho:
            from django_ledger.api.serializers.erp import KhoHangModelSerializer

            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_lo_data(self, obj):  # noqa: C901
        """Get lot data."""
        if obj.ma_lo:
            # Assuming LoModel serializer exists
            return {"uuid": str(obj.ma_lo.uuid), "ma_lo": obj.ma_lo.ma_lo}
        return None

    def get_ma_vi_tri_data(self, obj):  # noqa: C901
        """Get location data."""
        if obj.ma_vi_tri:
            # Assuming ViTriKhoHangModel serializer exists
            return {
                "uuid": str(obj.ma_vi_tri.uuid),
                "ma_vi_tri": obj.ma_vi_tri.ma_vi_tri,
            }
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """Get tax data."""
        if obj.ma_thue:
            from django_ledger.api.serializers.tax import TaxModelSerializer

            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_tk_thue_data(self, obj):  # noqa: C901
        """Get tax account data."""
        if obj.tk_thue:
            from django_ledger.api.serializers.accounts import AccountModelSerializer

            return AccountModelSerializer(obj.tk_thue).data
        return None

    def get_tk_vt_data(self, obj):  # noqa: C901
        """Get material account data."""
        if obj.tk_vt:
            from django_ledger.api.serializers.accounts import AccountModelSerializer

            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_ma_nx_data(self, obj):  # noqa: C901
        """Get import/export data."""
        if obj.ma_nx:
            # Assuming NhapXuatModel serializer exists
            return {"uuid": str(obj.ma_nx.uuid), "ma_nx": obj.ma_nx.ma_nx}
        return None

    def get_tk_cpxt_data(self, obj):  # noqa: C901
        """Get export cost account data."""
        if obj.tk_cpxt:
            from django_ledger.api.serializers.accounts import AccountModelSerializer

            return AccountModelSerializer(obj.tk_cpxt).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """Get department data."""
        if obj.ma_bp:
            from django_ledger.api.serializers.erp import BoPhanModelSerializer

            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """Get task data."""
        if obj.ma_vv:
            # Assuming VuViecModel serializer exists
            return {"uuid": str(obj.ma_vv.uuid), "ma_vv": obj.ma_vv.ma_vv}
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """Get contract data."""
        if obj.ma_hd:
            # Assuming HopDongModel serializer exists
            return {"uuid": str(obj.ma_hd.uuid), "ma_hd": obj.ma_hd.ma_hd}
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """Get agreement data."""
        if obj.ma_ku:
            from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (
                KheUocModelSerializer,
            )

            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """Get fee data."""
        if obj.ma_phi:
            from django_ledger.api.serializers.erp import PhiModelSerializer

            return PhiModelSerializer(obj.ma_phi).data
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """Get payment batch data."""
        if obj.ma_dtt:
            from django_ledger.api.serializers.erp import DotThanhToanModelSerializer

            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """Get product data."""
        if obj.ma_sp:
            from django_ledger.api.serializers.erp import (
                VatTuSanPhamHangHoaModelSerializer,
            )

            return VatTuSanPhamHangHoaModelSerializer(obj.ma_sp).data
        return None

    def get_ma_lsx_data(self, obj):  # noqa: C901
        """Get production order data."""
        if obj.ma_lsx:
            # ma_lsx is now a CharField, not a ForeignKey
            return {"ma_lsx": obj.ma_lsx}
        return None


class ChiTietHoaDonNhapMuaXuatThangCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonNhapMuaXuatThangModel.
    Used for create and update operations.
    """

    class Meta:
        model = ChiTietHoaDonNhapMuaXuatThangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]
