# Generated by Django 4.2.10 on 2025-07-02 10:06

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0134_phieuxuatkhomodel_ledger_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='HoaDonNhapMuaXuatThangModel',
            fields=[
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, null=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('ma_ngv', models.CharField(blank=True, help_text='Employee code', max_length=10, null=True, verbose_name='Mã nhân viên')),
                ('so_ct0', models.Char<PERSON>ield(help_text='Document number 0', max_length=20, verbose_name='Số chứng từ 0')),
                ('ngay_ct0', models.DateField(help_text='Document date 0', verbose_name='Ngày chứng từ 0')),
                ('so_ct2', models.CharField(help_text='Document number 2', max_length=20, verbose_name='Số chứng từ 2')),
                ('dien_giai', models.TextField(help_text='Description', verbose_name='Diễn giải')),
                ('id', models.IntegerField(blank=True, help_text='Original ID', null=True, verbose_name='ID gốc')),
                ('xdatetime2', models.CharField(blank=True, help_text='Extended datetime 2', max_length=50, verbose_name='Thời gian X')),
                ('ty_gia', models.DecimalField(decimal_places=6, help_text='Exchange rate', max_digits=15, verbose_name='Tỷ giá')),
                ('status', models.CharField(help_text='Status', max_length=10, verbose_name='Trạng thái')),
                ('transfer_yn', models.BooleanField(default=False, help_text='Transfer flag', verbose_name='Chuyển khoản')),
                ('ma_gd', models.CharField(blank=True, help_text='Transaction code', max_length=10, verbose_name='Mã giao dịch')),
                ('t_cp_nt', models.DecimalField(decimal_places=2, default=0, help_text='Total cost in foreign currency', max_digits=15, verbose_name='Tổng chi phí ngoại tệ')),
                ('t_cp', models.DecimalField(decimal_places=2, default=0, help_text='Total cost', max_digits=15, verbose_name='Tổng chi phí')),
                ('t_tien_nt0', models.DecimalField(decimal_places=2, help_text='Total amount in foreign currency 0', max_digits=15, verbose_name='Tổng tiền ngoại tệ 0')),
                ('t_tien0', models.DecimalField(decimal_places=2, help_text='Total amount 0', max_digits=15, verbose_name='Tổng tiền 0')),
                ('t_thue_nt', models.DecimalField(decimal_places=2, default=0, help_text='Total tax in foreign currency', max_digits=15, verbose_name='Tổng thuế ngoại tệ')),
                ('t_thue', models.DecimalField(decimal_places=2, default=0, help_text='Total tax', max_digits=15, verbose_name='Tổng thuế')),
                ('t_tien_nt', models.DecimalField(decimal_places=2, help_text='Total amount in foreign currency', max_digits=15, verbose_name='Tổng tiền ngoại tệ')),
                ('t_tien', models.DecimalField(decimal_places=2, help_text='Total amount', max_digits=15, verbose_name='Tổng tiền')),
                ('t_so_luong', models.DecimalField(decimal_places=3, help_text='Total quantity', max_digits=15, verbose_name='Tổng số lượng')),
                ('t_tt_nt', models.DecimalField(decimal_places=2, help_text='Total payment in foreign currency', max_digits=15, verbose_name='Tổng thanh toán ngoại tệ')),
                ('t_tt', models.DecimalField(decimal_places=2, help_text='Total payment', max_digits=15, verbose_name='Tổng thanh toán')),
                ('xfile', models.CharField(blank=True, help_text='Attached file', max_length=255, verbose_name='File đính kèm')),
                ('chung_tu', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_chung_tu', to='django_ledger.chungtu', verbose_name='Document Reference')),
                ('chung_tu_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_documents', to='django_ledger.chungtuitemmodel', verbose_name='Document Item')),
                ('entity_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hoa_don_nhap_mua_xuat_thang', to='django_ledger.entitymodel', verbose_name='Entity Model')),
                ('ledger', models.OneToOneField(blank=True, help_text='Sổ cái được tạo cho hóa đơn này', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='hoa_don_nhap_mua_xuat_thang', to='django_ledger.ledgermodel', verbose_name='Sổ cái')),
                ('ma_kh', models.ForeignKey(blank=True, help_text='Customer code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hoa_don_nhap_mua_xuat_thang_kh', to='django_ledger.customermodel', verbose_name='Mã khách hàng')),
                ('ma_kh_x', models.ForeignKey(blank=True, help_text='Customer X code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hoa_don_nhap_mua_xuat_thang_kh_x', to='django_ledger.customermodel', verbose_name='Mã khách hàng X')),
                ('ma_nt', models.ForeignKey(help_text='Currency code', on_delete=django.db.models.deletion.CASCADE, related_name='hoa_don_nhap_mua_xuat_thang_nt', to='django_ledger.ngoaitemodel', verbose_name='Mã ngoại tệ')),
                ('ma_tt', models.ForeignKey(blank=True, help_text='Payment method code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hoa_don_nhap_mua_xuat_thang_tt', to='django_ledger.hanthanhtoanmodel', verbose_name='Mã thanh toán')),
                ('tk', models.ForeignKey(blank=True, help_text='Account', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hoa_don_nhap_mua_xuat_thang_tk', to='django_ledger.accountmodel', verbose_name='Tài khoản')),
                ('unit_id', models.ForeignKey(blank=True, help_text='Unit ID', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hoa_don_nhap_mua_xuat_thang_unit', to='django_ledger.entityunitmodel', verbose_name='ID đơn vị')),
            ],
            options={
                'verbose_name': 'Hóa Đơn Nhập Mua Xuất Tháng',
                'verbose_name_plural': 'Hóa Đơn Nhập Mua Xuất Tháng',
                'db_table': 'hoa_don_nhap_mua_xuat_thang',
                'ordering': ['-created'],
                'abstract': False,
            },
        ),
        migrations.AlterField(
            model_name='journalentrymodel',
            name='journal_type',
            field=models.CharField(blank=True, choices=[('CONGNO', 'Bút toán công nợ'), ('DT0CK', 'Doanh thu không có chiết khấu'), ('DTCK', 'Doanh thu có chiết khấu'), ('THUE', 'Bút toán thuế'), ('THUHD', 'Thu từ hóa đơn'), ('THUKH', 'Thu từ khách hàng'), ('CHIHD', 'Chi hóa đơn'), ('CHINCC', 'Chi nhà cung cấp'), ('CHITHUE', 'Chi thuế'), ('TONKHO', 'Bút toán tồn kho'), ('NHAP', 'Bút toán nhập kho'), ('XUAT', 'Bút toán xuất kho'), ('CHIPHIXT', 'Chi phí xuất thẳng'), ('KHAC', 'Loại khác'), ('CK', 'Chiết khấu')], help_text='Phân loại theo tính chất nghiệp vụ kế toán', max_length=20, null=True, verbose_name='Loại bút toán'),
        ),
        migrations.CreateModel(
            name='ChiTietHoaDonNhapMuaXuatThangModel',
            fields=[
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, null=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('id_goc', models.IntegerField(blank=True, help_text='Original ID', null=True, verbose_name='ID gốc')),
                ('line', models.IntegerField(help_text='Line number', verbose_name='Số dòng')),
                ('ten_dvt', models.CharField(blank=True, help_text='Unit name', max_length=100, verbose_name='Tên đơn vị tính')),
                ('ten_kho', models.CharField(blank=True, help_text='Warehouse name', max_length=200, verbose_name='Tên kho')),
                ('ten_lo', models.CharField(blank=True, help_text='Lot name', max_length=200, verbose_name='Tên lô')),
                ('lo_yn', models.IntegerField(default=0, help_text='Lot management flag', verbose_name='Quản lý theo lô')),
                ('ten_vi_tri', models.CharField(blank=True, help_text='Location name', max_length=200, verbose_name='Tên vị trí')),
                ('vi_tri_yn', models.IntegerField(default=0, help_text='Location management flag', verbose_name='Quản lý theo vị trí')),
                ('he_so', models.DecimalField(decimal_places=6, help_text='Conversion factor', max_digits=15, verbose_name='Hệ số')),
                ('the_tich', models.DecimalField(decimal_places=6, help_text='Volume', max_digits=15, verbose_name='Thể tích')),
                ('khoi_luong', models.DecimalField(decimal_places=6, help_text='Weight', max_digits=15, verbose_name='Khối lượng')),
                ('so_luong', models.DecimalField(decimal_places=3, help_text='Quantity', max_digits=15, verbose_name='Số lượng')),
                ('gia_nt0', models.DecimalField(decimal_places=2, help_text='Price in foreign currency 0', max_digits=15, verbose_name='Giá ngoại tệ 0')),
                ('tien_nt0', models.DecimalField(decimal_places=2, help_text='Amount in foreign currency 0', max_digits=15, verbose_name='Tiền ngoại tệ 0')),
                ('cp_nt', models.DecimalField(decimal_places=2, help_text='Cost in foreign currency', max_digits=15, verbose_name='Chi phí ngoại tệ')),
                ('ten_thue', models.CharField(blank=True, help_text='Tax name', max_length=100, verbose_name='Tên thuế')),
                ('thue_suat', models.DecimalField(decimal_places=2, help_text='Tax rate', max_digits=5, verbose_name='Thuế suất')),
                ('ten_tk_thue', models.CharField(blank=True, help_text='Tax account name', max_length=200, verbose_name='Tên tài khoản thuế')),
                ('thue_nt', models.DecimalField(decimal_places=2, help_text='Tax in foreign currency', max_digits=15, verbose_name='Thuế ngoại tệ')),
                ('ten_tk_vt', models.CharField(blank=True, help_text='Material account name', max_length=200, verbose_name='Tên tài khoản vật tư')),
                ('px_dd', models.IntegerField(help_text='Export identification', verbose_name='Phiếu xuất định danh')),
                ('ten_tk_cpxt', models.CharField(blank=True, help_text='Export cost account name', max_length=200, verbose_name='Tên tài khoản chi phí xuất')),
                ('gia0', models.DecimalField(decimal_places=2, help_text='Price 0', max_digits=15, verbose_name='Giá 0')),
                ('tien0', models.DecimalField(decimal_places=2, help_text='Amount 0', max_digits=15, verbose_name='Tiền 0')),
                ('cp', models.DecimalField(decimal_places=2, help_text='Cost', max_digits=15, verbose_name='Chi phí')),
                ('thue', models.DecimalField(decimal_places=2, help_text='Tax', max_digits=15, verbose_name='Thuế')),
                ('ma_lsx', models.CharField(blank=True, help_text='Production order code', max_length=20, null=True, verbose_name='Mã lệnh sản xuất')),
                ('tien_nt', models.DecimalField(decimal_places=2, help_text='Amount in foreign currency', max_digits=15, verbose_name='Tiền ngoại tệ')),
                ('tien', models.DecimalField(decimal_places=2, help_text='Amount', max_digits=15, verbose_name='Tiền')),
                ('tt_nt', models.DecimalField(decimal_places=2, help_text='Payment in foreign currency', max_digits=15, verbose_name='Thanh toán ngoại tệ')),
                ('tt', models.DecimalField(decimal_places=2, help_text='Payment', max_digits=15, verbose_name='Thanh toán')),
                ('gia_nt', models.DecimalField(decimal_places=2, help_text='Price in foreign currency', max_digits=15, verbose_name='Giá ngoại tệ')),
                ('gia', models.DecimalField(decimal_places=2, help_text='Price', max_digits=15, verbose_name='Giá')),
                ('id_pn', models.IntegerField(blank=True, help_text='Receipt ID', null=True, verbose_name='ID phiếu nhập')),
                ('line_pn', models.IntegerField(blank=True, help_text='Receipt line', null=True, verbose_name='Dòng phiếu nhập')),
                ('id_dh5', models.IntegerField(blank=True, help_text='Order ID 5', null=True, verbose_name='ID đơn hàng 5')),
                ('id_dh6', models.IntegerField(blank=True, help_text='Order ID 6', null=True, verbose_name='ID đơn hàng 6')),
                ('line_dh', models.IntegerField(blank=True, help_text='Order line', null=True, verbose_name='Dòng đơn hàng')),
                ('dvt', models.ForeignKey(blank=True, help_text='Unit of measure', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_dvt', to='django_ledger.donvitinhmodel', verbose_name='Đơn vị tính')),
                ('hoa_don', models.ForeignKey(help_text='Main invoice reference', on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet', to='django_ledger.hoadonnhapmuaxuatthangmodel', verbose_name='Hóa đơn')),
                ('ma_bp', models.ForeignKey(blank=True, help_text='Department code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_bp', to='django_ledger.bophanmodel', verbose_name='Mã bộ phận')),
                ('ma_dtt', models.ForeignKey(blank=True, help_text='Payment batch code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_dtt', to='django_ledger.dotthanhtoanmodel', verbose_name='Mã đợt thanh toán')),
                ('ma_hd', models.ForeignKey(blank=True, help_text='Contract code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_hd', to='django_ledger.contractmodel', verbose_name='Mã hợp đồng')),
                ('ma_kho', models.ForeignKey(blank=True, help_text='Warehouse code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_kho', to='django_ledger.khohangmodel', verbose_name='Mã kho')),
                ('ma_ku', models.ForeignKey(blank=True, help_text='Contract code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_ku', to='django_ledger.kheuocmodel', verbose_name='Khế ước')),
                ('ma_lo', models.ForeignKey(blank=True, help_text='Lot code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_lo', to='django_ledger.lomodel', verbose_name='Mã lô')),
                ('ma_nx', models.ForeignKey(blank=True, help_text='Import/Export code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_nx', to='django_ledger.nhapxuatmodel', verbose_name='Mã nhập xuất')),
                ('ma_phi', models.ForeignKey(blank=True, help_text='Fee code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_phi', to='django_ledger.phimodel', verbose_name='Mã phí')),
                ('ma_sp', models.ForeignKey(blank=True, help_text='Product code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_sp', to='django_ledger.vattumodel', verbose_name='Mã sản phẩm')),
                ('ma_thue', models.ForeignKey(blank=True, help_text='Tax code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_thue', to='django_ledger.taxmodel', verbose_name='Mã thuế')),
                ('ma_vi_tri', models.ForeignKey(blank=True, help_text='Location code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_vi_tri', to='django_ledger.vitrikhohangmodel', verbose_name='Mã vị trí')),
                ('ma_vt', models.ForeignKey(blank=True, help_text='Material code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_vt', to='django_ledger.vattumodel', verbose_name='Mã vật tư')),
                ('ma_vv', models.ForeignKey(blank=True, help_text='Task code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_vv', to='django_ledger.vuviecmodel', verbose_name='Mã vụ việc')),
                ('tk_cpxt', models.ForeignKey(blank=True, help_text='Export cost account', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_tk_cpxt', to='django_ledger.accountmodel', verbose_name='Tài khoản chi phí xuất')),
                ('tk_thue', models.ForeignKey(blank=True, help_text='Tax account', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_tk_thue', to='django_ledger.accountmodel', verbose_name='Tài khoản thuế')),
                ('tk_vt', models.ForeignKey(blank=True, help_text='Material account', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_tiet_hoa_don_nhap_mua_xuat_thang_tk_vt', to='django_ledger.accountmodel', verbose_name='Tài khoản vật tư')),
            ],
            options={
                'verbose_name': 'Chi Tiết Hóa Đơn Nhập Mua Xuất Tháng',
                'verbose_name_plural': 'Chi Tiết Hóa Đơn Nhập Mua Xuất Tháng',
                'db_table': 'chi_tiet_hoa_don_nhap_mua_xuat_thang',
                'ordering': ['line'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ChiPhiHoaDonNhapMuaXuatThangModel',
            fields=[
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, null=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('id_goc', models.IntegerField(blank=True, help_text='Original ID', null=True, verbose_name='ID gốc')),
                ('line', models.IntegerField(help_text='Line number', verbose_name='Số dòng')),
                ('tien_cp_nt', models.DecimalField(decimal_places=2, help_text='Cost amount in foreign currency', max_digits=15, verbose_name='Tiền chi phí ngoại tệ')),
                ('tien_cp', models.DecimalField(decimal_places=2, help_text='Cost amount', max_digits=15, verbose_name='Tiền chi phí')),
                ('hoa_don', models.ForeignKey(help_text='Main invoice reference', on_delete=django.db.models.deletion.CASCADE, related_name='chi_phi', to='django_ledger.hoadonnhapmuaxuatthangmodel', verbose_name='Hóa đơn')),
                ('ma_cp', models.ForeignKey(blank=True, help_text='Cost code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_phi_hoa_don_nhap_mua_xuat_thang', to='django_ledger.chiphimodel', verbose_name='Mã chi phí')),
            ],
            options={
                'verbose_name': 'Chi Phí Hóa Đơn Nhập Mua Xuất Tháng',
                'verbose_name_plural': 'Chi Phí Hóa Đơn Nhập Mua Xuất Tháng',
                'db_table': 'chi_phi_hoa_don_nhap_mua_xuat_thang',
                'ordering': ['line'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ChiPhiChiTietHoaDonNhapMuaXuatThangModel',
            fields=[
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, null=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('id_goc', models.IntegerField(blank=True, help_text='Original ID', null=True, verbose_name='ID gốc')),
                ('line', models.IntegerField(help_text='Line number', verbose_name='Số dòng')),
                ('tien_cp_nt', models.DecimalField(decimal_places=2, help_text='Cost amount in foreign currency', max_digits=15, verbose_name='Tiền chi phí ngoại tệ')),
                ('tien_cp', models.DecimalField(decimal_places=2, help_text='Cost amount', max_digits=15, verbose_name='Tiền chi phí')),
                ('line_vt', models.IntegerField(help_text='Material line number', verbose_name='Dòng vật tư')),
                ('hoa_don', models.ForeignKey(help_text='Main invoice reference', on_delete=django.db.models.deletion.CASCADE, related_name='chi_phi_vat_tu', to='django_ledger.hoadonnhapmuaxuatthangmodel', verbose_name='Hóa đơn')),
                ('ma_cp', models.ForeignKey(blank=True, help_text='Cost code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_phi_chi_tiet_hoa_don_nhap_mua_xuat_thang', to='django_ledger.chiphimodel', verbose_name='Mã chi phí')),
                ('ma_vt', models.ForeignKey(blank=True, help_text='Material code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chi_phi_chi_tiet_hoa_don_nhap_mua_xuat_thang', to='django_ledger.vattumodel', verbose_name='Mã vật tư')),
            ],
            options={
                'verbose_name': 'Chi Phí Vật Tư Hóa Đơn Nhập Mua Xuất Tháng',
                'verbose_name_plural': 'Chi Phí Vật Tư Hóa Đơn Nhập Mua Xuất Tháng',
                'db_table': 'chi_phi_chi_tiet_hoa_don_nhap_mua_xuat_thang',
                'ordering': ['line'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ThueHoaDonNhapMuaXuatThangModel',
            fields=[
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, null=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('id_goc', models.IntegerField(blank=True, help_text='Original ID', null=True, verbose_name='ID gốc')),
                ('line', models.IntegerField(help_text='Line number', verbose_name='Số dòng')),
                ('ten_thue', models.CharField(blank=True, help_text='Tax name', max_length=100, verbose_name='Tên thuế')),
                ('thue_suat', models.DecimalField(decimal_places=2, help_text='Tax rate', max_digits=5, verbose_name='Thuế suất')),
                ('so_ct0', models.CharField(help_text='Document number 0', max_length=20, verbose_name='Số chứng từ 0')),
                ('so_ct2', models.CharField(help_text='Document number 2', max_length=20, verbose_name='Số chứng từ 2')),
                ('ngay_ct0', models.DateTimeField(help_text='Document date 0', verbose_name='Ngày chứng từ 0')),
                ('ma_mau_ct', models.CharField(blank=True, help_text='Document template code', max_length=20, verbose_name='Mã mẫu chứng từ')),
                ('ma_mau_bc', models.CharField(help_text='Report template code', max_length=20, verbose_name='Mã mẫu báo cáo')),
                ('ma_tc_thue', models.CharField(help_text='Tax property code', max_length=20, verbose_name='Mã tờ chính thuế')),
                ('ten_kh_thue', models.CharField(blank=True, help_text='Tax customer name', max_length=200, verbose_name='Tên khách hàng thuế')),
                ('dia_chi', models.TextField(help_text='Address', verbose_name='Địa chỉ')),
                ('ma_so_thue', models.CharField(help_text='Tax code', max_length=50, verbose_name='Mã số thuế')),
                ('ten_vt_thue', models.CharField(blank=True, help_text='Tax material name', max_length=200, verbose_name='Tên vật tư thuế')),
                ('t_tien_nt', models.DecimalField(decimal_places=2, help_text='Total amount in foreign currency', max_digits=15, verbose_name='Tổng tiền ngoại tệ')),
                ('t_tien', models.DecimalField(decimal_places=2, help_text='Total amount', max_digits=15, verbose_name='Tổng tiền')),
                ('ten_tk_thue_no', models.CharField(blank=True, help_text='Tax debit account name', max_length=200, verbose_name='Tên tài khoản thuế nợ')),
                ('ten_tk_du', models.CharField(blank=True, help_text='Balance account name', max_length=200, verbose_name='Tên tài khoản dư')),
                ('t_thue_nt', models.DecimalField(decimal_places=2, help_text='Total tax in foreign currency', max_digits=15, verbose_name='Tổng thuế ngoại tệ')),
                ('t_thue', models.DecimalField(decimal_places=2, help_text='Total tax', max_digits=15, verbose_name='Tổng thuế')),
                ('ten_kh9', models.CharField(blank=True, help_text='Customer name 9', max_length=200, verbose_name='Tên khách hàng 9')),
                ('ten_tt', models.CharField(blank=True, help_text='Payment method name', max_length=200, verbose_name='Tên thanh toán')),
                ('ghi_chu', models.TextField(blank=True, help_text='Notes', verbose_name='Ghi chú')),
                ('id_tt', models.IntegerField(blank=True, help_text='Payment ID', null=True, verbose_name='ID thanh toán')),
                ('ma_lsx', models.CharField(blank=True, help_text='Production order code', max_length=20, verbose_name='Mã lệnh sản xuất')),
                ('hoa_don', models.ForeignKey(help_text='Main invoice reference', on_delete=django.db.models.deletion.CASCADE, related_name='thue', to='django_ledger.hoadonnhapmuaxuatthangmodel', verbose_name='Hóa đơn')),
                ('ma_bp', models.ForeignKey(blank=True, help_text='Department code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_bp', to='django_ledger.bophanmodel', verbose_name='Mã bộ phận')),
                ('ma_dtt', models.ForeignKey(blank=True, help_text='Tax object code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_dtt', to='django_ledger.dotthanhtoanmodel', verbose_name='Mã đối tượng thuế')),
                ('ma_hd', models.ForeignKey(blank=True, help_text='Contract code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_hd', to='django_ledger.taxmodel', verbose_name='Mã hợp đồng')),
                ('ma_kh', models.ForeignKey(blank=True, help_text='Customer code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_kh', to='django_ledger.customermodel', verbose_name='Mã khách hàng')),
                ('ma_kh9', models.ForeignKey(blank=True, help_text='Customer code 9', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_kh9', to='django_ledger.customermodel', verbose_name='Mã khách hàng 9')),
                ('ma_ku', models.ForeignKey(blank=True, help_text='Area code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_ku', to='django_ledger.kheuocmodel', verbose_name='Mã khu vực')),
                ('ma_phi', models.ForeignKey(blank=True, help_text='Fee code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_phi', to='django_ledger.phimodel', verbose_name='Mã phí')),
                ('ma_sp', models.ForeignKey(blank=True, help_text='Product code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_sp', to='django_ledger.vattumodel', verbose_name='Mã sản phẩm')),
                ('ma_thue', models.ForeignKey(blank=True, help_text='Tax code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang', to='django_ledger.taxmodel', verbose_name='Mã thuế')),
                ('ma_tt', models.ForeignKey(blank=True, help_text='Payment method code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_tt', to='django_ledger.hanthanhtoanmodel', verbose_name='Mã thanh toán')),
                ('ma_vv', models.ForeignKey(blank=True, help_text='Task code', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_vv', to='django_ledger.vuviecmodel', verbose_name='Mã vụ việc')),
                ('tk_du', models.ForeignKey(blank=True, help_text='Balance account', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_tk_du', to='django_ledger.accountmodel', verbose_name='Tài khoản dư')),
                ('tk_thue_no', models.ForeignKey(blank=True, help_text='Tax debit account', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_hoa_don_nhap_mua_xuat_thang_tk_no', to='django_ledger.accountmodel', verbose_name='Tài khoản thuế nợ')),
            ],
            options={
                'verbose_name': 'Thuế Hóa Đơn Nhập Mua Xuất Tháng',
                'verbose_name_plural': 'Thuế Hóa Đơn Nhập Mua Xuất Tháng',
                'db_table': 'thue_hoa_don_nhap_mua_xuat_thang',
                'ordering': ['line'],
                'abstract': False,
                'indexes': [models.Index(fields=['hoa_don'], name='thue_hoa_do_hoa_don_970515_idx'), models.Index(fields=['ma_thue'], name='thue_hoa_do_ma_thue_067a6c_idx'), models.Index(fields=['ma_kh'], name='thue_hoa_do_ma_kh_i_037ba3_idx'), models.Index(fields=['ma_so_thue'], name='thue_hoa_do_ma_so_t_ed3741_idx'), models.Index(fields=['line'], name='thue_hoa_do_line_9a7407_idx'), models.Index(fields=['created'], name='thue_hoa_do_created_e35b23_idx'), models.Index(fields=['updated'], name='thue_hoa_do_updated_c24051_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='hoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['entity_model'], name='hoa_don_nha_entity__3d07d5_idx'),
        ),
        migrations.AddIndex(
            model_name='hoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['ledger'], name='hoa_don_nha_ledger__574ccd_idx'),
        ),
        migrations.AddIndex(
            model_name='hoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['ma_kh'], name='hoa_don_nha_ma_kh_i_c266e6_idx'),
        ),
        migrations.AddIndex(
            model_name='hoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['ma_nt'], name='hoa_don_nha_ma_nt_i_a737f9_idx'),
        ),
        migrations.AddIndex(
            model_name='hoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['status'], name='hoa_don_nha_status_3bd02c_idx'),
        ),
        migrations.AddIndex(
            model_name='hoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['created'], name='hoa_don_nha_created_2ad9d3_idx'),
        ),
        migrations.AddIndex(
            model_name='hoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['updated'], name='hoa_don_nha_updated_4ed494_idx'),
        ),
        migrations.AddIndex(
            model_name='chitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['hoa_don'], name='chi_tiet_ho_hoa_don_3178a1_idx'),
        ),
        migrations.AddIndex(
            model_name='chitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['ma_vt'], name='chi_tiet_ho_ma_vt_i_3fc142_idx'),
        ),
        migrations.AddIndex(
            model_name='chitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['ma_kho'], name='chi_tiet_ho_ma_kho__18f394_idx'),
        ),
        migrations.AddIndex(
            model_name='chitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['line'], name='chi_tiet_ho_line_04cd17_idx'),
        ),
        migrations.AddIndex(
            model_name='chitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['created'], name='chi_tiet_ho_created_99abab_idx'),
        ),
        migrations.AddIndex(
            model_name='chitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['updated'], name='chi_tiet_ho_updated_af12f0_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphihoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['hoa_don'], name='chi_phi_hoa_hoa_don_df2b1c_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphihoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['ma_cp'], name='chi_phi_hoa_ma_cp_i_330bb7_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphihoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['line'], name='chi_phi_hoa_line_0fc63d_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphihoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['created'], name='chi_phi_hoa_created_558f3c_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphihoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['updated'], name='chi_phi_hoa_updated_74a4a9_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphichitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['hoa_don'], name='chi_phi_chi_hoa_don_c3d4ff_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphichitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['ma_cp'], name='chi_phi_chi_ma_cp_i_46a5f7_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphichitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['ma_vt'], name='chi_phi_chi_ma_vt_i_e9fe2e_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphichitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['line'], name='chi_phi_chi_line_50359e_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphichitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['created'], name='chi_phi_chi_created_96ac73_idx'),
        ),
        migrations.AddIndex(
            model_name='chiphichitiethoadonnhapmuaxuatthangmodel',
            index=models.Index(fields=['updated'], name='chi_phi_chi_updated_298e7d_idx'),
        ),
    ]
