"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the HoaDonMuaHangTrongNuocService, which handles business logic
for the HoaDonMuaHangTrongNuocModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocModel,
)
from django_ledger.repositories.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiChiTietHoaDonMuaHangTrongNuocService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiHoaDonMuaHangTrongNuocService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiTietHoaDonMuaHangTrongNuocService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.thue_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ThueHoaDonMuaHangTrongNuocService,
)


class HoaDonMuaHangTrongNuocService(BaseService):
    """
    Service class for HoaDonMuaHangTrongNuocModel.
    Handles business logic for the model.
    """

    def __init__(self):  # noqa: C901
        self.repository = HoaDonMuaHangTrongNuocRepository()
        self.chi_tiet_service = ChiTietHoaDonMuaHangTrongNuocService()
        self.chi_phi_service = ChiPhiHoaDonMuaHangTrongNuocService()
        self.chi_phi_chi_tiet_service = ChiPhiChiTietHoaDonMuaHangTrongNuocService()
        self.thue_service = ThueHoaDonMuaHangTrongNuocService()

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Retrieves a HoaDonMuaHangTrongNuocModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaHangTrongNuocModel to retrieve.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The HoaDonMuaHangTrongNuocModel with the given UUID, or None if not found.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists HoaDonMuaHangTrongNuocModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of HoaDonMuaHangTrongNuocModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> HoaDonMuaHangTrongNuocModel:  # noqa: C901
        """
        Creates a new HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new HoaDonMuaHangTrongNuocModel.

        Returns
        -------
        HoaDonMuaHangTrongNuocModel
            The created HoaDonMuaHangTrongNuocModel instance.
        """
        # Process related data
        chi_tiet_hoa_don = data.pop('chi_tiet_hoa_don', [])
        chi_phi_hoa_don = data.pop('chi_phi_hoa_don', [])
        chi_phi_chi_tiet_hoa_don = data.pop('chi_phi_chi_tiet_hoa_don', [])
        thue_hoa_don = data.pop('thue_hoa_don', [])

        # Create the HoaDonMuaHangTrongNuocModel instance
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # Process related data
        if chi_tiet_hoa_don:
            for chi_tiet_data in chi_tiet_hoa_don:
                self.chi_tiet_service.create(
                    hoa_don_id=instance.uuid, data=chi_tiet_data
                )

        if chi_phi_hoa_don:
            for chi_phi_data in chi_phi_hoa_don:
                self.chi_phi_service.create(
                    hoa_don_id=instance.uuid, data=chi_phi_data
                )

        if chi_phi_chi_tiet_hoa_don:
            for chi_phi_chi_tiet_data in chi_phi_chi_tiet_hoa_don:
                self.chi_phi_chi_tiet_service.create(
                    hoa_don_id=instance.uuid,
                    data=chi_phi_chi_tiet_data,
                )

        if thue_hoa_don:
            for thue_data in thue_hoa_don:
                self.thue_service.create(hoa_don_id=instance.uuid, data=thue_data)

        return instance

    @transaction.atomic
    def create_hoa_don(
        self,
        entity_model,
        hoa_don_data: Dict[str, Any],
        chi_tiet_data=None,
        chi_phi_data=None,
        chi_phi_chi_tiet_data=None,
        thue_data=None,
    ) -> HoaDonMuaHangTrongNuocModel:  # noqa: C901
        """
        Creates a new HoaDonMuaHangTrongNuocModel instance with optional related data.
        Similar to PhieuNhapChiPhiMuaHangService.create_phieu_nhap.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model.
        hoa_don_data : Dict[str, Any]
            The data for the new HoaDonMuaHangTrongNuocModel.
        chi_tiet_data : List[Dict[str, Any]], optional
            The data for related ChiTietHoaDonMuaHangTrongNuocModel instances.
        chi_phi_data : List[Dict[str, Any]], optional
            The data for related ChiPhiHoaDonMuaHangTrongNuocModel instances.
        chi_phi_chi_tiet_data : List[Dict[str, Any]], optional
            The data for related ChiPhiChiTietHoaDonMuaHangTrongNuocModel instances.
        thue_data : List[Dict[str, Any]], optional
            The data for related ThueHoaDonMuaHangTrongNuocModel instances.

        Returns
        -------
        HoaDonMuaHangTrongNuocModel
            The created HoaDonMuaHangTrongNuocModel instance.
        """
        # Set entity_model in hoa_don_data
        hoa_don_data['entity_model'] = entity_model

        # Create the main HoaDonMuaHangTrongNuocModel instance
        instance = self.repository.create_instance(hoa_don_data)

        # Create related data if provided
        if chi_tiet_data:
            for chi_tiet_item in chi_tiet_data:
                chi_tiet_item['hoa_don'] = instance
                self.chi_tiet_service.create_instance(chi_tiet_item)

        if chi_phi_data:
            for chi_phi_item in chi_phi_data:
                chi_phi_item['hoa_don'] = instance
                self.chi_phi_service.create_instance(chi_phi_item)

        if chi_phi_chi_tiet_data:
            for chi_phi_chi_tiet_item in chi_phi_chi_tiet_data:
                chi_phi_chi_tiet_item['hoa_don'] = instance
                self.chi_phi_chi_tiet_service.create_instance(chi_phi_chi_tiet_item)

        if thue_data:
            for thue_item in thue_data:
                thue_item['hoa_don'] = instance
                self.thue_service.create_instance(thue_item)

        return instance

    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Updates a HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaHangTrongNuocModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonMuaHangTrongNuocModel with.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The updated HoaDonMuaHangTrongNuocModel instance, or None if not found.
        """
        return self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaHangTrongNuocModel to delete.

        Returns
        -------
        bool
            True if the HoaDonMuaHangTrongNuocModel was deleted, False otherwise.
        """
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    @transaction.atomic
    def update_with_details(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Updates a HoaDonMuaHangTrongNuocModel instance with related details.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaHangTrongNuocModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonMuaHangTrongNuocModel with, including related details.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The updated HoaDonMuaHangTrongNuocModel instance, or None if not found.
        """
        # Extract related data
        chi_tiet_hoa_don = data.pop('chi_tiet_hoa_don', [])
        chi_phi_hoa_don = data.pop('chi_phi_hoa_don', [])
        chi_phi_chi_tiet_hoa_don = data.pop('chi_phi_chi_tiet_hoa_don', [])
        thue_hoa_don = data.pop('thue_hoa_don', [])

        # Update main instance
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        if not instance:
            return None

        # Update related data
        if chi_tiet_hoa_don:
            # Delete existing chi_tiet and create new ones
            self.chi_tiet_service.delete_by_hoa_don(instance.uuid)
            for chi_tiet_data in chi_tiet_hoa_don:
                self.chi_tiet_service.create(
                    hoa_don_id=instance.uuid, data=chi_tiet_data
                )

        if chi_phi_hoa_don:
            # Delete existing chi_phi and create new ones
            self.chi_phi_service.delete_by_hoa_don(instance.uuid)
            for chi_phi_data in chi_phi_hoa_don:
                self.chi_phi_service.create(
                    hoa_don_id=instance.uuid, data=chi_phi_data
                )

        if chi_phi_chi_tiet_hoa_don:
            # Delete existing chi_phi_chi_tiet and create new ones
            self.chi_phi_chi_tiet_service.delete_by_hoa_don(instance.uuid)
            for chi_phi_chi_tiet_data in chi_phi_chi_tiet_hoa_don:
                self.chi_phi_chi_tiet_service.create(
                    hoa_don_id=instance.uuid,
                    data=chi_phi_chi_tiet_data,
                )

        if thue_hoa_don:
            # Delete existing thue and create new ones
            self.thue_service.delete_by_hoa_don(instance.uuid)
            for thue_data in thue_hoa_don:
                self.thue_service.create(hoa_don_id=instance.uuid, data=thue_data)

        return instance

    def get_by_entity_slug_and_status(
        self, entity_slug: str, status: str
    ) -> QuerySet:  # noqa: C901
        """
        Get HoaDonMuaHangTrongNuocModel instances by entity slug and status.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        status : str
            The status to filter by.

        Returns
        -------
        QuerySet
            A queryset of HoaDonMuaHangTrongNuocModel instances.
        """
        return self.repository.get_by_entity_slug_and_status(entity_slug, status)

    def get_by_so_ct(
        self, entity_slug: str, so_ct: str
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Get a HoaDonMuaHangTrongNuocModel by so_ct (document number).

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        so_ct : str
            The document number.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The HoaDonMuaHangTrongNuocModel with the specified so_ct, or None if not found.
        """
        return self.repository.get_by_so_ct(entity_slug, so_ct)
