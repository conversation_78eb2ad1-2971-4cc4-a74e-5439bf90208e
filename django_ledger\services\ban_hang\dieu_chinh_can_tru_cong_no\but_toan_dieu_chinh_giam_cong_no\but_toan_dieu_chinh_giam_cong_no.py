"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for ButToanDieuChinhGiamCongNo (Debt Reduction Adjustment) model.
"""

from typing import Any, Dict, List, Optional, Tuple, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import models, transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import (  # noqa: F401,
    ButToanDieuChinhGiamCongNoModel,
    ChiTietButToanDieuChinhGiamCongNoModel,
)
from django_ledger.repositories.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import (  # noqa: F401,
    ButToanDieuChinhGiamCongNoRepository,
    ChiTietButToanDieuChinhGiamCongNoRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.utils_new.debt_management.cong_no_creation import CongNoCreation  # noqa: F401,


class ButToanDieuChinhGiamCongNoService(BaseService):
    """
    Service class for handling ButToanDieuChinhGiamCongNo business logic.
    Implements the Service pattern for ButToanDieuChinhGiamCongNo.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Bút toán điều chỉnh giảm công nợ accounting mappings
    SALES_INVOICE_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'THUKH',                # Thu từ khách hàng (ma_ngv=2)
            'debit_account_field': 'tk',            # Tài khoản nợ - DEBIT
            'credit_account_field': 'tk_co',        # Tài khoản có - CREDIT
            'debit_account_source': 'header',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'tien',                 # Thành tiền (detail)
            'detail_source': 'chitiet',             # Related name
            'canCreate': True                       # Default: always create entry
        },
        {
            'journal_type': 'THUHD',                # Thu từ hóa đơn (ma_ngv=1)
            'debit_account_field': 'tk',            # Tài khoản nợ - DEBIT
            'credit_account_field': 'tk_co',        # Tài khoản có - CREDIT
            'debit_account_source': 'header',       # Lấy debit account từ header
            'credit_account_source': 'detail',      # Lấy credit account từ detail
            'amount_field': 'tien',                 # Thành tiền (detail)
            'detail_source': 'chitiet',             # Related name
            'canCreate': True                       # Default: always create entry
        }
    ]

    def __init__(self):  # noqa: C901
        """
        Initialize the service.
        """
        super().__init__()
        self.repository = ButToanDieuChinhGiamCongNoRepository()
        self.child_repository = ChiTietButToanDieuChinhGiamCongNoRepository()
        self.entity_slug = None
        self.user_model = None

        # ✅ ĐƠN GIẢN: Khởi tạo unified accounting service
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(self, but_toan: ButToanDieuChinhGiamCongNoModel) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên ma_ngv và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - THUKH cho ma_ngv=2 (Thu từ khách hàng) với điều kiện tien > 0
        - THUHD cho ma_ngv=1 (Thu từ hóa đơn) với điều kiện tien > 0
        - Kiểm tra status để quyết định có tạo bút toán hay không
        - Support flexible business rules cho different scenarios

        Parameters
        ----------
        but_toan : ButToanDieuChinhGiamCongNoModel
            Bút toán điều chỉnh giảm công nợ để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với journal_type và canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.get_accounting_configuration()

        # ✅ BUSINESS LOGIC: Determine journal type based on ma_ngv
        if hasattr(but_toan, 'ma_ngv'):
            ma_ngv = getattr(but_toan, 'ma_ngv', '1')  # Default to THUHD

            # Filter mappings based on ma_ngv
            filtered_mappings = []
            for mapping in mappings:
                if ma_ngv == '2' and mapping['journal_type'] == 'THUKH':
                    filtered_mappings.append(mapping.copy())
                elif ma_ngv == '1' and mapping['journal_type'] == 'THUHD':
                    filtered_mappings.append(mapping.copy())

            mappings = filtered_mappings if filtered_mappings else [mappings[0].copy()]  # Fallback to first mapping

        # ✅ BUSINESS LOGIC: Check status for entry creation
        if hasattr(but_toan, 'status'):
            status = getattr(but_toan, 'status', '1')  # Default to active status

            for mapping in mappings:
                if status in ['0', 'draft', 'pending']:  # Inactive, draft, pending
                    mapping['canCreate'] = False
                else:
                    mapping['canCreate'] = True
        else:
            # Default: allow creation if no status field
            for mapping in mappings:
                mapping['canCreate'] = True

        # ✅ QUY TẮC NGHIỆP VỤ BỔ SUNG: Kiểm tra điều kiện tien > 0
        # Chỉ kiểm tra validation nếu but_toan đã có ID (đã được save)
        if hasattr(but_toan, 'pk') and but_toan.pk and hasattr(but_toan, 'chitiet'):
            try:
                chi_tiet_list = but_toan.chitiet.all()
                # Chỉ validate nếu có chitiet records
                if chi_tiet_list.exists():
                    has_valid_entries = any(
                        getattr(ct, 'tien', 0) > 0 and
                        getattr(ct, 'tk_co', None) is not None
                        for ct in chi_tiet_list
                    )

                    if not has_valid_entries:
                        for mapping in mappings:
                            mapping['canCreate'] = False
                # Nếu không có chitiet records, vẫn cho phép tạo bút toán
                # (có thể là header-only hoặc chitiet sẽ được tạo sau)
            except Exception as e:
                # Log error nhưng không fail - cho phép tạo bút toán
                pass

        return mappings

    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Lấy cấu hình kế toán cho bút toán điều chỉnh giảm công nợ.

        Returns:
            List[Dict[str, Any]]: Danh sách mapping configuration
        """
        return self.SALES_INVOICE_ACCOUNTING_CONFIG.copy()

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for the model.

        Returns
        -------
        QuerySet
            Base queryset for the model.
        """
        if self.entity_slug:
            return self.repository.list(entity_slug=self.entity_slug)
        return self.repository.model_class.objects.all()

    def get(
        self, entity_slug: str, uuid: str
    ) -> Optional[ButToanDieuChinhGiamCongNoModel]:  # noqa: C901
        """
        Get a ButToanDieuChinhGiamCongNoModel by UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the ButToanDieuChinhGiamCongNoModel.

        Returns
        -------
        Optional[ButToanDieuChinhGiamCongNoModel]
            The ButToanDieuChinhGiamCongNoModel with the specified UUID, or None if not found.  # noqa: E501
        """
        if entity_slug:
            self.entity_slug = entity_slug
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str = None) -> QuerySet:  # noqa: F811,
        """
        List all ButToanDieuChinhGiamCongNoModel instances.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug to filter by, by default None

        Returns
        -------
        QuerySet
            A queryset of ButToanDieuChinhGiamCongNoModel instances.
        """
        if entity_slug:
            self.entity_slug = entity_slug
        # Get data from repository
        return self.get_queryset()

    @transaction.atomic
    def create(  # noqa: C901
        self,
        entity_slug: str,
        data: Dict[str, Any],
        child_data: List[Dict] = None,
    ) -> ButToanDieuChinhGiamCongNoModel:
        """
        Create a new ButToanDieuChinhGiamCongNoModel instance with optional child records.  # noqa: E501

        Parameters
        ----------
        entity_slug : str
            The entity slug to create the instance for
        data : Dict[str, Any]
            The data for the new instance
        child_data : List[Dict], optional
            List of dictionaries containing data for child records, by default None

        Returns
        -------
        ButToanDieuChinhGiamCongNoModel
            The created ButToanDieuChinhGiamCongNoModel instance.
        """
        if entity_slug:
            self.entity_slug = entity_slug

        # Convert UUID strings to model instances for parent record
        processed_data = self.repository.convert_uuids_to_model_instances(data)

        # Create the parent record using repository
        # Repository now handles ChungTu fields properly
        instance = self.repository.create(
            entity_slug=self.entity_slug, data=processed_data
        )

        # Create child records if provided
        if child_data:
            for child in child_data:
                # Convert UUID strings to model instances for child records
                processed_child_data = (
                    self.child_repository.convert_uuids_to_model_instances(child)
                )

                self.child_repository.create(
                    product_unit=instance, data=processed_child_data
                )

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not instance.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="bút toán điều chỉnh giảm công nợ",
                    account_mappings=self._determine_accounting_mappings(instance)
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between ButToan and accounting entries
                raise Exception(f"Failed to create accounting entry for ButToan {instance.so_ct}: {str(e)}") from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance

    @transaction.atomic
    def update(  # noqa: C901
        self,
        uuid: str,
        data: Dict[str, Any],
        child_data: List[Dict] = None,
    ) -> ButToanDieuChinhGiamCongNoModel:
        """
        Update an existing ButToanDieuChinhGiamCongNoModel instance with optional child records.  # noqa: E501

        Parameters
        ----------
        uuid : str
            The UUID of the ButToanDieuChinhGiamCongNoModel to update.
        data : Dict[str, Any]
            The data to update on the instance.
        child_data : List[Dict], optional
            List of dictionaries containing data for child records, by default None

        Returns
        -------
        ButToanDieuChinhGiamCongNoModel
            The updated ButToanDieuChinhGiamCongNoModel instance.
        """
        instance = self.get(entity_slug=self.entity_slug, uuid=uuid)
        # Update the parent record
        instance = self.repository.update(
            entity_slug=self.entity_slug, uuid=uuid, data=data
        )

        # Update child records if provided
        if child_data:
            # Delete existing child records
            existing_children = self.child_repository.list(product_unit_uuid=uuid)
            for child in existing_children:
                self.child_repository.delete(child)

            # Create new child records
            for child in child_data:
                self.child_repository.create(product_unit=instance, data=child)

        # ✅ UNIFIED ACCOUNTING: Cập nhật bút toán kế toán
        try:
            self._cong_no_service.update_document_accounting_entries(
                source_document=instance,
                document_type="bút toán điều chỉnh giảm công nợ",
                account_mappings=self._determine_accounting_mappings(instance)
            )
        except Exception as e:
            # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
            # to maintain data consistency between ButToan and accounting entries
            raise Exception(f"Failed to update accounting entry for ButToan {instance.so_ct}: {str(e)}") from e

        return instance

    @transaction.atomic
    def delete(self, uuid: str) -> None:  # noqa: C901
        """
        Delete a ButToanDieuChinhGiamCongNoModel instance and its child records.

        Parameters
        ----------
        uuid : str
            The UUID of the ButToanDieuChinhGiamCongNoModel to delete.
        """
        # Delete child records
        existing_children = self.child_repository.list(product_unit_uuid=uuid)
        for child in existing_children:
            self.child_repository.delete(child)

        # Delete parent record
        self.repository.delete(entity_slug=self.entity_slug, uuid=uuid)

    def get_child_data(self, uuid: str) -> QuerySet:  # noqa: C901
        """
        Get child records for a ButToanDieuChinhGiamCongNoModel.

        Parameters
        ----------
        uuid : str
            The UUID of the ButToanDieuChinhGiamCongNoModel.

        Returns
        -------
        QuerySet
            A queryset of ChiTietButToanDieuChinhGiamCongNoModel instances.
        """
        return self.child_repository.list(product_unit_uuid=uuid)

    @transaction.atomic
    def create_with_details(  # noqa: C901
        self,
        entity_slug: str,
        data: Dict[str, Any],
        child_data: List[Dict[str, Any]],
    ) -> Tuple[
        ButToanDieuChinhGiamCongNoModel,
        List[ChiTietButToanDieuChinhGiamCongNoModel],
    ]:
        """
        Create a new ButToanDieuChinhGiamCongNo instance with its details

        Parameters
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new ButToanDieuChinhGiamCongNo instance
        child_data : List[Dict[str, Any]]
            The data for the ChiTietButToanDieuChinhGiamCongNo instances

        Returns
        -------
        Tuple[ButToanDieuChinhGiamCongNoModel, List[ChiTietButToanDieuChinhGiamCongNoModel]]  # noqa: E501
            A tuple containing the created ButToanDieuChinhGiamCongNoModel instance and a list of created ChiTietButToanDieuChinhGiamCongNoModel instances  # noqa: E501
        """
        # Create the main instance using the updated create method
        # which handles ChungTu fields properly
        instance = self.create(entity_slug=entity_slug, data=data, child_data=None)

        # Create chi_tiet instances
        chi_tiet_instances = []
        if child_data:
            # Process each chi_tiet item
            for item in child_data:
                # Convert UUID strings to model instances for child records
                processed_item = self.child_repository.convert_uuids_to_model_instances(
                    item
                )

                chi_tiet = self.child_repository.create(
                    product_unit=instance, data=processed_item
                )
                chi_tiet_instances.append(chi_tiet)

        return instance, chi_tiet_instances
