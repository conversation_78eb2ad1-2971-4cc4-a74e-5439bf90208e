"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapKhoModel, which represents the warehouse receipt
in the system.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class PhieuNhapKhoModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the PhieuNhapKhoModel.
    """

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuNhapKhoModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        PhieuNhapKhoModelQueryset
            A QuerySet of PhieuNhapKhoModel with applied filters.
        """
        return self.filter(entity_model__slug=entity_slug)


class PhieuNhapKhoModelManager(Manager):
    """
    A custom defined PhieuNhapKhoModel Manager that will act as an interface to handle the  # noqa: E501
    PhieuNhapKhoModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom PhieuNhapKhoModelQueryset.
        """
        return PhieuNhapKhoModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuNhapKhoModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        PhieuNhapKhoModelQueryset
            A QuerySet of PhieuNhapKhoModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)


class PhieuNhapKhoModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuNhapKhoModel database will inherit from.  # noqa: E501
    The PhieuNhapKhoModel inherits functionality from the following MixIns:

        1. :func:`ChungTuMixIn <django_ledger.models._mixins.chung_tu_mixins.ChungTuMixIn>`
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    entity_model : ForeignKey
        The EntityModel this record belongs to.
    action : CharField
        Action type.
    question_ids : CharField
        Question IDs.
    ma_ngv : CharField
        Assignee code.
    ma_gd : CharField
        Transaction code.
    ma_kh : ForeignKey
        Customer code.
    dien_giai : TextField
        Description.
    unit_id : ForeignKey
        Related organizational unit.
    id_progress : IntegerField
        Progress ID.
    xprogress : CharField
        Progress details.
    xdatetime2 : CharField
        Additional datetime info.
    ma_nt : ForeignKey
        Currency code.
    ty_gia : DecimalField
        Exchange rate.
    status : CharField
        Status.
    transfer_yn : BooleanField
        Transfer status.
    t_so_luong : DecimalField
        Total quantity.
    t_tien_nt : DecimalField
        Total amount in foreign currency.
    t_tien : DecimalField
        Total amount.
    xfile : CharField
        File reference.
    tu_dong_tao : BooleanField
        Whether this receipt was auto-generated from invoice.
    nguon_hoa_don_id : CharField
        Source invoice UUID that generated this receipt.
    ngay_xu_ly : DateTimeField
        When auto-processing occurred.

    Note: Document fields (i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct, chung_tu)
    are inherited from ChungTuMixIn.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        help_text=_("Unique identifier for the record"),
    )
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity'),
        related_name='phieu_nhap_kho',
        help_text=_("Entity this record belongs to"),
    )

    # Basic information
    action = models.CharField(
        max_length=10,
        verbose_name=_('Hành động'),
        help_text=_("Type of action for this receipt"),
    )
    question_ids = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('ID câu hỏi'),
        help_text=_("Related question identifiers"),
    )
    ma_ngv = models.CharField(
        max_length=10,
        verbose_name=_('Mã người giao việc'),
        help_text=_("Person who assigned the task"),
    )
    ma_gd = models.CharField(
        max_length=10,
        verbose_name=_('Mã giao dịch'),
        help_text=_("Transaction identifier"),
    )
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng'),
        related_name='phieu_nhap_kho',
        null=True,
        blank=True,
        help_text=_("Related customer"),
    )
    dien_giai = models.TextField(
        verbose_name=_('Diễn giải'),
        blank=True,
        null=True,
        help_text=_("Detailed description of the receipt"),
    )

    # Progress information
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.CASCADE,
        related_name='phieu_nhap_kho',
        null=True,
        blank=True,
        verbose_name=_('Đơn vị'),
        help_text=_("Related organizational unit"),
    )

    id_progress = models.IntegerField(
        verbose_name=_('ID tiến độ'),
        help_text=_("Progress tracking identifier"),
    )
    xprogress = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Chi tiết tiến độ'),
        help_text=_("Additional progress information"),
    )

    # Document information - inherited from ChungTuMixIn
    # i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct, chung_tu are provided by ChungTuMixIn
    xdatetime2 = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Thông tin thời gian bổ sung'),
        help_text=_("Additional datetime information"),
    )

    # Currency information
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã ngoại tệ'),
        related_name='phieu_nhap_kho',
        null=True,
        help_text=_("Currency used in the transaction"),
    )
    ty_gia = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tỷ giá'),
        help_text=_("Currency exchange rate"),
    )

    # Status information
    status = models.CharField(
        max_length=2,
        verbose_name=_('Trạng thái'),
        help_text=_("Current status of the receipt"),
    )
    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_('Đã chuyển'),
        help_text=_("Whether the receipt has been transferred"),
    )

    # Amount information
    t_so_luong = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng số lượng'),
        help_text=_("Total quantity of items"),
    )
    t_tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ'),
        help_text=_("Total amount in foreign currency"),
    )
    t_tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền'),
        help_text=_("Total amount in local currency"),
    )

    # File information
    xfile = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Tham chiếu tệp'),
        help_text=_("Reference to attached files"),
    )

    # Auto-generation tracking fields
    tu_dong_tao = models.BooleanField(
        default=False,
        verbose_name=_('Tự động tạo'),
        help_text=_("Whether this receipt was auto-generated from invoice"),
    )
    nguon_hoa_don_id = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        verbose_name=_('Nguồn hóa đơn ID'),
        help_text=_("Source invoice UUID that generated this receipt"),
    )

    ngay_xu_ly = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('Ngày xử lý'),
        help_text=_("When auto-processing occurred"),
    )

    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho phiếu nhập kho này"),
        related_name="phieu_nhap_kho",
    )

    objects = PhieuNhapKhoModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Phiếu nhập kho')
        verbose_name_plural = _('Phiếu nhập kho')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['ma_nt']),
            models.Index(fields=['unit_id']),
            models.Index(fields=['status']),
            # Auto-generation tracking indexes
            models.Index(fields=['tu_dong_tao']),
            models.Index(fields=['nguon_hoa_don_id']),
            models.Index(fields=['tu_dong_tao', 'nguon_hoa_don_id']),
        ]
        ordering = ['-created']

    def __str__(self):  # noqa: C901
        return f'{self.so_ct} - {self.dien_giai}'


class PhieuNhapKhoModel(PhieuNhapKhoModelAbstract):
    """
    Base PhieuNhapKhoModel Implementation
    """

    class Meta(PhieuNhapKhoModelAbstract.Meta):
        abstract = False
        db_table = 'phieu_nhap_kho'
