"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Tinh Ton Kho Tuc Thoi (Real-time Inventory Calculation) API.
"""

from decimal import Decimal

from rest_framework import serializers

from django_ledger.models import KhoHangModel, VatTuModel


class TinhTonKhoTucThoiRequestSerializer(serializers.Serializer):
    """
    Serializer for Tinh Ton Kho Tuc Thoi request data validation.
    Validates input parameters for real-time inventory calculation.
    """

    nam = serializers.IntegerField(
        min_value=1900,
        max_value=2100,
        help_text="Năm tính toán tồn kho"
    )
    ma_vt = serializers.UUIDField(
        help_text="UUID của vật tư cần tính tồn kho"
    )
    ma_kho = serializers.UUIDField(
        help_text="UUID của kho hàng cần tính tồn kho"
    )

    def validate_ma_vt(self, value):
        """
        Validate that VatTu exists and has inventory tracking enabled.

        Parameters
        ----------
        value : UUID
            The VatTu UUID to validate

        Returns
        -------
        UUID
            The validated UUID

        Raises
        ------
        serializers.ValidationError
            If VatTu not found or doesn't have inventory tracking
        """
        # Get entity_slug and request from context
        entity_slug = self.context.get('entity_slug')
        request = self.context.get('request')
        if not entity_slug:
            raise serializers.ValidationError("Entity slug không được cung cấp")
        if not request or not request.user:
            raise serializers.ValidationError("User không được cung cấp")

        try:
            vat_tu = VatTuModel.objects.for_entity(
                entity_slug=entity_slug,
                user_model=request.user
            ).get(uuid=value)
            
            if not vat_tu.ton_kho_yn:
                raise serializers.ValidationError(
                    f"Vật tư {vat_tu.ma_vt} không được theo dõi tồn kho"
                )
            
            if vat_tu.status != '1':
                raise serializers.ValidationError(
                    f"Vật tư {vat_tu.ma_vt} không hoạt động"
                )
                
        except VatTuModel.DoesNotExist:
            raise serializers.ValidationError("Vật tư không tồn tại")

        return value

    def validate_ma_kho(self, value):
        """
        Validate that KhoHang exists and is active.

        Parameters
        ----------
        value : UUID
            The KhoHang UUID to validate

        Returns
        -------
        UUID
            The validated UUID

        Raises
        ------
        serializers.ValidationError
            If KhoHang not found or not active
        """
        # Get entity_slug and request from context
        entity_slug = self.context.get('entity_slug')
        request = self.context.get('request')
        if not entity_slug:
            raise serializers.ValidationError("Entity slug không được cung cấp")
        if not request or not request.user:
            raise serializers.ValidationError("User không được cung cấp")

        try:
            kho_hang = KhoHangModel.objects.for_entity(
                entity_slug=entity_slug,
                user_model=request.user
            ).get(uuid=value)
            
            if kho_hang.status != '1':
                raise serializers.ValidationError(
                    f"Kho hàng {kho_hang.ma_kho} không hoạt động"
                )
                
        except KhoHangModel.DoesNotExist:
            raise serializers.ValidationError("Kho hàng không tồn tại")

        return value

    def validate_nam(self, value):
        """
        Validate the year parameter.

        Parameters
        ----------
        value : int
            The year to validate

        Returns
        -------
        int
            The validated year

        Raises
        ------
        serializers.ValidationError
            If year is invalid
        """
        from datetime import datetime
        
        current_year = datetime.now().year
        if value > current_year + 1:
            raise serializers.ValidationError(
                f"Năm không thể lớn hơn {current_year + 1}"
            )
        
        return value



